<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>784</width>
    <height>524</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Port Configurator</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout_2">
    <item row="0" column="0">
     <widget class="QTabWidget" name="tabWidget">
      <property name="currentIndex">
       <number>0</number>
      </property>
      <widget class="QWidget" name="PortsTab">
       <property name="contextMenuPolicy">
        <enum>Qt::DefaultContextMenu</enum>
       </property>
       <attribute name="title">
        <string>Ports</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_5">
        <item row="0" column="0">
         <widget class="QTreeWidget" name="ports">
          <attribute name="headerStretchLastSection">
           <bool>false</bool>
          </attribute>
          <column>
           <property name="text">
            <string>Port</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>Switch</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>Switch port</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>Actual</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>VLAN</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>Submit</string>
           </property>
          </column>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="HealthTab">
       <attribute name="title">
        <string>Health</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_4">
        <item row="0" column="0">
         <widget class="QTreeWidget" name="Health">
          <property name="columnCount">
           <number>3</number>
          </property>
          <column>
           <property name="text">
            <string>Status</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>Switch</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>Details</string>
           </property>
          </column>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="console">
       <attribute name="title">
        <string>Console</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_3">
        <item row="1" column="0">
         <widget class="QScrollArea" name="scrollArea">
          <property name="widgetResizable">
           <bool>true</bool>
          </property>
          <widget class="QWidget" name="scrollAreaWidgetContents">
           <property name="geometry">
            <rect>
             <x>0</x>
             <y>0</y>
             <width>704</width>
             <height>363</height>
            </rect>
           </property>
           <layout class="QGridLayout" name="gridLayout">
            <item row="0" column="0">
             <widget class="QSplitter" name="splitter">
              <property name="orientation">
               <enum>Qt::Vertical</enum>
              </property>
              <property name="childrenCollapsible">
               <bool>false</bool>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QLineEdit" name="ConsoleInput"/>
        </item>
       </layout>

      </widget>
     </widget>
    </item>
    <item row="1" column="0">
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <widget class="QPushButton" name="buttonBugReport">
        <property name="text">
         <string>Report a bug</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="buttonReload">
        <property name="text">
         <string>Reload</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="spacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QPushButton" name="buttonSubmitAll">
        <property name="enabled">
         <bool>false</bool>
        </property>
        <property name="text">
         <string>Submit all</string>
        </property>
       </widget>
      </item>
     </layout>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>748</width>
     <height>23</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
