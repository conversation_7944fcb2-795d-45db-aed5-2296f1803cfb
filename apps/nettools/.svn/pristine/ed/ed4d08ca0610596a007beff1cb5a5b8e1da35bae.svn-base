{"packages": [{"name": "lamoni/junosnetconf", "version": "v1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/lamoni/junosnetconf.git", "reference": "fc74d9c0c4eb32578e7c91c5457dd43841f327b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lamoni/junosnetconf/zipball/fc74d9c0c4eb32578e7c91c5457dd43841f327b2", "reference": "fc74d9c0c4eb32578e7c91c5457dd43841f327b2", "shasum": ""}, "require": {"lamoni/netconf": "v1.0.1", "php": ">= 5.4.16"}, "time": "2017-10-20T20:58:48+00:00", "type": "library", "installation-source": "dist", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A Juniper NETCONF XML Management Protocol implementation in PHP", "homepage": "https://github.com/lamoni/junosnetconf", "keywords": ["juniper", "junos", "netconf", "php"], "support": {"issues": "https://github.com/lamoni/junosnetconf/issues", "source": "https://github.com/lamoni/junosnetconf/tree/v1.0.1"}, "install-path": "../lamoni/junosnetconf"}, {"name": "lamoni/netconf", "version": "v1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/lamoni/netconf.git", "reference": "f0637b1ed3a4934e29cead2c64c4977c4f647adf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lamoni/netconf/zipball/f0637b1ed3a4934e29cead2c64c4977c4f647adf", "reference": "f0637b1ed3a4934e29cead2c64c4977c4f647adf", "shasum": ""}, "require": {"php": ">= 5.4.16", "phpseclib/phpseclib": "1.0.*"}, "time": "2017-10-20T21:19:42+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Lamoni\\NetConf\\": "Lamoni/NetConf/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A vendor-agnostic PHP NETCONF implementation", "homepage": "https://github.com/lamoni/netconf", "keywords": ["netconf", "php"], "support": {"issues": "https://github.com/lamoni/netconf/issues", "source": "https://github.com/lamoni/netconf/tree/master"}, "install-path": "../lamoni/netconf"}, {"name": "phpseclib/phpseclib", "version": "1.0.20", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "b3a606b90c47fb526cfb3a1664771cb8314a3434"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/b3a606b90c47fb526cfb3a1664771cb8314a3434", "reference": "b3a606b90c47fb526cfb3a1664771cb8314a3434", "shasum": ""}, "require": {"php": ">=5.0.0"}, "require-dev": {"phing/phing": "~2.7", "phpunit/phpunit": "^4.8.35|^5.7|^6.0|^9.4", "squizlabs/php_codesniffer": "~2.0"}, "suggest": {"ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a wide variety of cryptographic operations.", "pear-pear/PHP_Compat": "Install PHP_Compat to get phpseclib working on PHP < 5.0.0."}, "time": "2021-12-28T06:26:23+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["phpseclib/bootstrap.php", "phpseclib/Crypt/Random.php"], "psr-0": {"Net": "phpseclib/", "File": "phpseclib/", "Math": "phpseclib/", "Crypt": "phpseclib/", "System": "phpseclib/"}}, "notification-url": "https://packagist.org/downloads/", "include-path": ["phpseclib/"], "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/1.0.20"}, "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "install-path": "../phpseclib/phpseclib"}], "dev": true, "dev-package-names": []}