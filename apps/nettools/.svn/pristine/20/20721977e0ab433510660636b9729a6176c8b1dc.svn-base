<?php
/**
 * Created by PhpStorm.
 * User: vermeerenp
 * Date: 2-5-17
 * Time: 12:21
 */

 include_once("DNW_PageBuilder.php");

 $PB = new \DNW\DNW_PageBuilder();

if((!isset($_POST['ip_lookup_NOP']) || $_POST['ip_lookup_NOP'] == "") && (!isset($_POST['ip_lookup_ASD']) || $_POST['ip_lookup_ASD'] == ""))
    exit("No data sent");
if(isset($_POST['ip_lookup_NOP'])){
  $ip = $_POST['ip_lookup_NOP'];
  $networkOverview = file_get_contents("/var/www/infocenter/apps/nettools/networkOverviewNOP.json");
}else{
  $ip = $_POST['ip_lookup_ASD'];
  $networkOverview = file_get_contents("/var/www/infocenter/apps/nettools/networkOverviewASD.json");
}

if(inet_pton($ip) === false) { //Not an IP-adres, try for hostname
    //It might be a port-number, check for this.
    preg_match("/^[0-9]{1}-[0-9]{2}-[0-9]{2}/", $ip, $ip_out);
    if(isset($ip_out[0])) {
        $portid = $ip_out[0];
        $networkOverview = json_decode($networkOverview);

        $arp = $networkOverview->arp;
        $mac = $networkOverview->mac;
        $allPorts = $networkOverview->ports;
        foreach ($mac as $macentry){
            if($macentry->patchid == $portid) {
                foreach ($arp as $arp_entry) {
                    $arp_entry_ip = $arp_entry->ip;
                    $arp_entry_mac = $arp_entry->macaddress;
                    if ($arp_entry_mac == $macentry->macaddress)
                        $macentry->ipaddress = $arp_entry_ip;
                }
                $results[] = $macentry;
            }
        }
    } else {
        $ip = gethostbyname($ip);
        if (inet_pton($ip) === false) {
            exit("Not a valid IP-address, and hostname not found");
        }
    }
    // exit("No valid IP-address");
} else {

    $networkOverview = json_decode($networkOverview);

    $arp = $networkOverview->arp;
    $mac = $networkOverview->mac;
    $allPorts = $networkOverview->ports;

    $ip_mac = "0000.0000.0000";
    foreach ($arp as $arp_entry) {
        $arp_entry_ip = $arp_entry->ip;
        $arp_entry_mac = $arp_entry->macaddress;
        if ($arp_entry_ip == $ip)
            $ip_mac = $arp_entry_mac;
    }

    $results = [];
    foreach ($mac as $mac_entry) {
        if ($mac_entry->macaddress == $ip_mac) {
            $mac_entry->ipaddress = $ip;
            $results[] = $mac_entry;
        }
    }
}


uasort($results, "uncertainty_sort");

$PB->preparePage("", "", array(), false);
$PB->addJQuery();
$PB->addDataTables();
$PB->insertHeadSection("<script>
$(document).ready(function(){
                ResultTable = $('table').DataTable( {
                  order: [1, 'asc']
                } );
});
</script>");

$body .= <<<BODY
<br />
<table class="tablesorter">
<thead><tr>
	<th>macaddress</th>
	<th>uncertainty</th>
	<th>patchid</th>
	<th>hostname</th>
	<th>vlanid</th>
  <th>vlanname</th>
  <th>port</th>
  <th>ip-address</th>
</tr></thead>
BODY;
foreach($results as $result){
	$body .= "<tr><td>".$result->macaddress."</td><td>".$result->uncertainty."</td><td>".$result->patchid."</td><td>".$result->hostname."</td><td>".$result->vlanid."</td><td>".$result->vlanname."</td><td>".$result->port."</td><td>".$result->ipaddress."</td></tr>";
}

$body .= "</table>";

$PB->insertBodySection($body);

echo $PB->getPage();


function uncertainty_sort($a, $b){
  if($a->uncertainty == $b->uncertainty){
    return 0;
  }
  return ($a->uncertainty < $b->uncertainty) ? -1 : 1;
}
