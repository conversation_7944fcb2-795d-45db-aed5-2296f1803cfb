language: python
sudo: false
env:
  global: TEST_RUNNER=unittest PYTHONHASHSEED=random
matrix:
  include:
    - python: "2.7"
      env: TEST_REQUIRE="gevent greenlet geventhttpclient fastimport"
    - python: "pypy"
      env: TEST_REQUIRE="fastimport"
    - python: "3.4"
      env: TEST_REQUIRE="gevent greenlet geventhttpclient fastimport"
    - python: "3.5"
      env: TEST_REQUIRE="gevent greenlet geventhttpclient fastimport"
    - python: "3.5-dev"
      env: TEST_REQUIRE="gevent greenlet geventhttpclient fastimport"
    - python: "3.6"
      env: TEST_REQUIRE="gevent greenlet geventhttpclient fastimport"
    - python: "3.6-dev"
      env: TEST_REQUIRE="gevent greenlet geventhttpclient fastimport"
    - python: "3.7-dev"
      env: TEST_REQUIRE="gevent greenlet geventhttpclient fastimport"
cache:
  directories:
    - $HOME/.cache/pip
script:
  - pip install pip --upgrade
  - pip install $TEST_REQUIRE

  # Test without c extensions
  - python -m $TEST_RUNNER dulwich.tests.test_suite

  # Test with c extensions
  - python setup.py build_ext -i
  - python -m $TEST_RUNNER dulwich.tests.test_suite

