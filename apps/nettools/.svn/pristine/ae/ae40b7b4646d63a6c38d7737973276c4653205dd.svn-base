<?php
	include_once("DNW_PageBuilder.php");

	$PB = new \DNW\DNW_PageBuilder();

	//Get VLAN Names
	if($argv[1][1] == "l")
		$nop = true;
	else
		$nop = false;

	if($nop) {
		$networkOverview = file_get_contents("/var/www/infocenter/apps/nettools/networkOverviewNOP.json");
		$rSpan = file_get_contents("/var/www/infocenter/apps/nettools/RspanNOP.json");
	}
	else {
		$networkOverview = file_get_contents("/var/www/infocenter/apps/nettools/networkOverviewASD.json");
		$rSpan = file_get_contents("/var/www/infocenter/apps/nettools/RspanASD.json");
	}

	$ports = json_decode($networkOverview)->ports;
	$rSpan = json_decode($rSpan);


//	$VlanNames = shell_exec("python /var/www/infocenter/apps/nettools/git/nettools/portconfig.py ".$argv[1]." infocenter CHTs262eMMRb 1");
//	$VlanNames = explode("\n", $VlanNames);
//	$Names = array();
//	foreach($VlanNames as $Vlan){
//		$Vlan = str_replace("'","\"",$Vlan);
//		$Vlan = json_decode($Vlan);
//		$Names[$Vlan->vlanid] = $Vlan->vlanname;
//	}
//	//Get Ports
//	$ports = shell_exec("python /var/www/infocenter/apps/nettools/git/nettools/portconfig.py ".$argv[1]." infocenter CHTs262eMMRb");
//	$ports = explode("\n", $ports);
//	foreach($ports as $i=>$port){
//		$port = str_replace("'","\"", $port);
//		$ports[$i] = json_decode($port);
//	}

//	print_r($ports);
//	exit();


	$PB->preparePage( ($nop)?"NOP VLAN list" : "ASD VLAN list", "", array(), false);
	$PB->addJQuery();
	$PB->addJQueryUI();
	$PB->addDataTables();
	$PB->addBootstrap();
	$PB->insertHeadSection("<script>
$(document).ready(function(){
									ResultTable = $('table').DataTable( {
										order: [0, 'asc'],
										lengthMenu: [100, 1000, 10000]
									} );
});
</script>");
$body = "Generated: ".date("Y-m-d H:i:s");
if($nop)
	$body .= "<br /><br /> NOP Vlan's <a href='./vlanASD.html'>ASD Vlan's</a>&nbsp;&nbsp;<a href='Rspan.php?loc=NOP'>RspanNOP</a>&nbsp;&nbsp;<a href='Rspan.php?loc=ASD'>RspanASD</a>";
else
	$body .= "<br /><br /> ASD Vlan's <a href='./vlanNOP.html'>NOP Vlan's</a>&nbsp;&nbsp;<a href='Rspan.php?loc=NOP'>RspanNOP</a>&nbsp;&nbsp;<a href='Rspan.php?loc=ASD'>RspanASD</a>";
$location = ($nop) ? "NOP" : "ASD";
$body .= <<<BODY
<br />
<div style="background-color:lightgreen">
<form action="ip_lookup.php" method="post">
	<label for="ip_lookup">Lookup IP-Address, Hostname or Portname: </label><input id="ip_lookup_$location" type="text" name="ip_lookup_$location"></input>
	<input type="submit"></input>
</form>
</div>
<br />
<table class="tablesorter">
<thead><tr>
	<th>patchid</th>
	<th>vlanid</th>
	<th>vlanName</th>
	<th>hostname</th>
	<th>interface</th>
</tr></thead>
BODY;


foreach($ports as $port){
	if(isset($port->vlanconfig)) {
		if ($port->vlanconfig == "dynamic") {
			$vlanName = "Dynamic";
		} else {
			$vlanName = $port->vlanname;
		}
	}
	else
		$vlanName = $port->vlanname;

	$body .= "<tr><td>".$port->patchid."</td><td>".$port->vlanid."</td><td>" . $vlanName ."</td><td>".$port->hostname."</td><td>".$port->interface."</td></tr>";
}

$body .= "</table>";

$PB->insertBodySection($body);

echo $PB->getPage();
