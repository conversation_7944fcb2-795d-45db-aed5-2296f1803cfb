<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="windowModality">
   <enum>Qt::NonModal</enum>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>799</width>
    <height>526</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>PortConfig</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QGridLayout" name="gridLayout_2">
    <item row="0" column="0">
     <widget class="QSplitter" name="splitter">
      <property name="orientation">
       <enum>Qt::Vertical</enum>
      </property>
      <widget class="QWidget" name="layoutWidget">
       <layout class="QVBoxLayout" name="verticalLayout">
        <item>
         <widget class="QTableWidget" name="tableWidget">
          <attribute name="horizontalHeaderStretchLastSection">
           <bool>true</bool>
          </attribute>
          <row>
           <property name="text">
            <string/>
           </property>
          </row>
          <column>
           <property name="text">
            <string>Patchport</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>IP</string>
           </property>
           <property name="font">
            <font>
             <strikeout>true</strikeout>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>VLAN ID</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>VLAN Name</string>
           </property>
          </column>
          <column>
           <property name="text">
            <string>Switchname</string>
           </property>
           <property name="font">
            <font>
             <italic>true</italic>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>Switchport</string>
           </property>
           <property name="font">
            <font>
             <italic>true</italic>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string/>
           </property>
           <property name="font">
            <font>
             <italic>true</italic>
            </font>
           </property>
          </column>
          <item row="0" column="0">
           <property name="text">
            <string/>
           </property>
           <property name="flags">
            <set>ItemIsEditable|ItemIsEnabled</set>
           </property>
          </item>
          <item row="0" column="1">
           <property name="text">
            <string/>
           </property>
           <property name="flags">
            <set>NoItemFlags</set>
           </property>
          </item>
          <item row="0" column="2">
           <property name="text">
            <string/>
           </property>
           <property name="flags">
            <set>ItemIsEditable|ItemIsEnabled</set>
           </property>
          </item>
          <item row="0" column="3">
           <property name="text">
            <string/>
           </property>
           <property name="flags">
            <set>ItemIsDragEnabled|ItemIsUserCheckable|ItemIsEnabled</set>
           </property>
          </item>
          <item row="0" column="4">
           <property name="text">
            <string/>
           </property>
           <property name="flags">
            <set>ItemIsSelectable|ItemIsDragEnabled|ItemIsUserCheckable</set>
           </property>
          </item>
          <item row="0" column="5">
           <property name="text">
            <string/>
           </property>
           <property name="flags">
            <set>ItemIsSelectable|ItemIsDragEnabled|ItemIsUserCheckable</set>
           </property>
          </item>
          <item row="0" column="6">
           <property name="text">
            <string/>
           </property>
           <property name="flags">
            <set>ItemIsSelectable|ItemIsDragEnabled|ItemIsUserCheckable</set>
           </property>
          </item>
         </widget>
        </item>
        <item>
         <layout class="QGridLayout" name="gridLayout">
          <item row="0" column="0">
           <widget class="QPushButton" name="buttonReload">
            <property name="enabled">
             <bool>true</bool>
            </property>
            <property name="text">
             <string>&amp;Reload and clear</string>
            </property>
           </widget>
          </item>
          <item row="0" column="2">
           <spacer name="horizontalSpacer">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item row="0" column="1">
           <widget class="QPushButton" name="buttonClearAll">
            <property name="enabled">
             <bool>true</bool>
            </property>
            <property name="text">
             <string>&amp;Clear All</string>
            </property>
           </widget>
          </item>
          <item row="0" column="4">
           <widget class="QPushButton" name="buttonSubmitAll">
            <property name="enabled">
             <bool>true</bool>
            </property>
            <property name="text">
             <string>Submit &amp;All</string>
            </property>
           </widget>
          </item>
          <item row="1" column="0" colspan="6">
           <widget class="Line" name="line">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
           </widget>
          </item>
          <item row="0" column="3">
           <widget class="QPushButton" name="buttonBugreport">
            <property name="text">
             <string>Send output as &amp;bugreport</string>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
      <widget class="QTextEdit" name="textEdit">
       <property name="textInteractionFlags">
        <set>Qt::TextEditorInteraction</set>
       </property>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>799</width>
     <height>19</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
