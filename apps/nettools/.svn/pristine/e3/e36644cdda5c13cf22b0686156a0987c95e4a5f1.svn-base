.. _tutorial-introduction:

Introduction
============

Like Git itself, Dulwich consists of two main layers; the so-called plumbing
and the porcelain.

The plumbing is the lower layer and it deals with the Git object database and the
nitty gritty internals. The porcelain is roughly what you would expect to
be exposed to as a user of the ``git`` command-like tool.

Dulwich has a fairly complete plumbing implementation, and a more recently
added porcelain implementation. The porcelain code lives in
``dulwich.porcelain``.


For the large part, this tutorial introduces you to the internal concepts of
Git and the main plumbing parts of Dulwich. The last chapter covers
the porcelain.
