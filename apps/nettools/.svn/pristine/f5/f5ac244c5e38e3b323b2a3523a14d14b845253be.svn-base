<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit7ba86187d169b592783d93dbda288429
{
    public static $files = array (
        'decc78cc4436b1292c6c0d151b19445c' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/bootstrap.php',
        '3919eeb97e98d4648304477f8ef734ba' => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib/Crypt/Random.php',
    );

    public static $prefixLengthsPsr4 = array (
        'L' => 
        array (
            'Lamoni\\NetConf\\' => 15,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Lamoni\\NetConf\\' => 
        array (
            0 => __DIR__ . '/..' . '/lamoni/netconf/Lamoni/NetConf',
        ),
    );

    public static $prefixesPsr0 = array (
        'S' => 
        array (
            'System' => 
            array (
                0 => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib',
            ),
        ),
        'N' => 
        array (
            'Net' => 
            array (
                0 => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib',
            ),
        ),
        'M' => 
        array (
            'Math' => 
            array (
                0 => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib',
            ),
        ),
        'F' => 
        array (
            'File' => 
            array (
                0 => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib',
            ),
        ),
        'C' => 
        array (
            'Crypt' => 
            array (
                0 => __DIR__ . '/..' . '/phpseclib/phpseclib/phpseclib',
            ),
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit7ba86187d169b592783d93dbda288429::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit7ba86187d169b592783d93dbda288429::$prefixDirsPsr4;
            $loader->prefixesPsr0 = ComposerStaticInit7ba86187d169b592783d93dbda288429::$prefixesPsr0;
            $loader->classMap = ComposerStaticInit7ba86187d169b592783d93dbda288429::$classMap;

        }, null, ClassLoader::class);
    }
}
