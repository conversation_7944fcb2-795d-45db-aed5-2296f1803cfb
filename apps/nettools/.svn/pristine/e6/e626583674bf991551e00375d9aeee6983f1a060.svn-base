strong {
    font-weight: 600;
}
.notification {
    width: 360px;
    padding: 15px;
    background-color: #F0F2F5;
    border-radius: 16px;
    position: fixed;
    top: 15px;
    right: 15px;
    transform: translateY(-200%);
    animation: noti 4s 2 forwards alternate ease-in;
}
.once {
    animation: noti 4s 1 forwards alternate ease-in;
}
.notification-header {
         display: flex;
         align-items: center;
         justify-content: space-between;
         margin-bottom: 15px;
     }
.notification-title {
         font-size: 16px;
         font-weight: 500;
         text-transform: capitalize;
     }
.notification-close {
         cursor: pointer;
         width: 30px;
         height: 30px;
         border-radius: 30px;
         display: flex;
         align-items: center;
         justify-content: center;
         background-color: #F0F2F5;
         font-size: 14px;
     }
.notification-container {
         display: flex;
         align-items: flex-start;
     }
.notification-media {
         position: relative;
     }
.notification-user-avatar {
         width: 60px;
         height: 60px;
         border-radius: 60px;
         object-fit: cover;
     }
.notification-reaction {
         width: 30px;
         height: 30px;
         display: flex;
         align-items: center;
         justify-content: center;
         border-radius: 30px;
         color: white;
         background-image: linear-gradient(45deg, #0070E1, #14ABFE);
         font-size: 14px;
         position: absolute;
         bottom: 0;
         right: 0;
     }
.notification-content {
         width: calc(100% - 60px);
         padding-left: 20px;
         line-height: 1.2;
     }
.notification-text {
         margin: 0px;
         display:-webkit-box;
         -webkit-line-clamp:3;
         -webkit-box-orient: vertical;
         overflow: hidden;
         text-overflow: ellipsis;
     }
.notification-timer {
         color: #1876F2;
         font-weight: 600;
         font-size: 14px;
     }
.notification-status {
         position: absolute;
         right: 15px;
         top: 50%;
         /* transform: translateY(-50%); */
         width: 15px;
         height: 15px;
         background-color: #1876F2;
         border-radius: 50%;
     }

@keyframes noti{
    50%{
        transform: translateY(0);
    }
    100%{
        transform: translateY(0);
    }
}

div.dataTables_wrapper {
    max-width: 701px;
    margin: auto;
}

.loader {
    width: 20px;
    height: 20px;
    margin: 0 auto;
    border-top: 5px solid #292929;
    border-right: 5px solid #efefef;
    border-bottom: 5px solid #efefef;
    border-left: 5px solid #efefef;
    border-radius: 100px;
    animation: spin 1s infinite linear;
}

@keyframes spin {
    100% {
        transform: rotate(360deg);
    }
}

.buttons {
    display: inline;
}

li li li{
    border-width: 1px;
    border-style: solid;
    padding-left: 5px;
    padding-bottom: 2px;
    padding-top: 2px;
}

/* Remove default bullets */
ul, #myUL {
    list-style-type: none;
}

/* Remove margins and padding from the parent ul */
#myUL {
    margin: auto;
    padding: 0;
    width: 475px;
    overflow-y: scroll;
    height:95%;
}

/* Style the caret/arrow */
.caret {
    cursor: pointer;
    user-select: none; /* Prevent text selection */
}

/* Create the caret/arrow with a unicode, and style it */
.caret::before {
    content: "\1433";/*U+1433*/
    color: black;
    display: inline-block;
    margin-right: 6px;
}

/* Rotate the caret/arrow icon when clicked on (using JavaScript) */
.caret-down::before {
    transform: rotate(90deg);
}

/* Hide the nested list */
.nested {
    display: none;
}

/* Show the nested list when the user clicks on the caret/arrow (with JavaScript) */
.active {
    display: block;
}