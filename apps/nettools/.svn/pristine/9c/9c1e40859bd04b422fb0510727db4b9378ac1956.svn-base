<?php

// autoload_namespaces.php @generated by Composer

$vendorDir = dirname(dirname(__FILE__));
$baseDir = dirname($vendorDir);

return array(
    'System' => array($vendorDir . '/phpseclib/phpseclib/phpseclib'),
    'Net' => array($vendorDir . '/phpseclib/phpseclib/phpseclib'),
    'Math' => array($vendorDir . '/phpseclib/phpseclib/phpseclib'),
    'File' => array($vendorDir . '/phpseclib/phpseclib/phpseclib'),
    'Crypt' => array($vendorDir . '/phpseclib/phpseclib/phpseclib'),
);
