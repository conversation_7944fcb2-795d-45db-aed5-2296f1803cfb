<?php
        include_once("DNW_PageBuilder.php");

        $PB = new \DNW\DNW_PageBuilder();

        //Get VLAN Names
        if($_GET['loc'] == "NOP")
                $nop = true;
        else
                $nop = false;

        if($nop)
                $networkOverview = file_get_contents("/var/www/infocenter/apps/nettools/networkOverviewNOP.json");
        else
                $networkOverview = file_get_contents("/var/www/infocenter/apps/nettools/networkOverviewASD.json");

	//print_r(json_decode($networkOverview));

$JIT = "";

$networkOverview = json_decode($networkOverview)->neighbors;

$usedHostnames = [];
$hostArray = array();
foreach($networkOverview as $id => $network) {
    if(!isset($hostArray[$network->hostname]["data"]["\$dim"]))
        $hostArray[$network->hostname]["data"]["\$dim"] = 3;
    if(!in_array($network->deviceid, $hostArray[$network->hostname]["adjacencies"]))
        $hostArray[$network->hostname]["data"]["\$dim"]++;

    if($hostArray[$network->hostname]["data"]["\$dim"] > 5) {
        $hostArray[$network->hostname]["data"]["\$color"] = "#00FF00";
    }

    if($hostArray[$network->hostname]["data"]["\$dim"] > 10) {
        $hostArray[$network->hostname]["data"]["\$type"] = "triangle";
        $hostArray[$network->hostname]["data"]["\$color"] = "#FF0000";
    }
    $hostArray[$network->hostname]["adjacencies"][] = $network->deviceid;
    $hostArray[$network->hostname]["id"] = $network->hostname;
    $hostArray[$network->hostname]["name"] = $network->hostname;

}
foreach($hostArray as $host) {
        $JIT[] = $host;
}

//        if(in_array($network->hostname, $usedHostnames))
//                $inuse = true;
//        else
//                $inuse = false;
//
//        if(in_array($network->deviceid, $usedHostnames))
//                $targetinuse = true;
//        else
//                $targetinuse = false;
//
//        if(!$inuse)
//                $wireit .= "$('#placeholder').append('<div id=\"$network->hostname\">$network->hostname</div>');
//        if(!$inuse)
//                $wireit .= "$('#placeholder').append('<div id=\"$network->hostname\">$network->hostname</div>');
//";
//
//        $wireit2 .= "var " . str_replace(".", "", str_replace("-", "_", $network->hostname)) . " = new WireIt.Terminal(document.getElementById(\"$network->hostname\"), {name: \"$network->hostname\"});
//        ";
//        $wireit3 .= "new WireIt.Wire(" . str_replace(".", "", str_replace("-", "_", $network->hostname)) . ", " . str_replace(".", "", str_replace("-", "_", $network->deviceid)) . ", document.getElementById(\"placeholder\"), []);
//        ";
//        $usedHostnames [] = $network->hostname;



$PB->preparePage("", "", array(), false);
$PB->addJQuery();
//$PB->addDataTables();

$JIT = json_encode($JIT);

$head = <<<HEAD

<link type="text/css" href="/js/Jit/Examples/css/base.css" rel="stylesheet" />
<link type="text/css" href="/js/Jit/Examples/css/ForceDirected.css" rel="stylesheet" />

<!--[if IE]><script language="javascript" type="text/javascript" src="/js/Jit/Extras/excanvas.js"></script><![endif]-->

<script type='text/javascript' src='/js/Jit/jit.js'></script>

<script>

var labelType, useGradients, nativeTextSupport, animate;

(function() {
  var ua = navigator.userAgent,
      iStuff = ua.match(/iPhone/i) || ua.match(/iPad/i),
      typeOfCanvas = typeof HTMLCanvasElement,
      nativeCanvasSupport = (typeOfCanvas == 'object' || typeOfCanvas == 'function'),
      textSupport = nativeCanvasSupport
        && (typeof document.createElement('canvas').getContext('2d').fillText == 'function');
  //I'm setting this based on the fact that ExCanvas provides text support for IE
  //and that as of today iPhone/iPad current text support is lame
  labelType = (!nativeCanvasSupport || (textSupport && !iStuff))? 'Native' : 'HTML';
  nativeTextSupport = labelType == 'Native';
  useGradients = nativeCanvasSupport;
  animate = !(iStuff || !nativeCanvasSupport);
})();

var Log = {
  elem: false,
  write: function(text){
    if (!this.elem)
      this.elem = document.getElementById('log');
    this.elem.innerHTML = text;
    this.elem.style.left = (500 - this.elem.offsetWidth / 2) + 'px';
  }
};

$(document).ready(function(){
var json = $JIT
var fd = new \$jit.ForceDirected({
  //id of the visualization container
  injectInto: 'infovis',
  //Enable zooming and panning
  //by scrolling and DnD
  Navigation: {
    enable: true,
    //Enable panning events only if we're dragging the empty
    //canvas (and not a node).
    panning: 'avoid nodes',
    zooming: 10 //zoom speed. higher is more sensible
  },
  // Change node and edge styles such as
  // color and width.
  // These properties are also set per node
  // with dollar prefixed data-properties in the
  // JSON structure.
  Node: {
    overridable: true
  },
  Edge: {
    overridable: true,
    color: '#23A4FF',
    lineWidth: 0.4
  },
  //Native canvas text styling
  Label: {
    type: labelType, //Native or HTML
    size: 10,
    style: 'bold'
  },
  //Add Tips
  Tips: {
    enable: true,
    onShow: function(tip, node) {
      //count connections
      var count = 0;
      node.eachAdjacency(function() { count++; });
      //display node info in tooltip
      tip.innerHTML = "<div class=\"tip-title\">" + node.name + "</div>"
        + "<div class=\"tip-text\"><b>connections:</b> " + count + "</div>";
    }
  },
  // Add node events
  Events: {
    enable: true,
    type: 'Native',
    //Change cursor style when hovering a node
    onMouseEnter: function() {
      fd.canvas.getElement().style.cursor = 'move';
    },
    onMouseLeave: function() {
      fd.canvas.getElement().style.cursor = '';
    },
    //Update node positions when dragged
    onDragMove: function(node, eventInfo, e) {
        var pos = eventInfo.getPos();
        node.pos.setc(pos.x, pos.y);
        fd.plot();
    },
    //Implement the same handler for touchscreens
    onTouchMove: function(node, eventInfo, e) {
      \$jit.util.event.stop(e); //stop default touchmove event
      this.onDragMove(node, eventInfo, e);
    },
    //Add also a click handler to nodes
    onClick: function(node) {
      if(!node) return;
      // Build the right column relations list.
      // This is done by traversing the clicked node connections.
      var html = "<h4>" + node.name + "</h4><b> connections:</b><ul><li>",
          list = [];
      node.eachAdjacency(function(adj){
        list.push(adj.nodeTo.name);
      });
      //append connections information
      \$jit.id('inner-details').innerHTML = html + list.join("</li><li>") + "</li></ul>";
    }
  },
  //Number of iterations for the FD algorithm
  iterations: 200,
  //Edge length
  levelDistance: 130,
  // Add text to the labels. This method is only triggered
  // on label creation and only for DOM labels (not native canvas ones).
  onCreateLabel: function(domElement, node){
    domElement.innerHTML = node.name;
    var style = domElement.style;
    style.fontSize = "0.8em";
    style.color = "#ddd";
  },
  // Change node styles when DOM labels are placed
  // or moved.
  onPlaceLabel: function(domElement, node){
    var style = domElement.style;
    var left = parseInt(style.left);
    var top = parseInt(style.top);
    var w = domElement.offsetWidth;
    style.left = (left - w / 2) + 'px';
    style.top = (top + 10) + 'px';
    style.display = '';
  }
});
// load JSON data.
fd.loadJSON(json);
// compute positions incrementally and animate.
fd.computeIncremental({
  iter: 40,
  property: 'end',
  onStep: function(perc){
    Log.write(perc + '% loaded...');
  },
  onComplete: function(){
    Log.write('done');
    fd.animate({
      modes: ['linear'],
      transition: \$jit.Trans.Elastic.easeOut,
      duration: 2500
    });
  }
});
});
</script>
HEAD;

$PB->insertHeadSection($head);
$body = <<<BODY
<div id="container">

<div id="left-container">



        <div class="text">
        <h4>

        </h4>

            You can <b>zoom</b> and <b>pan</b> the visualization by <b>scrolling</b> and <b>dragging</b>.<br /><br />
            You can <b>change node positions</b> by <b>dragging the nodes around</b>.<br /><br />
            The clicked node's connections are displayed in a relations list in the right column.<br /><br />

        </div>

        <div id="id-list"></div>

</div>

<div id="center-container">
    <div id="infovis"></div>
</div>

<div id="right-container">

<div id="inner-details"></div>

</div>

<div id="log"></div>
</div>
BODY;

//if($nop)
//        $body .= "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; NOP Vlan's <a href='./vlanASD.html'>ASD Vlan's</a>";
//else
//        $body .= "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; ASD Vlan's <a href='./vlanNOP.html'>NOP Vlan's</a>";
//$location = ($nop) ? "NOP" : "ASD";
//$body .= <<<BODY
//<br />
//<form action="ip_lookup.php" method="post">
//	<label for="ip_lookup">Lookup IP-Address or Hostname: </label><input id="ip_lookup_$location" type="text" name="ip_lookup_$location"></input>
//	<input type="submit"></input>
//</form>
//<br />
//<table class="tablesorter">
//<thead><tr>
//	<th>patchid</th>
//	<th>vlanid</th>
//	<th>vlanName</th>
//	<th>hostname</th>
//	<th>interface</th>
//</tr></thead>
//BODY;

$PB->insertBodySection($body);

echo $PB->getPage();
