[tox]
downloadcache = {toxworkdir}/cache/
envlist = py27, pypy, py27-noext, pypy-noext, py34, py34-noext, py35, py35-noext, py36, py36-noext

[testenv]

commands = make check
recreate = True
whitelist_externals = make

[testenv:py27-noext]
commands = make check-noextensions

[testenv:pypy-noext]
commands = make check-noextensions

[testenv:py34-noext]
commands = make check-noextensions

[testenv:py35-noext]
commands = make check-noextensions

[testenv:py36-noext]
commands = make check-noextensions
