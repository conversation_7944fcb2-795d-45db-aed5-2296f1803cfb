<?php
/**
 * Created by PhpStorm.
 * User: vermeerenp
 * Date: 21-7-2017
 * Time: 14:30
 */

namespace DNW;

use CurlHandle;
use stdClass;

include_once("DNW_DB.php");
include_once("DNW_PageBuilder.php");


class DNW_AccessControl
{
    public string $baseUrl = "http://192.168.140.60/vapix/";
    public string $username = "CDS";
    public string $password = "CDS";
    protected DNW_DB $DB;
    protected array $curlOptions;
    protected false|CurlHandle $ch;
    private DNW_PageBuilder $PB;

    function __construct()
    {
        $this->DB = new DNW_DB();
        $this->PB = new DNW_PageBuilder();

        $this->curlOptions = array(
            CURLOPT_HEADER => false,
            CURLOPT_VERBOSE => false,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,    // for https
            CURLOPT_USERPWD => $this->username . ":" . $this->password,
            CURLOPT_HTTPAUTH => CURLAUTH_DIGEST,
            CURLOPT_CUSTOMREQUEST => "POST"
        );

        $this->ch = curl_init();

    }

    function pullLogs(): int
    {
        $Users = $this->pullUsers();
        $Doors = $this->pullAccessPoints();
        $Start = date('Y-m-d', strtotime('-1 day')) . "T00:00:00";
        $Stop = date('Y-m-d') . "T00:00:00";
        $command = array(
            "axlog:FetchEvents2" => array(
                "FilterSets" => array(
                    array(
                        "Start" => $Start,
                        "Stop" => $Stop,
                        "Filters" => array(
                            array(
                                "Key" => "topic0",
                                "Value" => "AccessControl"
                            ),
                            array(
                                "Key" => "topic1",
                                "Value" => "AccessGranted"
                            )
                        ))),
                "Descending" => true,
                //"IncludeHumanReadableTime" => true,
                //"ConvertFilterTimeFromLocal" => true
            )
        );
        $this->setPostFields($command);
        $GrantedLogs = $this->executeCurl("eventlogger");
        $Events = array();
        foreach ($GrantedLogs->Event as $Event) {
            $Door = $UserName = "";
            foreach ($Event->KeyValues as $KeyValue) {
                switch ($KeyValue->Key) {
                    case "AccessPointToken":
                        $Door = $Doors[$KeyValue->Value];
                        break;
                    case "topic2":
                        if ($KeyValue->Value == "Anonymous")
                            $UserName = "Anonymous";
                        break;
                    case "CredentialHolderName":
                        $UserName = $Users[$KeyValue->Value];
                        break;
                }
            }
            $Events[] = array(
                "token" => $Event->token,
                "Time" => explode(".", $Event->UtcTime)[0],
                "Granted" => 1,
                "Door" => $Door,
                "UserName" => $UserName
            );
        }
        $command = array(
            "axlog:FetchEvents2" => array(
                "FilterSets" => array(
                    array(
                        "Start" => $Start,
                        "Stop" => $Stop,
                        "Filters" => array(
                            array(
                                "Key" => "topic0",
                                "Value" => "AccessControl"
                            ),
                            array(
                                "Key" => "topic1",
                                "Value" => "Denied"
                            )
                        ))),
                "Descending" => true,
//                "IncludeHumanReadableTime" => true,
//                "ConvertFilterTimeFromLocal" => true
            )
        );
        $this->setPostFields($command);
        $DeniedLogs = $this->executeCurl("eventlogger");
        foreach ($DeniedLogs->Event as $Event) {
            $Door = $UserName = "";
            foreach ($Event->KeyValues as $KeyValue) {
                switch ($KeyValue->Key) {
                    case "AccessPointToken":
                        $Door = $Doors[$KeyValue->Value];
                        break;
                    case "topic2":
                        if ($KeyValue->Value == "Anonymous")
                            $UserName = "Anonymous";
                        break;
                    case "CredentialHolderName":
                        $UserName = $Users[$KeyValue->Value];
                        break;
                }
            }
            $Events[] = array(
                "token" => $Event->token,
                "Time" => explode(".", $Event->UtcTime)[0],
                "Granted" => 0,
                "Door" => $Door,
                "UserName" => $UserName
            );
        }
        foreach ($Events as $event) {
            $this->DB->InsertHelper("AccessControl", "LogEntry", $event, array("token", "UserName", "Door", "Time", "Granted"), true);
        }
        return count($Events);
    }

    function pullUsers(): array
    {
        $this->setPostFields("axudb:GetUserList");
        $Users = $this->executeCurl("pacs");
        $UserReturn = array();
        foreach ($Users->User as $User) {
            $ln = $fn = "";
            foreach ($User->Attribute as $Attr) {
                if ($Attr->Name == "FirstName")
                    $fn = $Attr->Value;
                if ($Attr->Name == "LastName")
                    $ln = $Attr->Value;
            }
            $UserReturn[$User->token] = $fn . " " . $ln;
        }
        return $UserReturn;
    }

    function setPostFields($command):VOID
    {
        if (is_string($command))
            $post_data = json_encode(array(
                $command => new stdClass()
            ));
        else if (is_array($command))
            $post_data = json_encode($command);
        else
            exit();
        $this->curlOptions[CURLOPT_POSTFIELDS] = $post_data;
        $this->curlOptions[CURLOPT_HTTPHEADER] = array(
            'Content-Type: application/json',
            'Content-Length: ' . strlen($post_data)
        );
    }

    function executeCurl($url)
    {
        $this->curlOptions[CURLOPT_URL] = $this->baseUrl . $url;
        curl_setopt_array($this->ch, $this->curlOptions);

        $raw_response = curl_exec($this->ch);

        // validate CURL status
        if (curl_errno($this->ch))
            return (curl_error($this->ch));

        $status_code = curl_getinfo($this->ch, CURLINFO_HTTP_CODE);
        if ($status_code != 200)
            return ("Response with Status Code [" . $status_code . "]." . $raw_response);

        //if ($this->ch != null) curl_close($this->ch);

        return json_decode($raw_response);
    }

    function pullAccessPoints(): array
    {
        $this->setPostFields("axtdc:GetDoorList");
        $Doors = $this->executeCurl("doorcontrol");
        $DoorReturn = $DoorTokens = array();
        foreach ($Doors->Door as $Door) {
            $DoorTokens[$Door->token] = $Door->Name; // This is the door token, we need the UUID though.
        }
        $this->setPostFields("pacsaxis:GetAccessPointList");
        $DoorConfig = $this->executeCurl("pacs");

        foreach ($DoorConfig->AccessPoint as $DoorConfig) {
            $DoorReturn[$DoorConfig->token] = $DoorTokens[$DoorConfig->Entity];
        }
        return $DoorReturn;
    }

    function buildPage()
    {
        $this->PB->preparePage("AccessControl items list", $this->getLogs(), array(), false);
        $this->PB->addJQuery();
        $this->PB->addDataTables();
        $this->PB->insertHeadSection(" ");
        $this->PB->insertBodySection();
        return $this->PB->getPage();
    }

    function getLogs(): string
    {
        $output = $this->DB->Select("SELECT * from AccessControl.LogEntry", [], false, true);
        return $this->PB->createTableFromArray($output, array("token", "UserName", "Door", "Time", "Granted"), array(
            "Search" => true,
            "dom" => 'lfrtipB',
            "Columns" => '{ "width": "30px", "targets": 0 }',
            "Selectable" => false,
            "Buttons" => array("csv"),
            "Paging" => true,
            "Sort_on" => "3,'desc'"
        ));

    }

}