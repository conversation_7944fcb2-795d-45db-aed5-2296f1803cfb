# Environment Configuration
.env
.env.local
.env.*.local

# Development Tools Cache Files
.phplint.cache/
.php-cs-fixer.cache
.phpstan.cache/
.rector.cache/

# PHP Development Tools Output
dev-tools/output/
dev-tools/logs/

# Local Development Login Script (security sensitive)
dev-tools/localLogin.php

# Composer (unsure how currently used)
vendor/
##composer.phar
##composer-setup.php

# SVN Externals (converted to git submodules)
docs/
wiki/
API/maintenance/
ICx/apps/IDB/
ICx/apps/maintenance/
apps/AccessControl/
apps/nettools/
tests/

# Docker
.docker/
docker-compose.override.yml

# IDE and Editor Files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Logs
*.log
/log/*.log
/logs/
error_log
access_log

# Temporary Files
/tmp/
/temp/
*.tmp
*.temp

# Cache Directories
/cache/
/storage/cache/
/var/cache/

# Session Files
/sessions/
*.sess
cookies.txt
ICx/KAlog.txt

# Backup Files
*.bak
*.backup
*.old
*~

# OS Generated Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Zone.Identifier

# Database Dumps (keep structure, ignore data dumps)
*.sql
!schema.sql
!structure.sql

# Configuration Files with Sensitive Data
.claude
CLAUDE.md
*_conf
*.conf
!docker/apache/*.conf
!docker/mysql/*.conf

# PHP Error Logs
php_errors.log
error.log

# Node.js (if any frontend tools are added later)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build Artifacts
/build/
/dist/
