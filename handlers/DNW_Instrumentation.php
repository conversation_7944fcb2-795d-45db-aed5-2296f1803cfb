<?php
/**
 * Author: <PERSON>
 */

namespace DNW;

use JetBrains\PhpStorm\NoReturn;
use phpmailerException;
use Wikimedia\ParamValidator\TypeDef\StringDefTest;

include_once("DNW_DB.php");
include_once("defines.php");
include_once("DNW_Mailer.php");
include_once("DNW_User.php");
include_once("DNW_Logger.php");
include_once("DNW_Companies.php");

if (!function_exists('mysql_real_escape_string')) {
    /**
     * mysql_escape_string — Escapes a string for use in a mysql_query
     *
     * @link https://dev.mysql.com/doc/refman/8.0/en/string-literals.html#character-escape-sequences
     *
     * @param string $unescaped_string
     * @return string
     * @deprecated
     */
    function mysql_real_escape_string(string $unescaped_string): string
    {
//        $replacementMap = [
//            "\0" => "\\0",
//            "\n" => "\\n",
//            "\r" => "\\r",
//            "\t" => "\\t",
//            chr(26) => "\\Z",
//            chr(8) => "\\b",
//            '"' => '\"',
//            "'" => "\'",
//            '_' => "\_",
//            "%" => "\%",
//            '\\' => '\\\\'
//        ];
//
//        return \strtr($unescaped_string, $replacementMap);
        return $unescaped_string;
    }
}

/**
 * Class DNW_Instrumentation
 * @package DNW
 */
class DNW_Instrumentation
{

    //General DB link for the entire class
    public DNW_DB $DB;
    public DNW_Companies $Companies;
    public DNW_User $User;
    public DNW_Logger $LOG;

    //Field list representing the order in which fields are sent out, and retrieved.
    public array $field = array('equipmentNumber', 'type', 'serialNumber', 'phys_units', 'calibrationDate', 'calibrationFrequency', 'calibrationRequ', 'remarks', 'storageLocation', 'equipmentManager', 'lup', 'lupBy', 'wtParameter', 'facility', 'phys_status', 'cal_status', 'ip', 'supplier', 'chan_properties');

    //Define for the NewLine that is used in the Export functionality for Comput
    public string $Apropos_NewLine = "\r\n";

    //Defines for amount of coefficients per line for each type, sorted by first 2 numbers
    public int $Apropos_Coeff_Rowlength_1 = 4; //Diff. Press. transducer
    public int $Apropos_Coeff_Rowlength_2 = 4; //Abs. Press. transducer
    public int $Apropos_Coeff_Rowlength_3 = 4; //Temp. sensor
    public int $Apropos_Coeff_Rowlength_5 = 4; //Thermocouple
    public int $Apropos_Coeff_Rowlength_6 = 2; //Humidity sensor
    public int $Apropos_Coeff_Rowlength_11 = 4; //Inclinometer
    public int $Apropos_Coeff_Rowlength_16 = 2; //Position and Angle meters
    public int $Apropos_Coeff_Rowlength_21 = 6; //Ext 6 comp balance
    public int $Apropos_Coeff_Rowlength_22 = 5; //Ext 5 comp balance
    public int $Apropos_Coeff_Rowlength_23 = 4; //Ext 4 comp balance
    public int $Apropos_Coeff_Rowlength_24 = 3; //Ext 3 comp balance
    public int $Apropos_Coeff_Rowlength_25 = 2; //Ext 2 comp balance
    public int $Apropos_Coeff_Rowlength_26 = 2; //Ext 1 comp balance
    public int $Apropos_Coeff_Rowlength_40 = 4; //Microphone

    //Define the amount of coefficients that should be filled in.
    public int $Apropos_Coeff_Amount_1 = 4; //Diff. Press. transducer
    public int $Apropos_Coeff_Amount_2 = 4; //Abs. Press. transducer
    public int $Apropos_Coeff_Amount_3 = 4; //Temp. sensor
    public int $Apropos_Coeff_Amount_5 = 24; //Thermocouple
    public int $Apropos_Coeff_Amount_6 = 2; //Humidity sensor
    public int $Apropos_Coeff_Amount_11 = 4; //Inclinometer
    public int $Apropos_Coeff_Amount_16 = 2; //Position and Angle meters
    public int $Apropos_Coeff_Amount_21 = 324; //Ext 6 comp balance
    public int $Apropos_Coeff_Amount_22 = 200; //Ext 5 comp balance
    public int $Apropos_Coeff_Amount_23 = 112; //Ext 4 comp balance
    public int $Apropos_Coeff_Amount_24 = 54; //Ext 3 comp balance
    public int $Apropos_Coeff_Amount_25 = 20; //Ext 2 comp balance
    public int $Apropos_Coeff_Amount_26 = 4; //Ext 1 comp balance
    public int $Apropos_Coeff_Amount_40 = 51; //Microphone


    //Defines for amount of extra numbers in the Apropos content
    public int $Apropos_Extra_Amount_1 = 3; //Diff. Press. transducer
    public int $Apropos_Extra_Amount_2 = 3; //Abs. Press. transducer
    public int $Apropos_Extra_Amount_3 = 3; //Temp. sensor
    public int $Apropos_Extra_Amount_5 = 9; //Thermocouple
    public int $Apropos_Extra_Amount_6 = 0; //Humidity sensor
    public int $Apropos_Extra_Amount_11 = 3; //Inclinometer
    public int $Apropos_Extra_Amount_16 = 0; //Position and Angle meters
    public int $Apropos_Extra_Amount_21 = 0; //Ext 6 comp balance
    public int $Apropos_Extra_Amount_22 = 0; //Ext 5 comp balance
    public int $Apropos_Extra_Amount_23 = 0; //Ext 4 comp balance
    public int $Apropos_Extra_Amount_24 = 0; //Ext 3 comp balance
    public int $Apropos_Extra_Amount_25 = 0; //Ext 2 comp balance
    public int $Apropos_Extra_Amount_26 = 0; //Ext 1 comp balance
    public int $Apropos_Extra_Amount_40 = 9; //Microphone


    public array $Apropos_Extra_Vars_1 = array("rangemin", "rangemax", "excitation"); //Diff. Press. transducer
    public array $Apropos_Extra_Vars_2 = array("rangemin", "rangemax", "excitation"); //Abs. Press. transducer
    public array $Apropos_Extra_Vars_3 = array("rangemin", "rangemax", "excitation"); //Temp. sensor
    public array $Apropos_Extra_Vars_5 = array("", "", "", "", "", "", "", "", ""); //Thermocouple
    public array $Apropos_Extra_Vars_6 = array(); //Humidity sensor
    public array $Apropos_Extra_Vars_11 = array("rangemin", "rangemax", "excitation"); //Inclinometer
    public array $Apropos_Extra_Vars_16 = array(); //Position and Angle meters
    public array $Apropos_Extra_Vars_21 = array(); //Ext 6 comp balance
    public array $Apropos_Extra_Vars_22 = array(); //Ext 5 comp balance
    public array $Apropos_Extra_Vars_23 = array(); //Ext 4 comp balance
    public array $Apropos_Extra_Vars_24 = array(); //Ext 3 comp balance
    public array $Apropos_Extra_Vars_25 = array(); //Ext 2 comp balance
    public array $Apropos_Extra_Vars_26 = array(); //Ext 1 comp balance
    public array $Apropos_Extra_Vars_40 = array("", "", "", "", "", "", "", "", ""); //Microphone


    public array $facs = array('DNW' => '16,20,17,19,18,23', 'GUK' => '18,23', 'NOP' => '16,20,17,19', 'Nop/Asd' => '16,20,17', 'LLF' => 16, 'LST' => 20, 'HST' => 17, 'NWB' => 19, 'GOE' => 18, 'KKK' => 23, 'Ext.' => 30);

    public string $q_GetLatestCalibrationsRemind = "SELECT
				ins.equipmentManager as equipmentManager,
				ins.id as id,
				ins.equipmentNumber as insName,
				appl.application_name as name,
				appl.application_id as applId,
				calib.cal_appr as approvalDate,
				unix_timestamp(calib.cal_date) as calibrationDate,
				ins.calibrationFrequency as calibrationFrequency
			FROM    apps.instrument_application appl
			INNER JOIN
					(
						SELECT  cal_fk_application_id,
								MAX(cal_date) MaxDate
						FROM    apps.instrument_calibration
						GROUP BY cal_fk_application_id
					) MaxDates
					ON appl.application_id = MaxDates.cal_fk_application_id
			INNER JOIN
					apps.instrument_calibration calib
					ON MaxDates.cal_fk_application_id = calib.cal_fk_application_id
						AND MaxDates.MaxDate = calib.cal_date
			JOIN apps.instrument ins ON ins.id = appl.fk_instrument_id
			WHERE
				ins.calibrationFrequency > ?
				AND (ins.cal_status = " . INS_CALIBRATED . " OR ins.cal_status = " . INS_NO_VALID_CAL . ")
				AND ins.phys_status <> " . PHYS_UNDER_CALIB . "
				AND calib.cal_appr_by IS NOT NULL
				AND appl.last_reminder_sent < date_add(NOW(), INTERVAL -? SECOND)";

    public string $q_GetLatestCalibrations = "SELECT
				ins.equipmentManager as equipmentManager,
				ins.id as id,
				ins.equipmentNumber as insName,
				MaxDates.cal_appr as approvalDate,
				unix_timestamp(MaxDates.MaxDate) as calibrationDate,
				ins.calibrationFrequency as calibrationFrequency,
				unix_timestamp(ins.calibrationDate) as calibratedDate
			FROM    apps.instrument ins
			INNER JOIN
					(
						SELECT  cal_fk_id,
								MAX(cal_date) MaxDate,
								cal_id as MaxID,
								cal_appr
						FROM    apps.instrument_calibration
						where cal_appr_by IS NOT NULL
						GROUP BY cal_fk_application_id
					) MaxDates
					ON ins.id = MaxDates.cal_fk_id
			WHERE
				ins.calibrationFrequency > ?
				AND ins.cal_status = ?";

    /**
     * Constructor class to set up needed vars
     */
    function __construct()
    {
        $this->DB = new DNW_DB();
        $this->User = new DNW_User();
        $this->LOG = new DNW_Logger();
        $this->Companies = new DNW_Companies();
    }

    /**
     * @param $row
     * @param string $cell
     * @param string $val
     * @return string
     */
    function updateInstrument($row, string $cell = "", string $val = ""): string
    {
        if (!$this->permission($row)) return ('No permission to update this instrument. (' . __LINE__ . ')');

        if($val == "") $val = $_POST['V'];
        if($cell == "") $cell = $_POST['C'];
        // look whether instrument needs calibration
        list($qry) = $this->DB->Select("SELECT calibrationFrequency FROM apps.instrument WHERE id =?", [$row], false)[0];
        $qry = trim($qry);
        if ($qry && strstr('0123456789', $qry[0])) {
            // avoid "ok" status if no approved calibration available
            $qry = "SELECT count(*) FROM apps.instrument_calibration WHERE cal_fk_id =? AND cal_appr IS NOT NULL AND adddate(cal_date, INTERVAL " . $qry . " MONTH) > CURDATE()";
            list($res) = $this->DB->Select($qry, [$row], false)[0];
            //Todo: Fix this for phys and cal status
            if ($res !== false && !$res && $this->field[$cell] == 'status' && in_array($val, array(0, 2))) { // $val is index of statusMenu array: 'ok' and 'in use' are not allowed
                $this->upd($row, $cell, $val + 1);
                return ('Changed to *no valid cal.* because of missing approved calibration.');
            }
            //When the status is set to OK or IN USE, check if this is allowed with the current calibrations
            if ($res !== false && !$res && $this->field[$cell] == 'cal_status' && $val == INS_CALIBRATED) { // $val is index of statusMenu array: 'ok' and 'in use' are not allowed
                $this->upd($row, $cell, $val + 1);
                return ('Changed to *no valid cal.* because of missing approved calibration.');
            }
        }
        //  $val = utf8_decode($val);
        $val = preg_replace('/<[ \/]*?script[^>]*?>/i', '', $val);
        $val = str_replace(array("\n", "\t", "\r"), array('<br />', ''), $val);
        if ($this->upd($row, $cell, $val) === true) return ('done');
        return ('not updated');
    }

    /**
     * @param int $id
     * @param bool $fac
     * @param bool $typ
     * @return bool
     */
    function permission(int $id = 0, bool $fac = false, bool $typ = false): bool
    {
        global $rights, $uid, $conf;
        //  file_put_contents('debug_permission.txt',"\n".'User='.$uid." Right=".$rights,FILE_APPEND);
        if ($rights >= 5) return true; // users with edit right for the application "Instrumentation Database"
        $permObj = file_get_contents('/var/www/infocenter/ICx/apps/IDB/perm.txt');
        $perm_array = jsonDecode($permObj);
        if ($id > 0) {
            list($typ, $fac) = $this->DB->Select("SELECT typeID,facility FROM apps.instrument WHERE id = ?", [$id], false)[0];
        } elseif ((int)$fac > 0 && (int)$typ > 9) {
        } else {
            echo '<pre>', print_r($perm_array, 1);
            return false;
        }
        //  echo "$id, $fac, $typ,",print_r($perm_array,1);
        // get index of equipment type
        foreach ($conf['tab'] as $k => $v) {
            //    echo '**',$k,print_r($v,1),'  ';
            if ($v['filter'] == $typ) {
                $typ = explode('_', $k);
                $typ = $typ[1];
                break;
            }
        }
        // map facilty ids to array key
        $map = array('17' => 0, '19' => 1, '18' => 2, '23' => 3, '16' => 4, '20' => 4, '30' => 5);
        $fac = $map[$fac];
        $allowed = explode(',', $perm_array[$fac]['adm'] . ',' . $perm_array[$fac][$typ]['adm']);
        $allowed[] = '';
        //  echo 'id:',$id,' fac:',$fac,' typ:',$typ, ' allowed:',print_r($allowed,1);
        if (in_array($uid, $allowed)) return true;
        return false;
    }

    /*********************** update a specific cell in row(s) of table instruments **********************************************************
     * @param int $row //Instrument ID
     * @param string $cell //Cell number
     * @param string $val //Value to update the cell number with.
     * @return bool
     */
    function upd(int $row = 0, string $cell = '', string $val = ''): bool
    {
        file_put_contents("updates_function.log", date("d-m-y") . "row:" . $row . " cell:" . $cell . " val:" . $val);
        global $uid, $statusMenu, $DB;
        //file_put_contents('update_log', "\n" . date('d-m-Y H:i:s') . ' ' . $uid . ' upd: ' . print_r($row, 1) . ' ' . print_r($cell, 1) . ' ' . print_r($val, 1) . "\n", FILE_APPEND);
        if ($row == 0 || $cell == '') return false;
        // sanitize $row
        if (is_array($row)) {
            $dum = array();
            foreach ($row as $rId) {
                if ((int)$rId) $dum[] = (int)$rId;
            }
            $dum = array_unique($dum);
            if (!count($dum)) {
                $this->LOG->log($uid . ': error Empty Array' . "\n");
                return false;
            }
            $row = join(',', $dum);
        }
        /* -- sanitize $cell
         * cell could be a number (int) or the string 'type' (if type then update typeID) or the string 'calibrationDate'
        */
        switch ($cell) {
            case 'calibrationDate':
                $val = swapDate($val);
                break;

            case 'typeID':
                $cell = 'typeID';
                break;

            case 'accuracy':
                $cell = 'accuracy';
                break;

            case '0':
            case"equipmentNumber":
                if (strlen($val)) {
                    $this->checkEqID($val);
                }
                break;
            default:
                break;
        }
        if ($cell == (int)$cell) //This is a int in a string
            $cell = $this->field[(int)$cell];
        if (!in_array($cell, $this->field)) {
            $this->LOG->log($uid . ': cell ' . $cell . ' not found in ' . print_r($this->field, 1) . "\n");
            return false;
        }
        $where = "WHERE id IN ($row)";
        //$val = mysql_real_escape_string($val);
        // update
        if ($cell == 'status') list($old_status) = dbs_get("SELECT status FROM apps.instrument $where");
        //Catching a weird bug. No idea where this is generated..
        if (($cell == "equipmentNumber" || $cell == 0 || $cell == "0") && ($val == 1 || $val[0] == "{")) {
            return true;
        }
        $qry = "UPDATE apps.instrument SET `$cell`='$val',lupBy='$uid' $where";
        $res = $DB->Update($qry, []);
        //file_put_contents('update_log', "\n" . $uid . ': ' . $qry . "\n", FILE_APPEND);
        if ($res) {
            setLup();
            if ($cell == 'status') { // document change in Log
                $old_status = ($old_status == '?') ? '?' : $statusMenu[$old_status];
                $new_status = $statusMenu[$val];
                if ($old_status != $new_status) {
                    $logtext = "Status changed from \"$old_status\" to \"$new_status\"";
                    //file_put_contents('update_log', "\n$uid:$logtext\n", FILE_APPEND);
                    // entry into logbook
                    $this->logbookEntry($row, $logtext);
                }
            }
            return true;
        } else {
            //file_put_contents('update_log', 'MySql error: ' . mysql_error(), FILE_APPEND);
            return false;
        }
    }

    /**
     * This function is used by the IDB to check if an entered Equipment number is not already in use
     * @param int|string $cid The equipment ID as entered by the user.
     * @return string
     */
    function checkEqID(int|string $cid): string
    {
        $rs = $this->DB->Select("SELECT id FROM apps.instrument WHERE equipmentNumber=?", [$cid], false);
        if (!is_array($rs)) exit('DB error in 291');
        if (count($rs) >= 1) exit('Error: Equipment ID already in use.');
        return 'ok';
    }

    /**
     * @param int $ref
     * @param string $msg
     * @param string $uid
     */
    function logbookEntry(int $ref = 0, string $msg = '', string $uid = ""): void
    {
        if (!is_int($uid)) {
            $uid = 0;
            $me = "Automatic System";
        } else {
            $me = join(' ', array($_SESSION['user_fname_s'], $_SESSION['user_mname'], $_SESSION['user_lname']));
        }
        if (!$ref || !$msg) return;
        $set = array();
        $set[] = "log_hrn = '" . date('d-m-Y') . "'";
        $set[] = "log_remark = '$msg'";
        $set[] = "log_fk_id = '$ref'";
        $set[] = "log_insert = NOW()";
        $set[] = "log_insert_by = '$uid'";
        $set[] = "log_ins_by_name = '$me'";
        $set[] = "log_lup = NOW()";
        $set[] = "log_lup_by = '$uid'";
        $set[] = "log_lup_by_name = '$me'";
        $set = join(',', $set);
        $qry = "INSERT INTO apps.instrument_log SET $set";
        $this->DB->Insert($qry, []);
    }

    /**
     * This function generates an export file with calibrations to add to comput
     *
     * @param $PostArray array of Applications to Export for. The PostArray should contain the DateSelector as a first element of the array
     * @return string
     */
    function exportCalibToComput(array $PostArray): string
    {
        $ExportDate = 0;
        //Get the Date from the array, rest is Applications to export.
        if (isset($PostArray['DateSelector'])) {
            $ExportDate = $PostArray['DateSelector'];
            array_shift($PostArray);
        }
        if (isset($PostArray['tunnel'])) {
            //$Tunnel = $PostArray['tunnel'];
            array_shift($PostArray);
        }
        $ReturnString = "";
        //Name consist of Appl_[Ins_ID], so that way we can get the instrument
        foreach ($PostArray as $name => $ApplID) {
            $CalEntry = $this->getApplicationCalibration($ApplID, $ExportDate);
            $ApplEntry = $this->getInstrumentApplicationDetails($ApplID);
            $InstrumentEntry = $this->getInstrumentDetails(explode("_", $name)[1]);
            $InstrumentFullData = $this->getInstrumentEntry($InstrumentEntry[0]['id']);
            $InsType = substr($ApplEntry['comput_eq_id'], 0, -4);

            //This is the actual putting together of the export data.
            $CalEntry['cal_coeff'] = str_replace("<br />", "", $CalEntry['cal_coeff']);

            if (isset($CalEntry['inverse'])) { //There also exists an Inverse calibration, this needs to be added as well to the export
                $CalEntry['inverse'] = str_replace("<br />", "", $CalEntry['inverse']['cal_coeff']);
            }
            $ReturnString .= $this->BuildComputCalibrationHeader($InstrumentEntry[0], $CalEntry, $ApplEntry, $this->{"Apropos_Coeff_Amount_" . $InsType}, $this->{"Apropos_Extra_Amount_" . $InsType});
            //$ReturnString .= $this->Apropos_NewLine;
            $Extras = [];
            if ($this->{"Apropos_Extra_Amount_" . $InsType} > 0) {

                foreach ($this->{"Apropos_Extra_Vars_" . $InsType} as $extraName) {
                    switch ($extraName) {
                        case "rangemin":
                            $range = json_decode($InstrumentFullData['range']);
                            $item = $range->rows[0]->data[1];
                            break;
                        case "rangemax":
                            $range = json_decode($InstrumentFullData['range']);
                            $item = $range->rows[0]->data[4];
                            break;
                        case "excitation":
                            $item = (int)$InstrumentFullData['excitation'];
                            break;
                        default:
                            $item = 9999999;
                    }
                    $Extras[] = $item;
                }
            }

            $ReturnString .= $this->BuildComputCalibrationContent($CalEntry, $this->{"Apropos_Coeff_Rowlength_" . $InsType}, $this->{"Apropos_Coeff_Amount_" . $InsType}, $this->{"Apropos_Extra_Amount_" . $InsType}, $Extras);
            $ReturnString .= $this->Apropos_NewLine;
        }
        return $ReturnString;
    }

    /**
     * @param $ApplID
     * @param string|int $Date
     * @param bool $DateValidityCheck
     * @return array
     */
    function getApplicationCalibration($ApplID, string|int $Date = 0, bool $DateValidityCheck = false): array
    {
        global $INT_SECS;
        if ($Date == 0)
            $CalibrationEntry = $this->DB->Select("SELECT * FROM apps.instrument_calibration WHERE cal_fk_application_id = ? ORDER BY cal_date DESC", [$ApplID], false);
        else if (!$DateValidityCheck) {
            $CalibrationEntry = $this->DB->Select("SELECT * FROM apps.instrument_calibration WHERE cal_fk_application_id = ? AND cal_date <= STR_TO_DATE(?, '%d-%m-%Y') ORDER BY cal_date DESC", [$ApplID, $Date], false);
        } else {
            //$Date = date('Y-m-d H:i:s', date_parse_from_format("d-m-Y", $Date)); //Format date to a mysql format
            $Application = $this->getInstrumentApplicationDetails($ApplID);
            $CalibFrequency = $this->getInstrumentDetails($Application['fk_instrument_id']);
            $CalibrationEntry = $this->DB->Select("SELECT * FROM apps.instrument_calibration WHERE cal_fk_application_id = ? AND UNIX_TIMESTAMP(cal_date) < UNIX_TIMESTAMP(STR_TO_DATE(?, '%d-%m-%Y')) AND UNIX_TIMESTAMP(cal_date) > (UNIX_TIMESTAMP(STR_TO_DATE(?, '%d-%m-%Y')) - ?) ORDER BY cal_date DESC", [$ApplID, $Date, $Date, $INT_SECS[$CalibFrequency['calibrationFrequency']]], false);

        }
        $InverseCalibrationEntry = $this->DB->Select("SELECT * FROM apps.instrument_calibration_inv where cal_fk_cal_id = ?", [$CalibrationEntry[0]['cal_id']], false);

        if ($InverseCalibrationEntry && $InverseCalibrationEntry[0]) { //If an Inverse calibration exists, we add it to the CalibrationEntry that will be returned
            $CalibrationEntry[0]["inverse"] = $InverseCalibrationEntry[0];
        }
        return $CalibrationEntry[0];
    }

    /**
     * @param $ApplID
     * @return array
     */
    function getInstrumentApplicationDetails($ApplID): array
    {
        return $this->DB->Select("SELECT * FROM apps.instrument_application WHERE application_id = ?", [$ApplID]);
    }

    /**
     * @param int $instrument
     * @return array
     */
    function getInstrumentDetails(int $instrument = 0): array
    {
        if (isset($_POST['instrument'])) {
            $instrument = $_POST['instrument'];
        }
        if ((int)$instrument == 0)
            exit("No Instrument ID given");
        return $this->DB->Select("SELECT id, equipmentNumber, type, serialNumber, calibrationFrequency FROM apps.instrument WHERE id = ?", [$instrument], false);
    }

    function getInstrumentEntry($ID)
    {
        $ins = $this->DB->Select("SELECT * from apps.instrument WHERE id = ?", [$ID], false);
        return $ins[0];
    }

    /**
     * @param $Instrument
     * @param $Calibration
     * @param $Application
     * @param int $CoeffAmount
     * @param int $ExtraVars
     * @return string
     */
    private function BuildComputCalibrationHeader($Instrument, $Calibration, $Application, int $CoeffAmount = 0, int $ExtraVars = 0): string
    {
        $ReturnString = "   "; //Add the Three spaces at the start of the Header

        //Add the Equipment Nr, which is 6 chars long with leading spaces to fill it.
        $ReturnString .= $this->SetStringLength($Application['comput_eq_id'], 6);

        $ReturnString .= "    "; // 4 leading spaces
        $ReturnString .= "0"; //Subfolder nr, should always be 0 on an export
        $ReturnString .= " "; //Separating space

        //Brand name for the instrument, needs to be 10 chars with leading spaces
        $ReturnString .= $this->SetStringLength($Instrument['equipmentNumber'], 10);
        $ReturnString .= $this->SetStringLength($Instrument['type'], 10); //Type aanduiding (Bijv. PCDR820)
        $ReturnString .= $this->SetStringLength($Instrument['serialNumber'], 10); //Serienummer
        //.
        //$Calibration['cal_date'] = strptime(, '%Y-%m-%d %H:%i:%s');
        //trigger_error(var_export(strtotime($Calibration['cal_date']), true));
        $ReturnString .= $this->SetStringLength(date("Ymd", strtotime($Calibration['cal_date'])), 10);//Date of Calibration
        if (count(json_decode($Calibration['cal_coeff'])) > $CoeffAmount)
            $CoeffAmount = count(json_decode($Calibration['cal_coeff']));
        $ReturnString .= $this->SetStringLength($CoeffAmount, 5);//Number of Coefficients
        $ReturnString .= $this->SetStringLength($CoeffAmount + $ExtraVars, 5);//Total number of variables including the above number.
        return $ReturnString;
    }

    /**
     * @param $string
     * @param $length
     * @return string
     */
    private function SetStringLength($string, $length): string
    {
        if (strlen($string) <= $length) {
            $fillSpaceAmount = $length - strlen($string);
            return str_repeat(" ", $fillSpaceAmount) . $string;
        } else {
            return substr($string, 0, $length);
        }
    }

    /**
     * @param $Calibration
     * @param int $RowLength
     * @param int $CoeffCount
     * @param int $ExtraVars
     * @param array $Extras
     * @return string
     */
    private function BuildComputCalibrationContent($Calibration, int $RowLength = 4, int $CoeffCount = 1, int $ExtraVars = 0, array $Extras = array()): string
    {
        $ReturnString = "";
        $CurrentRowLength = $RowLength;
        $CurrentRow = 0;
        $Coefficients = json_decode($Calibration['cal_coeff']);
        $Coefficients = $this->array_flatten($Coefficients);
        if (isset($Calibration['inverse'])) {
            $InverseCoefficients = json_decode($Calibration['inverse']);
            $InverseCoefficients = $this->array_flatten($InverseCoefficients);
            $Coefficients = array_merge($Coefficients, $InverseCoefficients);
        }

        while (count($Coefficients) < $CoeffCount)
            $Coefficients[] = 0;

        while (count($Coefficients) % $RowLength != 0)
            $Coefficients[] = 0;

        if ($ExtraVars != 0) {
            //trigger_error("Extra var");
            //TODO: Add the actual vars here
            foreach ($Extras as $Extra) {
                $Coefficients[] = $Extra;
            }
        }


        foreach ($Coefficients as $CoEff) {

            if ($CurrentRowLength >= $RowLength) {
                $ReturnString .= $this->Apropos_NewLine;
                $CurrentRowLength = 0;
                $CurrentRow++;
                $ReturnString .= sprintf('%3s', $CurrentRow);
                $ReturnString .= "  ";
            }
            $CurrentRowLength++;
            //Format the Coefficient to follow the needed format for Comput
            // It should be E15.7, with the change that the number after the exponent should always be 2 char. (1.23455E04)
            $formattedCoefficient = sprintf("%14.7E", $CoEff);
            $coeffArray = explode("E", $formattedCoefficient);
            $coeffArray[1] = (int)$coeffArray[1];
            if ($coeffArray[1] < 10 && $coeffArray[1] > -1) {
                $coeffArray[1] = "E+0" . $coeffArray[1];
                $formattedCoefficient = implode($coeffArray);
            } else if ($coeffArray[1] < 0 && $coeffArray[1] > -10) {
                $coeffArray[1] = "E-0" . abs($coeffArray[1]);
                $formattedCoefficient = implode($coeffArray);
            } else //If the Exponent already has 2 chars we can just use the normal sprintf
                $formattedCoefficient = sprintf("%15.7E", $CoEff);
            $ReturnString .= $formattedCoefficient;
        }
        return $ReturnString;
    }

    /**
     * @param $array
     * @return array
     */
    function array_flatten($array): array
    {

        $return = array();
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $return = array_merge($return, $this->array_flatten($value));
            } else {
                $return[$key] = $value;
            }
        }
        return $return;

    }

    /* put entry in logbook */

    /**
     * @param $ApplID
     * @param $Date
     * @param $Tunnel
     * @return array
     */
    function CheckComputExportability($ApplID, $Date): array
    {
        $state = true;
        $msg = "";
        //Check if there is a Comput ID linked to the Application
        $row = $this->DB->Select("SELECT comput_eq_id FROM apps.instrument_application WHERE application_id = ?", [$ApplID]);
        if ($row['comput_eq_id'] == "") {
            $msg = "No Comput ID set for this application";
            $state = false;
        }
        //Check if the application has a valid calibration for the date
        $Calibration = $this->getApplicationCalibration($ApplID, $Date);
        if (!$Calibration || $Calibration == array()) {
            $msg = "No Valid calibration available";
            $state = false;
        }
        return array($state, $msg);
    }

    /**
     * @param int $instrument
     * @return array
     */
    function getInstrumentApplications(int $instrument = 0): array
    {
        if (isset($_POST['instrument'])) {
            $instrument = $_POST['instrument'];
        }
        if ((int)$instrument == 0)
            exit("No Instrument ID given");
        return $this->DB->Select("SELECT application_id as id, application_name, apropos_eq_id, comput_eq_id FROM apps.instrument_application WHERE fk_instrument_id = ?", [$instrument], false);

    }

    /**
     * @param $my_name String Users full name (First name, Middle name, Last name)
     *
     * Update a calibration for an instrument
     */
    #[NoReturn] function updateCalibration(string $my_name): void
    {

        global $uid;

        require_once(PATH_ICx . 'lib/utils.inc.php');

        $allowed = array('id', 'name', 'dateCal', 'coeff', 'uncert', 'equ', 'remark', 'apprChg', 'applSel', 'inUnit', 'outUnit', 'uncertAbs', 'uncertRem', 'rangeMin', 'rangeMax', 'read', 'sel', 'ch', 'applsel');
        $id = (int)$_POST['id'];
        $where = "cal_id = '$id'";
        $set = array();
        $me = mysql_real_escape_string($my_name);
        $setInv = $_POST['calInv'] == 'L2R';
        $oldval = dbs_get_assoc("SELECT cal_fk_id, cal_hrn, DATE(cal_date) as cal_date, cal_date as calDateTime, cal_coeff, cal_formula, cal_uncertainty, cal_remark FROM apps.instrument_calibration WHERE $where");
        //  jsalert(print_r($oldval,1));
        $refId = $oldval['cal_fk_id'];
        $calDate = $oldval['cal_date'];
        $calName = $oldval['cal_hrn'];
//        $uncert = $oldval['cal_uncertainty'];
//        $newdate = '';
        $msg = '';
        if (!permission($refId)) {
            jsalert('You have no permission to change the calibration!');
            exit();
        }
        //------------- update inverse calibration -----------------------------------------------------
        if ($setInv) {
            $allowed = array('coeff', 'equ', 'inUnit', 'outName', 'outUnit');
            $qry = "SELECT cal_fk_cal_id FROM apps.instrument_calibration_inv WHERE cal_fk_cal_id = '$id'";
            list($res) = dbs_get($qry);
            echo 'res=' . $res;
            if ($res != $id) dbs_insert("INSERT INTO apps.instrument_calibration_inv SET cal_fk_cal_id='$id',cal_insert=NOW(),cal_insert_by='$uid',cal_ins_by_name='$me'");
            echo "error: 8589376258932";
            $vals = array();
            foreach ($_POST as $key => $val) {
                if (!in_array($key, $allowed)) continue;
                // sanitize input
                $val = preg_replace('/<[ \/]*?script[^>]*?>/i', '', $val);
                $val = str_replace(array("\n", "\t", "\r"), array('<br />', ''), $val);
                switch ($key) {

                    case 'inUnit':
                        $set[] = "cal_inp_unit = ?";
                        $vals[] = $val;
                        break;

                    case 'outName':
                        $set[] = "cal_out_vec = ?";
                        $vals[] = $val;
                        break;

                    case 'outUnit':
                        $set[] = "cal_out_unit = ?";
                        $vals[] = $val;
                        break;

                    case 'coeff':
                        $set[] = "cal_coeff = ?";
                        $vals[] = $val;
                        break;

                    case 'equ':
                        $set[] = "cal_formula = ?";
                        $vals[] = $val;
                        break;
                }
            }
            $set[] = "cal_lup_by = ?";
            $vals[] = $uid;
            $set[] = "cal_lup_by_name = ?";
            $vals[] = $me;
            $set = join(',', $set);
            $qry = "Update apps.instrument_calibration_inv SET $set WHERE cal_fk_cal_id = '$id'";
            $this->DB->Update($qry, $vals);
            //dbs_update($qry);
            jsreply("parent.my.calAdd.done({id:'$id', 'refId':'$refId', calDate:'', approve:''});");
            exit;
        }
        //------------- update default calibration -----------------------------------------------------
        $vals = array();
        foreach ($_POST as $key => $val) {
            if (!in_array($key, $allowed)) continue;
            // sanitize input
            $val = preg_replace('/<[ \/]*?script[^>]*?>/i', '', $val);
            $val = str_replace(array("\n", "\t", "\r"), array('<br />', ''), $val);

            switch ($key) {
                case 'name':
                    $set[] = "cal_hrn = ?";
                    $vals[] = $val;
                    break;

                case 'applsel':
                    $set[] = "cal_fk_application_id = ?";
                    $vals[] = $val;
                    break;

                case 'inUnit':
                    $set[] = "cal_inp_unit = ?";
                    $vals[] = $val;
                    break;

                case 'outUnit':
                    $set[] = "cal_out_unit = ?";
                    $vals[] = $val;
                    break;

                case 'coeff':
                    $set[] = "cal_coeff = ?";
                    $vals[] = $val;
                    break;

                case 'equ':
                    $set[] = "cal_formula = ?";
                    $vals[] = $val;
                    break;

                case 'uncert': // is array
                    foreach ($val as $k => $v) {
                        $uncert_new[$k] = $v;
                    }
                    break;

                case 'uncertAbs': // is array
                    foreach ($val as $k => $v) {
                        $uncert_abs[$k] = $v;
                    }
                    break;

                case 'uncertRem': // is array
                    foreach ($val as $k => $v) {
                        $uncert_rem[$k] = mysql_real_escape_string($v);
                    }
                    break;

                case 'rangeMax':
                    foreach ($val as $k => $v) {
                        $range_max[$k] = mysql_real_escape_string($v);
                    }
                    break;

                case 'rangeMin':
                    foreach ($val as $k => $v) {
                        $range_min[$k] = mysql_real_escape_string($v);
                    }
                    break;

                case 'sel':
                    foreach ($val as $k => $v) {
                        $range_unit[$k] = mysql_real_escape_string($v);
                    }
                    break;

                case 'ch':
                    foreach ($val as $k => $v) {
                        $range_ch[$k] = mysql_real_escape_string($v);
                    }
                    break;

                case 'remark':
                    $set[] = "cal_remark = ?";
                    $vals[] = $val;
                    break;

                case 'dateCal':
                    $val = preg_replace('/[^0-9\.,-]/', '', $val);
                    $val = check_date($val);
                    if ($val) $val = "'" . swapDate($val) . "'";
                    else {
                        $val = 'NULL';
                        jsalert('The entered calibration date is not a valid date.');
                    }
                    $set[] = "cal_date = $val";
                    break;

                case 'apprChg':
                    if (!$val) break;

                    if (1 == $val) {
                        $set[] = "cal_appr = NOW()";
                        $set[] = "cal_appr_by = '$uid'";
                        $set[] = "cal_appr_by_name = '$me'";
                        $msg = 'Calibration approved.';
                    } elseif (-1 == $val) {
                        $set[] = "cal_appr = NULL";
                        $set[] = "cal_appr_by = NULL";
                        $set[] = "cal_appr_by_name = ''";
                        $msg = 'Calibration approval removed.';
                    }
                    $update = true;
                    jsalert($msg);
                    break;
            }
        }
        $set[] = "cal_uncertainty = ?";
        $vals[] = $this->uncertainty_to_JSON('', $range_ch, $uncert_new, $uncert_abs, $uncert_rem);
        $set[] = "cal_range = ?";
        $vals[] = $this->range_to_JSON($range_ch, $range_min, $range_max, $range_unit);
        $set[] = "cal_lup_by = '$uid'";
        $set[] = "cal_lup_by_name = '$me'";
        $set = join(',', $set);
        $qry = "UPDATE apps.instrument_calibration SET $set WHERE $where";
        $res = $this->DB->Update($qry, $vals);
        //file_put_contents('cal_debug.txt',$qry);
        if ($res === true) {
            // update instrument table
            if (isset($update) && $update) { // update with latest valid calibration date.
                $qry = "SELECT DATE(cal_date) FROM apps.instrument_calibration WHERE cal_fk_id='$refId' AND cal_appr ORDER BY cal_date DESC";
                list($calDate) = dbs_get($qry);
                $this->upd($refId, 'calibrationDate', swapDate($calDate));
                $ins = $this->DB->Select("SELECT cal_status from apps.instrument WHERE id = ?", [$refId]);
                if ($ins['cal_status'] == INS_NO_VALID_CAL)
                    $this->upd($refId, "cal_status", INS_CALIBRATED);
            }
            if ($calDate) $calDate = swapDate($calDate);
            else {
                $calDate = $msg ? '?' : '';
            }
            jsreply("parent.my.calAdd.done({id:'$id', 'refId':'$refId', calDate:'$calDate', approve:'$msg'});");
            setLup();
        } else echo $res;
        if ($msg) {
            $logtext = "Calibration *$calName* " . str_replace('Calibration', '', $msg);
            $this->logbookEntry($refId, $logtext);
            //file_put_contents('debug_approve.txt', "calID=$id, ref=$refId\n" . print_r($set, 1));
        }
        exit();
    }

    /**
     * @param string $m
     * @param $range_ch
     * @param $uncert_new
     * @param $uncert_abs
     * @param $uncert_rem
     * @return string
     */
    function uncertainty_to_JSON(string $m, $range_ch, $uncert_new, $uncert_abs, $uncert_rem): string
    {
        /** compose uncertainty object with structure
         *   $uncert_new = array('rows'=>array(array('id'=>'index','data'=>array('chan-name','fs','/','abs','remark'))));
         *   index: array index
         *   chan-name: array ch
         *   fs: array uncert
         *   abs: array uncertAbs
         *   remark: array uncertRem
         */
        $rows = array();
        if ($m == 'empty') {
            foreach ($range_ch as $id => $ch_name) {
                $ar = array('id' => $id, 'data' => array(mysql_real_escape_string(htmlentities($ch_name))), '', '', '', '');
                $rows[] = $ar;
            }
            return html_entity_decode(json_encode(array('rows' => $rows)));
        } else {
            //    foreach($range_ch AS $id=>$ch_name){
            //      $ar = array('id'=>$id, 'data'=>array(mysql_real_escape_string(htmlentities($ch_name)),$uncert_new[$id],'/',$uncert_abs[$id],mysql_real_escape_string(htmlentities($uncert_rem[$id]))));
            //      $rows[]=$ar;
            //    }
            foreach ($range_ch as $id => $ch_name) {
                $ar = '{"id":' . $id . ', "data":["' . join('","', array(mysql_real_escape_string(htmlentities($ch_name)), $uncert_new[$id], '/', $uncert_abs[$id], mysql_real_escape_string(htmlentities($uncert_rem[$id])))) . '"]}';
                $rows[] = $ar;
            }
            return '{"rows":[' . join(',', $rows) . ']}';
        }
    }

    /**
     * @param $range_ch
     * @param $range_min
     * @param $range_max
     * @param $range_unit
     * @return string
     */
    function range_to_JSON($range_ch, $range_min, $range_max, $range_unit): string
    {
        /** compose range object with structure
         *   $range = array('rows'=>array(array('id'=>'index','data'=>array('chan-name','min','minUnit','/','max','maxUnit','remark'))));
         *   index: array index
         *   chan-name: array ch
         *   fs: array uncert
         *   abs: array uncertAbs
         *   remark: array uncertRem
         */
        $rows = array();
        foreach ($range_ch as $id => $ch_name) {
            $ar = '{"id":' . $id . ', "data":["' . join('","', array(mysql_real_escape_string(htmlentities($ch_name)), $range_min[$id], $range_unit[$id], '/', $range_max[$id], $range_unit[$id], '')) . '"]}';
            $rows[] = $ar;
        }
        return '{"rows":[' . join(',', $rows) . ']}';
        // return html_entity_decode(json_encode(array('rows'=>$rows)));

    }

    /**
     *
     */
    #[NoReturn] function addInstrument(): void
    {
        require_once(PATH_ICx . 'lib/utils.inc.php');
        $allowed = array('equipmentID', 'equipmentNumber', 'type', 'serialNumber', 'range', 'calibrationFrequency', 'remarks', 'storageLocation', 'equipmentManager', 'fac', 'typeID', 'ip', 'excitation', 'supplier', 'copy', 'copyFrom');
        foreach ($_POST as $key => $val) {
            if (!in_array($key, $allowed)) continue;
            if ($key == 'copy') continue;
            if ($key == 'fac') {
                $key = 'facility';
                $val = $fac = $this->facs[$val];
            } elseif ($key == 'equipmentID') {
                $key = 'equipmentNumber';
            } elseif ($key == 'typeID') {
                $typ = $val;
            }
            if ($key == 'equipmentNumber' && strlen($val)) { // check for existing equipment number
                $rs = $this->DB->Select("SELECT id FROM apps.instrument WHERE status != '9' AND equipmentNumber=?", [$val], false);
                if ($rs === false) jsreply('DB error in 514');
                if (isset($rs[0])) {
                    jsalert('Error: Equipment ID already in use. Instrument not added.');
                    exit();
                }
            }
            if ($key == 'copyFrom') {
                if ($val && (isset($_POST['copy']) && $_POST['copy'])) {
                    list($range, $accuracy, $gain, $filter, $docs) = dbs_get("SELECT `range`,accuracy,gain,filter,documents FROM apps.instrument WHERE id='$val'");
                    $set[] = "`range`='$range'";
                    $set[] = "accuracy='$accuracy'";
                    $set[] = "gain='$gain'";
                    $set[] = "filter='$filter'";
                    $set[] = "documents='$docs'";
                }
                continue;
            }
            // for test period only
            //    if($key == 'equipmentManager') $val=$uid;
            $val = preg_replace('/<[ \/]*?script[^>]*?>/i', '', $val);
            $val = str_replace(array("\n", "\t", "\r"), array('<br />', ''), $val);
            $val = mysql_real_escape_string($val);
            $set[] = "`$key` = '$val'";
        }
        if (!permission('', $fac, $typ)) {
            jsalert('No permission to add instruments.');
            exit();
        }
        if (empty($set)) exit('empty');
        // set default status
        $set[] = "status = '1'";
        $set[] = "phys_units= '{\"type\":\"Phys\",\"rows\":[]}'";
        $set[] = "chan_properties= '{\"type\":\"Chan\",\"rows\":[]}'";
        $set = join(',', $set);
        $qry = "INSERT INTO apps.instrument SET $set";
        $id = $this->DB->Insert($qry, []);
        //file_put_contents('insert_log', "\n" . $qry . "\n", FILE_APPEND);
        //file_put_contents('insert_log', 'MySql: ' . mysql_error(), FILE_APPEND);
        if ((int)$id) {
            setLup();
            jsreply('parent.my.addRow.done(' . $this->getInstrumentDetails($id)[0]['id'] . ');');
            exit();
        }
        jsalert('not inserted ' . $id[1]);
        exit();
    }

    /**
     * @param $id
     * @return string
     */
    function deleteInstrument($id): string
    {
        if (is_string($id) && strpos($id, ",")) {
            $ids = explode(",", $id);
            $returnString = "";
            foreach ($ids as $instrument) {
                $returnString .= $this->deleteInstrument((int)$instrument) . "\n";
            }
            return $returnString;
        }

        $id = (int)$id;
        if ($id == 0) {
            return "Incorrect instrument id";
        }

        $ins = $this->DB->Select("SELECT * FROM apps.instrument WHERE id = ?", [$id]);

        if (!$ins || !$ins[0]) {
            return "Instrument not found";
        }

        $this->DB->Delete("DELETE from apps.instrument_calibration WHERE cal_fk_id = ?", [$id]);
        $this->DB->Delete("DELETE from apps.instrument_application WHERE fk_instrument_id = ?", [$id]);
        $this->DB->Delete("DELETE from apps.instrument_log WHERE log_fk_id = ?", [$id]);

        if ($this->DB->Delete("DELETE from apps.instrument WHERE id = ?", [$id])) {
            return "Instrument has been deleted";
        } else {
            return "Something went wrong with deleting the instrument";
        }

    }

    /**
     * @param string $where0
     * @param string $fac
     * @param string $type
     * @param string $json
     * @return array|string
     */
    function getInstruments(string $where0 = "", string $fac = "", string $type = "", string $json = "", bool $trueJSON = false): array|string
    {
        $where = [];
        if (strlen($where0))
            $where[] = $where0;
        if ($fac != "")
            $where[] = "FIND_IN_SET(facility,'$fac')";
        if ($type != "")
            $where[] = "typeID='$type'";
        //if(is_array($where)){
        //	$wString = $where[0];
        //	array_shift($where);
        //	foreach($where as $w)
        //		$wString .= " AND ".$w;
        //	$where = $wString;
        //}
        $where = join(' AND ', $where);

        $qry = "
			SELECT
				id,
				typeID,
				equipmentManager,
				equipmentNumber,
				type,
				serialNumber,
				phys_units,
				IF(calibrationDate,DATE_FORMAT(calibrationDate,'%d-%m-%Y'),'?'),
				calibrationFrequency,
				calibrationRequ,
				remarks,
				storageLocation,
				CONCAT_WS(' ',EM.user_fname_s,EM.user_mname,EM.user_lname),
				DATE_FORMAT(lup,'%d-%m-%Y'),
				CONCAT_WS(' ', LBY.user_fname_s, LBY.user_mname,LBY.user_lname),
				wtParameter,
				facility,
				phys_status,
				cal_status,
				ip,
				supplier,
				chan_properties
			FROM apps.instrument
			LEFT OUTER JOIN infocenter.info_user AS EM ON EM.user_id=equipmentManager
			LEFT OUTER JOIN infocenter.info_user AS LBY ON LBY.user_id=lupBy
			WHERE $where
			AND status < 9
		  ";
        $rs = $this->DB->Select($qry, [], false, true);
        if (!$rs) exit('Error: 6456456' . "\n" . $qry);
        $uid = $_SESSION['user_id'];
        $rights = $_SESSION['my_or_idb.php'];
        if ($this->User->hasAdminRights())
            $rights = OBJ_IS_ADMIN;
        if ($json && !$trueJSON) {
            $result = array();
            foreach ($rs as $row) {
                $res = '';
                $id = array_shift($row);
                $typeid = array_shift($row);
                $em_id = array_shift($row);
                $r = ($em_id == $uid) ? 6 : $rights;
                $res .= "'$id':{userdata:{em:'$em_id',r:'$r',typeID:'$typeid'}, data:[";
                $v = array_shift($row);
                $res .= "'$v'";
                foreach ($row as $cell) {
                    $cell = str_replace(array("\n", "\r", "\t"), array("<br>", ''), $cell);
                    $res .= ",'" . addslashes($cell) . "'";
                }
                $res .= ']}';
                $result[] = $res;
            }
            $result = '{' . join(',', $result) . '}';
        } else if ($trueJSON) {
            $result = json_encode($rs);
        } else {
            $result = '';
            foreach ($rs as $row) {
                $id = array_shift($row);
                $r = ($row[11] == $uid) ? 6 : $rights;
                $result .= "<row id='$id'>";
                $result .= '<cell>' . $r . '</cell>';
                foreach ($row as $cell) {
                    $result .= '<cell><![CDATA[' . htmlentities($cell) . ']]></cell>';
                }
                $result .= '</row>';
            }
        }
        return $result;
    }

    /********************************************************Cron Functions******************************************/

    /**
     * This function checks which Applications have Calibrations that have expired or are almost expired,
     * and then e-mails the Equipment Manager for the instrument
     * @produces Mails to the Managers of the Instruments
     * @throws phpmailerException
     */
    function sendCalibReminders(): bool
    {
        $SecPerMonth = 60 * 60 * 24 * 30;
        //Get a list of all the latest calibrations
        $ins = $this->DB->Select($this->q_GetLatestCalibrationsRemind, [0, CALIB_REMIND_FREQ], false);
        //AND date_add(calib.cal_date, INTERVAL ? SECOND) < NOW()
        //AND date_add(date_add(calib.cal_date, INTERVAL -? SECOND), INTERVAL calibrationFrequency*30*24*60*60 SECOND) < NOW()
        $Managers = [];
        foreach ($ins as $in) {
            //trigger_error($in['calibrationDate']);
            if (($in['calibrationDate'] + ($in['calibrationFrequency'] * $SecPerMonth)) < time() + CALIB_REMIND_BEFORE) {
                $Managers[$in['equipmentManager']] .= "Instrument: " . $in['insName'] . " | application: " . $in['name'] . " | expires on: " . date("d-M-Y", ($in['calibrationDate'] + ($in['calibrationFrequency'] * $SecPerMonth))) . "<br />";
                $this->DB->UpdateHelper("apps", "instrument_application", array("last_reminder_sent" => date('Y-m-d H:i:s')), "application_id = " . $in['applId']);
                $this->logbookEntry($in['id'], "Sent Calibration reminder to Instrument Manager for Application: " . $in['insName']);
            }
        }
        return $this->sendMailToManagers($Managers, "instrumentation_calib_reminder");
        //return $Managers;
    }

    /**
     * @param $Managers
     * @param $mailType
     * @return bool
     * @throws phpmailerException
     */
    function sendMailToManagers($Managers, $mailType): bool
    {
        foreach ($Managers as $manager => $insString) {
            //trigger_error(var_export($insString, true));
            $Mailer = new DNW_Mailer();
            $ManagerName = $this->User->getUserNameById($manager);
            $ManagerName = $ManagerName["user_fname"] . " " . $ManagerName["user_lname"];
            $Mailer->Prepare_Mail($mailType, "en", array("FullName" => $ManagerName, "InstrumentString" => $insString));
            if ($this->User->isActive($manager))
                $Mailer->Add_Receiver($manager);
            else  //User is not active anymore. Send the mail to the infocenter admin
                $Mailer->Add_Receiver(ADMIN_USER_ID);
            $MailSent = $Mailer->Send_Mail();
            if (!$MailSent) {
                trigger_error("Mail sending error");
            }
        }
        return true;

    }


    /**
     *
     * @throws phpmailerException
     */
    function checkCalibrationFrequency(): void
    {
        $noFrequency = $this->DB->Select('SELECT * from apps.instrument WHERE (calibrationFrequency IS NULL OR calibrationFrequency = "") AND cal_status = 1 AND calibrationDate != "0000-00-00"', []);
        $noCalibration = $this->DB->Select('SELECT * from apps.instrument WHERE (calibrationFrequency IS NULL OR calibrationFrequency = "") AND cal_status = 1 AND calibrationDate = "0000-00-00"', []);
        $Managers = [];
        foreach ($noFrequency as $noFreq) {

            $Managers[$noFreq['equipmentManager']] .= "The instrument " . $noFreq['equipmentNumber'] . " has no frequency, will be set to No Valid Calibration <br />";
            $this->DB->UpdateHelper("apps", "instrument", array("cal_status" => INS_NO_VALID_CAL), "id = " . $noFreq['id']);
        }
        $this->sendMailToManagers($Managers, "instrumentation_no_freq");
        $Managers = [];
        foreach ($noCalibration as $noCal) {
            if ($noCal['equipmentManager'] == "" || $noCal['equipmentManager'] == NULL)
                $noCal['equipmentManager'] = ADMIN_USER_ID;
            if (!isset($Managers[$noCal['equipmentManager']]))
                $Managers[$noCal['equipmentManager']] = "";
            $Managers[$noCal['equipmentManager']] .= " " . $noCal['equipmentNumber'] . "<br />";
            if ($noCal['facility'] == FACILITY_NWB)
                unset($Managers[$noCal['equipmentManager']]);
            //$this->DB->UpdateHelper("apps", "instrument", array("cal_status" => INS_NO_VALID_CAL), "id = " . $noCal['id']);
        }
        $this->sendMailToManagers($Managers, "instrumentation_no_cal");
    }

    /**
     *
     */
    function CheckCalibrations(): void
    {
        $SecPerMonth = 30 * 24 * 60 * 60;
        $overtimeCalibrations = array();
        $undertimeCalibrations = array();
        //Get all the latest calibrations, sorted by wether the status is Calibrated, or not Calibrated.
        $latestCalibrationsCalibrated = $this->DB->Select($this->q_GetLatestCalibrations, [0, INS_CALIBRATED], false);
        $latestCalibrationsNotCalibrated = $this->DB->Select($this->q_GetLatestCalibrations, [0, INS_NO_VALID_CAL], false);

        //Get all the Expired items and non-expired items
        foreach ($latestCalibrationsCalibrated as $cal) {
            if (($cal['calibrationDate'] + ($cal['calibrationFrequency'] * $SecPerMonth)) < time())
                $overtimeCalibrations[] = $cal;
            $this->updateCalDate($cal);
        }
        foreach ($latestCalibrationsNotCalibrated as $cal) {
            if (($cal['calibrationDate'] + ($cal['calibrationFrequency'] * $SecPerMonth)) > time())
                $undertimeCalibrations[] = $cal;
            $this->updateCalDate($cal);
        }

        //Count out the calibrations and update them
        if (count($overtimeCalibrations) > 0)
            foreach ($overtimeCalibrations as $cal) {
                trigger_error("Setting to overTime: " . $cal['id']);
                $this->DB->Update("UPDATE apps.instrument SET cal_status = ? WHERE id = ?", [INS_NO_VALID_CAL, $cal['id']]);
                $this->logbookEntry($cal['id'], "Calibration status changed, calibration out of time");
                //trigger_error("Updating instrument to No Valid Cal: " . $cal['id']);
            }

        if (count($undertimeCalibrations) > 0)
            foreach ($undertimeCalibrations as $cal) {
                trigger_error("Setting to underTime: " . $cal['id']);
                $this->DB->Update("UPDATE apps.instrument SET cal_status = ? WHERE id = ?", [INS_CALIBRATED, $cal['id']]);
                $this->logbookEntry($cal['id'], "Calibration status changed, calibration ok");
            }
    }

    /**
     * @param mixed $cal
     * @return void
     */
    public function updateCalDate(mixed $cal): void
    {
        if ($cal['calibrationDate'] != $cal['calibratedDate']) {
            $this->logbookEntry($cal['id'], "CalibrationDate was wrong, was: " . date("Y-m-d", $cal['calibratedDate']) . " Updating to: " . date("Y-m-d", $cal['calibrationDate']));
            $this->DB->Update("UPDATE apps.instrument SET calibrationDate = ? WHERE id = ?", [date("Y-m-d", $cal['calibrationDate']), $cal['id']]);
        }
    }

    function exportToPTC($id): array
    {
        $ins = $this->getInstrumentEntry($id);
        $supplier = $this->Companies->getCompanyName($ins['supplier']);
        $phys_units = json_decode($ins['phys_units']);
        $chan_properties = json_decode($ins['chan_properties']);
        $excitation = $chan_properties->rows[0]->data[2];
        foreach ($phys_units->rows as $i => $phys_unit) {
            $rangeRowsArray["rows"][] = array("id" => $phys_unit->id,
                "data" => array(
                    "name" => $phys_unit->id,
                    "min" => $phys_unit->data[0],
                    "minUnit" => $phys_unit->data[2],
                    "max" => $phys_unit->data[1],
                    "maxUnit" => $phys_unit->data[2],
                    "remark" => $phys_unit->data[7]
                ));
            $accuracyArray["rows"][] = array("id" => $phys_unit->id,
                "data" => array(
                    "name" => $phys_unit->id,
                    "accuracyFs" => ($phys_unit->data[6] != "" ? (float)$phys_unit->data[6] - (float)$phys_unit->data[5] : (float)$phys_unit->data[5]),
                    "del" => "",
                    "accuracyAbs" => "",
                    "remark" => $phys_unit->data[7]
                ));
            $gainArray["rows"][] = array("id" => $phys_unit->id,
                "data" => array(
                    "name" => $phys_unit->id,
                    "gain" => $chan_properties->rows[$i]->data[0],
                    "remark" => $phys_unit->data[7]
                ));
            $filterArray["rows"][] = array("id" => $phys_unit->id,
                "data" => array(
                    "name" => $phys_unit->id,
                    "filter" => $chan_properties->rows[$i]->data[1],
                    "remark" => $phys_unit->data[7],
                    "filterUnit" => ""
                ));
        }
        return array(
            "equID" => $ins['equipmentNumber'],
            "type" => $ins['type'],
            "serialNr" => $ins['serialNumber'],
            "supplier" => $supplier,
            "range" => $rangeRowsArray,
            "accuracy" => $accuracyArray,
            "calFreq" => $ins['calibrationFrequency'],
            "calibrationDate" => $ins['calibrationDate'],
            "gain" => $gainArray,
            "filter" => $filterArray,
            "excitation" => $excitation,
            "excitationUnit" => "", // This has been added in the Excitation itself
            "IP" => $ins['ip'],
            "phys_status" => $this->getPhysStatusName($ins['phys_status']),
            "cal_status" => $this->getCalStatusName($ins['cal_status'])

        );
    }


    /**
     * function to synchronise all instruments containing an IP address to NetBox
     * @return void
     */
    function syncInstrumentsToNB(){
        global $apiUrl, $apiKey;
        include_once "NetBox/helpers.php";

        //Get all instruments with Ip-Adresses set
        $instrList = $this->getInstruments("IP <> ''", "", "", true, true);
        $instrList = json_decode($instrList, true);

        //array linking IDB facilities to NB Locations
        $SitesList = array(
            FACILITY_LLF => 6,
            FACILITY_HST => 3,
            FACILITY_LST => 2
        );
        foreach($instrList as $ICinstrument){

            //Some instruments have malformed, or non-ip entries in this field.
            if(preg_match('/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/', $ICinstrument['ip'], $ip_matches)) {
                $ICinstrument['ip'] = $ip_matches[0];
                // print_r($ICinstrument['ip']);
            }else{
                continue;
            }

            print_r("Getting instrument " . $ICinstrument['id']);
            $NBInstrument = NBGetInstrument($ICinstrument['id']);
//            print_r($NBInstrument);

            if($NBInstrument["count"] <= 0){
                //The instrument doesn't exist yet, create it
                print_r("<br \>Instrument Not found!");
                $Instrument = array();
                $Instrument['name'] = $ICinstrument['equipmentNumber'];
                $Instrument["role"] = 17; //Magic number indicating IC_Instrument role
                $Instrument['manufacturer'] = 24; //Magic number for the Manufacturer "Generic"
                $Instrument['device_type'] = 97; //Magic number for the "Generic Measuring Instrument" Type
                $Instrument['custom_fields']['IC_IDB_ID'] = $ICinstrument['id'];
                $Instrument['status'] = "active";
                $Instrument['site'] = $SitesList[$ICinstrument['facility']];

                $NewInstrument = NBCreateInstrument($Instrument);

                //In NB an Instrument needs an Interface before we can assign an IP, so create this first.
                $Interface['name'] = "Ethernet";
                $Interface['type'] = "1000base-tx";
                $Interface['device'] = $NewInstrument['id'];
                NBCreateInterface($Interface);

                $NBInstrument = NBGetInstrument($ICinstrument['id']);
                print_r("<br \>Instrument and Interface created");
            }

            print_r("<br \>Getting instrument interface");
            $NBInstrumentInterface = NBGetInterface($NBInstrument['results'][0]['id'])['results'][0];

            if($NBInstrument['results'][0]['primary_ip'] == null || $NBInstrument['results'][0]['primary_ip']['address'] != $ICinstrument['ip'] . "/32"){
                //We don't have a set IP address in NetBox, or the wrong address set.
                print_r("<br \>Getting IP entry from NetBox");
                $NBIpAddress = NBGetIPAddress($ICinstrument['ip']);

                if($NBIpAddress['count'] == 0){ //IP-address does not exist yet, create it!
                    $NBIpAddress = NBCreateIpAddress($ICinstrument['ip'] . "/32", $ICinstrument['equipmentNumber']);
                    print_r("<br \>Created IP entry: " . $NBIpAddress['id']);
                } else
                    $NBIpAddress = $NBIpAddress['results'][0]; //To make sure the new IP and gotten IP look the same

                //NetBox wants only the updated fields
                $UpdateIPAddress['assigned_object_type'] = "dcim.interface";
                $UpdateIPAddress['assigned_object_id'] = $NBInstrumentInterface['id'];
                print_r("<br \>Assigning IP to Interface ");
                NBUpdateIPAddress($NBIpAddress['id'], $UpdateIPAddress);

                $UpdateInstrument['primary_ip'] = $NBIpAddress['id'];
                $UpdateInstrument['primary_ip4'] = $NBIpAddress['id'];
                print_r("<br \> Set IP as Primary on Device");
                NBUpdateInstrument($NBInstrument['results'][0]['id'], $UpdateInstrument);
            }
        }
    }

    function getPhysStatusName($id): string
    {
        switch ($id) {
            case PHYS_IN_USE:
                return "In use";
            case PHYS_DEFECT:
                return "Defect";
            case PHYS_REMOVED:
                return "Removed";
            case PHYS_UNDER_CALIB:
                return "Under Calibration";
            case PHYS_OK:
                return "Ok";
            default:
                return "Physical status unknown";
        }
    }

    function getCalStatusName($id): string
    {
        //Calibration Statuses
        switch ($id) {
            case INS_CALIBRATED:
                return "Calibrated";
            case INS_CALIB_BEFORE_USE:
                return "Calibrate before use";
            case INS_CHECK_CALIB:
                return "Check Calibration before use";
            case INS_NO_CALIB_REQ:
                return "No Calibration required";
            case INS_INDICATION_ONLY:
                return "For indication only";
            case INS_DEFECT_DO_NOT_USE:
                return "Defect, do not use";
            case INS_NO_VALID_CAL:
                return "No valid calibration";
            default:
                return "Calibration status unknown";
        }
    }
}
