#!/bin/bash

# Sync SVN externals manually (i.e. for use with git-svn bridge)
# Author: <PERSON><PERSON> <<EMAIL>>

echo "Syncing SVN externals..."

# Create directories if they don't exist
mkdir -p docs API/maintenance wiki tests ICx/apps/IDB ICx/apps/maintenance apps/AccessControl apps/nettools

# Update each external
svn checkout http://scm.dnw.aero/svn/infocenter/docs docs
svn checkout http://scm.dnw.aero/svn/infocenter-maintenance/API API/maintenance
svn checkout http://scm.dnw.aero/svn/infocenter_wiki wiki  
svn checkout http://scm.dnw.aero/svn/ic-testing tests
svn checkout http://scm.dnw.aero/svn/infocenter-idb ICx/apps/IDB
svn checkout http://scm.dnw.aero/svn/infocenter-maintenance ICx/apps/maintenance
svn checkout http://scm.dnw.aero/svn/InfoCenter-AccessControl@7 apps/AccessControl
svn checkout http://scm.dnw.aero/svn/ic_nettools apps/nettools

echo "External sync complete"
