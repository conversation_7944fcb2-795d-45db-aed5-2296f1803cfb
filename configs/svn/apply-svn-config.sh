#!/bin/bash

# <PERSON>ript to apply SVN ignore patterns globally
# Author: <PERSON><PERSON> Groesbeek <<EMAIL>>

echo "Applying SVN global ignore patterns..."

# Ensure .subversion directory exists
mkdir -p ~/.subversion

# Backup existing config
if [ -f ~/.subversion/config ]; then
    cp ~/.subversion/config ~/.subversion/config.backup.$(date +%Y%m%d-%H%M%S)
    echo "Backup created: ~/.subversion/config.backup.$(date +%Y%m%d-%H%M%S)"
fi

# Define our ignore patterns
IGNORE_PATTERNS="*.gz *.tmp *.bak *~ .DS_Store Thumbs.db vendor node_modules .env .vscode .idea *.swp *.swo composer.phar svn-commit.tmp discover-*.json formdata_log.txt debug*.txt error_log.txt"

# Apply global ignores
if [ -f ~/.subversion/config ]; then
    # Update existing config
    if grep -q "global-ignores" ~/.subversion/config; then
        sed -i "s/^global-ignores =.*/global-ignores = $IGNORE_PATTERNS/" ~/.subversion/config
        echo "Updated existing global-ignores in ~/.subversion/config"
    else
        # Add global-ignores to [miscellany] section
        if grep -q "^\[miscellany\]" ~/.subversion/config; then
            sed -i "/^\[miscellany\]/a global-ignores = $IGNORE_PATTERNS" ~/.subversion/config
            echo "Added global-ignores to existing [miscellany] section"
        else
            echo -e "\n[miscellany]\nglobal-ignores = $IGNORE_PATTERNS" >> ~/.subversion/config
            echo "Created new [miscellany] section with global-ignores"
        fi
    fi
else
    # Create new config
    echo "[miscellany]" > ~/.subversion/config
    echo "global-ignores = $IGNORE_PATTERNS" >> ~/.subversion/config
    echo "Created new ~/.subversion/config"
fi

echo "SVN global ignore patterns applied successfully"
echo "Patterns: $IGNORE_PATTERNS"