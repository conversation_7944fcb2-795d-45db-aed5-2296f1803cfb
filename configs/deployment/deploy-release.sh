#!/bin/bash

# Script to deploy a new release of Infocenter
# Author: <PERSON><PERSON> <<EMAIL>>

set -e

# Parse arguments
DRY_RUN=false
VERBOSE=false
SKIP_AUTH_CHECK=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --skip-auth-check)
            SKIP_AUTH_CHECK=true
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --dry-run           Show what would be done without making changes"
            echo "  --verbose           Show detailed output"
            echo "  --skip-auth-check   Skip SVN authentication check"
            echo "  -h, --help          Show this help message"
            echo ""
            echo "Prerequisites:"
            echo "  - SVN authentication will be prompted if needed"
            echo "  - Credentials are temporarily stored and cleaned up"
            echo ""
            echo "Examples:"
            echo "  $0 --dry-run              # Test deployment without changes"
            echo "  $0 --dry-run --verbose    # Test with detailed output"
            echo "  $0                        # Run actual deployment"
            exit 0
            ;;
        *)
            echo "Unknown option $1"
            exit 1
            ;;
    esac
done

# Configuration
REPO_URL="http://scm.dnw.aero/svn/infocenter"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
REPO_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Environment detection
if [ -d "/var/www/infocenter" ]; then
    # Production/Test server environment
    PROD_DIR="/var/www/infocenter"
    MIGRATION_URL="https://infocenter.dnw.aero"
    ENVIRONMENT="server"
elif [ -f "composer.json" ] && [ -d "ICx" ]; then
    # Current directory looks like infocenter - use it (local development)
    PROD_DIR="$(pwd)"
    MIGRATION_URL="http://localhost/"
    ENVIRONMENT="local"
else
    echo -e "${RED}Error: Cannot detect infocenter environment${NC}"
    echo "Please run from infocenter directory or specify path"
    echo "Current directory: $(pwd)"
    echo "Home directory: $HOME"
    echo ""
    echo "Supported environments:"
    echo "  1. Production server: /var/www/infocenter"
    echo "  2. Current directory: Must contain composer.json and ICx/ folder"
    echo "  3. Home directory: ~/webapps/infocenter"
    exit 1
fi

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Credential management
SVN_CREDS_SET=false

setup_svn_credentials() {
    if [ "$DRY_RUN" = true ] || [ "$SKIP_AUTH_CHECK" = true ]; then
        echo -e "${BLUE}[DRY RUN] Skipping SVN credential setup${NC}"
        return 0
    fi
    
    echo -e "${YELLOW}🔐 Setting up SVN credentials...${NC}"
    
    # Test if we already have working credentials
    if svn info "$REPO_URL/trunk" --non-interactive >/dev/null 2>&1; then
        echo -e "${GREEN}✅ SVN credentials already working${NC}"
        return 0
    fi
    
    echo -e "${YELLOW}SVN credentials needed for: $REPO_URL${NC}"
    read -p "SVN Username: " SVN_USERNAME
    read -s -p "SVN Password: " SVN_PASSWORD
    echo ""
    
    # Test credentials
    if ! svn info "$REPO_URL/trunk" --username "$SVN_USERNAME" --password "$SVN_PASSWORD" --non-interactive >/dev/null 2>&1; then
        echo -e "${RED}❌ SVN authentication failed${NC}"
        cleanup_svn_credentials
        exit 1
    fi
    
    # Export for use in other commands
    export SVN_USERNAME
    export SVN_PASSWORD
    SVN_CREDS_SET=true
    
    echo -e "${GREEN}✅ SVN credentials validated${NC}"
}

cleanup_svn_credentials() {
    if [ "$SVN_CREDS_SET" = true ]; then
        echo -e "${YELLOW}🧹 Cleaning up SVN credentials...${NC}"
        unset SVN_USERNAME
        unset SVN_PASSWORD
        SVN_CREDS_SET=false
    fi
}

# Trap to ensure cleanup on exit
trap cleanup_svn_credentials EXIT

# Helper function for dry run
run_command() {
    local cmd="$1"
    local description="$2"
    
    # Add SVN credentials to SVN commands if available
    if [ "$SVN_CREDS_SET" = true ] && [[ "$cmd" == *"svn "* ]]; then
        cmd=$(echo "$cmd" | sed "s/svn /svn --username \"\$SVN_USERNAME\" --password \"\$SVN_PASSWORD\" --non-interactive /g")
    fi
    
    if [ "$DRY_RUN" = true ]; then
        echo -e "${BLUE}[DRY RUN] $description${NC}"
        echo -e "${BLUE}  Command: $cmd${NC}"
    else
        echo -e "${GREEN}$description${NC}"
        if [ "$VERBOSE" = true ]; then
            echo -e "${YELLOW}  Executing: $cmd${NC}"
        fi
        eval "$cmd"
    fi
}

# Header
if [ "$DRY_RUN" = true ]; then
    echo -e "${BLUE}🔍 Infocenter Deployment Script (DRY RUN)${NC}"
    echo -e "${BLUE}No changes will be made to the system${NC}"
else
    echo -e "${GREEN}🚀 Infocenter Deployment Script${NC}"
fi
echo "=================================="
echo -e "${YELLOW}Environment: $ENVIRONMENT${NC}"
echo -e "${YELLOW}Working directory: $PROD_DIR${NC}"
echo -e "${YELLOW}Migration URL: $MIGRATION_URL${NC}"
echo ""

# Set up SVN credentials early
setup_svn_credentials

# Step 1: Get current tag
echo -e "${YELLOW}📋 Current deployment info:${NC}"
if [ -d "$PROD_DIR" ]; then
    CURRENT_TAG=$(svn info "$PROD_DIR" 2>/dev/null | grep "Relative URL" | cut -d'/' -f3 || echo "unknown")
    echo "Current tag: $CURRENT_TAG"
    echo "Production directory: $PROD_DIR"
else
    echo -e "${RED}Warning: Production directory $PROD_DIR not found${NC}"
    CURRENT_TAG="*******"  # fallback
fi

# Step 2: Version bump
echo -e "${YELLOW}📈 Version selection:${NC}"
IFS='.' read -r major minor patch build <<< "${CURRENT_TAG//v/}"
echo "Current version: $major.$minor.$patch.$build"
echo "1) Patch release ($major.$minor.$patch.$((build+1)))"
echo "2) Minor release ($major.$minor.$((patch+1)).0)"
echo "3) Major release ($major.$((minor+1)).0.0)"
echo "4) Custom version"

if [ "$DRY_RUN" = true ]; then
    echo -e "${BLUE}[DRY RUN] Auto-selecting patch release for demo${NC}"
    version_choice=1
else
    read -p "Select version type [1-4]: " version_choice
fi

case $version_choice in
    1) NEW_VERSION="$major.$minor.$patch.$((build+1))" ;;
    2) NEW_VERSION="$major.$minor.$((patch+1)).0" ;;
    3) NEW_VERSION="$major.$((minor+1)).0.0" ;;
    4) 
        if [ "$DRY_RUN" = true ]; then
            NEW_VERSION="$major.$minor.$patch.$((build+1))"
        else
            read -p "Enter custom version: " NEW_VERSION
        fi
        ;;
    *) echo "Invalid choice"; exit 1 ;;
esac

NEW_TAG="$NEW_VERSION"
echo -e "${GREEN}New version will be: $NEW_TAG${NC}"

# Step 3: Pre-deployment checks
echo -e "${YELLOW}🔍 Pre-deployment checks:${NC}"
if [ "$DRY_RUN" = true ]; then
    echo -e "${BLUE}[DRY RUN] Skipping interactive confirmations${NC}"
    snapshot_confirm="y"
    users_confirm="y"
    create_tag="y"
elif [ "$ENVIRONMENT" = "local" ]; then
    echo -e "${YELLOW}⚠️  LOCAL DEVELOPMENT ENVIRONMENT DETECTED${NC}"
    echo -e "${YELLOW}Running this script locally will:${NC}"
    echo -e "${YELLOW}  - Create a REAL SVN tag (affects entire team)${NC}"
    echo -e "${YELLOW}  - Switch your local dev environment to production mode${NC}"
    echo -e "${YELLOW}  - Remove dev dependencies (may break your setup)${NC}"
    echo ""
    echo -e "${RED}Are you sure you want to proceed?${NC}"
    echo "Consider using --dry-run for testing"
    read -p "Continue with REAL deployment from local? [y/N]: " local_confirm
    if [[ ! "$local_confirm" =~ ^[Yy]$ ]]; then
        echo -e "${GREEN}Cancelled - use --dry-run for safe testing${NC}"
        exit 0
    fi
    echo -e "${YELLOW}Skipping snapshot and user notification checks for local environment${NC}"
    snapshot_confirm="y"
    users_confirm="y"
else
    read -p "Have you created a Hyper-V snapshot? [y/N]: " snapshot_confirm
    if [[ ! "$snapshot_confirm" =~ ^[Yy]$ ]]; then
        echo -e "${RED}Please create a snapshot first!${NC}"
        exit 1
    fi

    read -p "Have you notified users of the deployment? [y/N]: " users_confirm
    if [[ ! "$users_confirm" =~ ^[Yy]$ ]]; then
        echo -e "${RED}Please notify users first!${NC}"
        exit 1
    fi
fi

# Step 4: Create tag
echo -e "${YELLOW}🏷️  Creating tag $NEW_TAG...${NC}"
if [ "$DRY_RUN" = true ]; then
    create_tag="y"
else
    read -p "Proceed with tag creation? [y/N]: " create_tag
fi

if [[ "$create_tag" =~ ^[Yy]$ ]]; then
    run_command "svn copy '$REPO_URL/trunk' '$REPO_URL/tags/$NEW_TAG' -m 'Create release tag $NEW_TAG'" "Creating SVN tag $NEW_TAG"
else
    echo "Tag creation skipped"
    exit 1
fi

# Step 5: Backup critical files
echo -e "${YELLOW}💾 Backing up critical files...${NC}"
BACKUP_SCRIPT="$REPO_ROOT/configs/deployment/backup-critical-files.sh"
if [ -f "$BACKUP_SCRIPT" ]; then
    run_command "chmod +x '$BACKUP_SCRIPT' && '$BACKUP_SCRIPT'" "Running backup script from current checkout"
else
    echo -e "${RED}Warning: Backup script not found at $BACKUP_SCRIPT${NC}"
fi

# Step 6: Switch to new tag
echo -e "${YELLOW}🔄 Switching to new tag...${NC}"
run_command "cd '$PROD_DIR' && svn switch '$REPO_URL/tags/$NEW_TAG'" "Switching to tag $NEW_TAG"

# Step 7: Fix permissions in new checkout
echo -e "${YELLOW}🔧 Setting script permissions...${NC}"
run_command "find '$PROD_DIR/configs' -name '*.sh' -exec chmod +x {} \;" "Setting executable permissions on all shell scripts"

# Step 8: Install dependencies
echo -e "${YELLOW}📦 Installing dependencies...${NC}"
if [ "$ENVIRONMENT" = "local" ]; then
    echo -e "${YELLOW}⚠️  Local environment: Installing with dev dependencies${NC}"
    run_command "cd '$PROD_DIR' && composer install --optimize-autoloader" "Installing Composer dependencies (with dev)"
else
    run_command "cd '$PROD_DIR' && composer install --no-dev --optimize-autoloader" "Installing Composer dependencies (production)"
fi

# Step 9: Run migrations
echo -e "${YELLOW}🗄️  Running migrations...${NC}"
if [ "$DRY_RUN" = true ]; then
    echo -e "${BLUE}[DRY RUN] Would execute: curl -s '$MIGRATION_URL'${NC}"
    echo -e "${BLUE}[DRY RUN] Would trigger any pending database migrations${NC}"
else
    echo "Triggering migrations via: $MIGRATION_URL"
    
    # Execute the migration trigger
    if curl -s "$MIGRATION_URL" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Migration trigger executed successfully${NC}"
    else
        echo -e "${RED}❌ Failed to trigger migrations${NC}"
        echo "You may need to manually visit: $MIGRATION_URL"
    fi
    
    echo ""
    echo -e "${YELLOW}📋 Migration verification:${NC}"
    echo "- If there were any database edits, they should now have been executed"
    echo "- Check the database to confirm migrations completed successfully"
    echo "- Verify application functionality"
    
    read -p "Press Enter after verifying migrations are complete..."
fi

# Step 10: Verification
echo -e "${YELLOW}✅ Deployment verification:${NC}"
if [ "$DRY_RUN" = true ]; then
    echo -e "${BLUE}[DRY RUN] Deployment simulation completed successfully!${NC}"
    echo -e "${BLUE}[DRY RUN] No actual changes were made${NC}"
else
    echo "1. Check website functionality"
    echo "2. Verify critical features"
    echo "3. Monitor logs for errors"
    echo ""
    echo -e "${GREEN}Deployment of $NEW_TAG completed!${NC}"
    echo "Backup location: $(cat /tmp/last-backup-location 2>/dev/null || echo 'Not available')"
fi

echo ""
echo "Next steps:"
echo "- Monitor system for 30 minutes"
echo "- Delete Hyper-V snapshot after 72 hours"
echo "- Update documentation if needed"

if [ "$DRY_RUN" = true ]; then
    echo ""
    echo -e "${GREEN}To run for real: $0 (without --dry-run)${NC}"
fi