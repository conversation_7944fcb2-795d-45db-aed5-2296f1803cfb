#!/bin/bash

# Script to backup critical files before deployment, called in step 5 of ./deploy-release.sh
# Author: <PERSON><PERSON> <<EMAIL>>

set -e

# Configuration
BACKUP_BASE_DIR="/var/backups/infocenter-critical"
SOURCE_DIR="/var/www/infocenter"
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
BACKUP_DIR="$BACKUP_BASE_DIR/$TIMESTAMP"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Detect environment
if [ -f "composer.json" ] && [ -d "ICx" ]; then
    # Running from infocenter directory
    SOURCE_DIR="$(pwd)"
    BACKUP_BASE_DIR="/tmp/infocenter-critical-backup"
    BACKUP_DIR="$BACKUP_BASE_DIR/$TIMESTAMP"
elif [ -d "$HOME/webapps/infocenter" ]; then
    # Home directory development environment
    SOURCE_DIR="$HOME/webapps/infocenter"
    BACKUP_BASE_DIR="/tmp/infocenter-critical-backup"
    BACKUP_DIR="$BACKUP_BASE_DIR/$TIMESTAMP"
fi

echo -e "${GREEN}🔄 Creating backup of critical files...${NC}"
echo "Source directory: $SOURCE_DIR"
echo "Backup directory: $BACKUP_DIR"

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Function to backup files safely
backup_files() {
    local pattern="$1"
    local description="$2"
    local found=0
    
    echo -e "${YELLOW}Backing up $description...${NC}"
    
    # Use find to handle patterns safely
    while IFS= read -r -d '' file; do
        if [ -f "$file" ]; then
            cp "$file" "$BACKUP_DIR/" 2>/dev/null || echo "  Warning: Could not backup $file"
            found=1
            echo "  ✅ $(basename "$file")"
        fi
    done < <(find "$SOURCE_DIR" -path "*$pattern" -type f -print0 2>/dev/null)
    
    if [ $found -eq 0 ]; then
        echo "  ℹ️  No files found matching: $pattern"
    fi
}

# Function to backup directories
backup_directory() {
    local source_path="$1"
    local backup_name="$2"
    local description="$3"
    
    echo -e "${YELLOW}Backing up $description...${NC}"
    
    if [ -d "$source_path" ]; then
        mkdir -p "$BACKUP_DIR/$backup_name"
        cp -r "$source_path/"* "$BACKUP_DIR/$backup_name/" 2>/dev/null || echo "  Warning: Could not backup $source_path"
        echo "  ✅ Directory backed up to $backup_name/"
    else
        echo "  ℹ️  Directory not found: $source_path"
    fi
}

# Backup critical files based on assessment with Patrick
backup_files "/ICx/apps/pr/txtblock*.txt" "txtblock files"
backup_files "/ICx/apps/IDB/perm.txt" "IDB permissions"
backup_files "/ICx/apps/maintenance/options/opt*.txt" "maintenance options"

# Backup critical directories
backup_directory "$SOURCE_DIR/ICx/apps/project_db/templates" "templates" "project templates"
backup_directory "$SOURCE_DIR/wiki" "wiki" "wiki content (including images)"

# Backup external modifications (CRITICAL for production)
echo -e "${YELLOW}Backing up external modifications...${NC}"
if [ -d "$SOURCE_DIR/wiki/images" ]; then
    mkdir -p "$BACKUP_DIR/external-wiki"
    cp -r "$SOURCE_DIR/wiki/images" "$BACKUP_DIR/external-wiki/" 2>/dev/null || echo "  Warning: Could not backup wiki images"
    if [ -f "$SOURCE_DIR/wiki/LocalSettings.php" ]; then
        cp "$SOURCE_DIR/wiki/LocalSettings.php" "$BACKUP_DIR/external-wiki/" 2>/dev/null || echo "  Warning: Could not backup LocalSettings.php"
    fi
    echo "  ✅ Wiki external files backed up"
else
    echo "  ℹ️  No wiki images directory found"
fi

# Create backup manifest
echo -e "${YELLOW}Creating backup manifest...${NC}"
cat > "$BACKUP_DIR/MANIFEST.txt" << EOF
Infocenter Critical Files Backup
================================
Timestamp: $TIMESTAMP
Source: $SOURCE_DIR
Backup: $BACKUP_DIR 

Files backed up:
- ICx/apps/pr/txtblock*.txt (business text blocks)
- ICx/apps/IDB/perm.txt (permissions)
- ICx/apps/maintenance/options/opt*.txt (user options)
- ICx/apps/project_db/templates/ (project templates)
- wiki/ (wiki content)

Created by: $(whoami)
Host: $(hostname)
EOF

# Set permissions (fix: set on directory, not individual files)
chmod -R 600 "$BACKUP_DIR" 2>/dev/null || {
    echo -e "${YELLOW}⚠️  Could not set restrictive permissions on backup files${NC}"
    echo "This is normal for /tmp directory - files are still backed up safely"
}

# Save backup location for other scripts
echo "$BACKUP_DIR" > /tmp/last-backup-location

echo -e "${GREEN}✅ Backup completed successfully!${NC}"
echo "Backup location: $BACKUP_DIR"
echo "Manifest: $BACKUP_DIR/MANIFEST.txt"
echo ""
echo -e "${YELLOW}📋 Next steps:${NC}"
echo "- Verify backup contents: ls -la $BACKUP_DIR"
echo "- Check manifest: cat $BACKUP_DIR/MANIFEST.txt"
echo "- Backup location saved to: /tmp/last-backup-location"
