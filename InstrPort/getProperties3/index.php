<?php
include_once 'defines.php';
if (!stristr($_SERVER['SERVER_NAME'], 'info-3') AND !stristr($_SERVER['SERVER_NAME'], 'infocenter')) exit($_SERVER['SERVER_NAME']);
if (strlen($_SERVER['QUERY_STRING'])) {
	$qry = urldecode($_SERVER['QUERY_STRING']);
	if (strlen($qry)) {
		//$qry = preg_replace('/[^0-9a-zA-Z _-\.\/]/','',$qry); // remove any disabled characters
		$qry = preg_replace('/[^0-9a-zA-Z-\.\/ ]/', '', $qry); // remove any disabled characters
		
	}
}
$now = date('d-m-Y H:i:s');
$received = $_SERVER['QUERY_STRING'];
file_put_contents('request.log', "\n" . $now . "\t" . $_SERVER['REMOTE_ADDR'] . ' ' . $_SERVER['REMOTE_HOST'] . "\t" . $received . "\t" . $qry, FILE_APPEND);
if (!strlen($qry)) die('Sorry ' . $_SERVER['REMOTE_ADDR'] . ', your query string *' . $_SERVER['QUERY_STRING'] . '* is not valid.');
// ---- decode query (should be a valid instrumentation and a valid (approved) calibration) ------
// string could be: instrument id; if id is ommited partly, all related instruments will be listed
list($equType, $loc, $equNumber) = explode('-', $qry);
// Checking for Sanity. Instruments should always have a 3 char string at the beginning (e.g. prs for PressureSensors)
$equType = (strlen($equType) == 3) ? $equType : false;
if (!$equType) die('Sorry ' . $_SERVER['REMOTE_ADDR'] . ', your query string *' . $_SERVER['QUERY_STRING'] . '* is not valid.');
$loci = (in_array($loc, array('N', 'A', 'G', 'K', 'B', 'E'))) ? $loc : false; //location identifier:N=Noordoostpolder, A=Amsterdam, G=G�ttingen, K=K�ln, B=Braunschweig, E=not DNW (External)
if (strlen($loc) && !$loci) die('Sorry ' . $_SERVER['REMOTE_ADDR'] . ', your location ID *' . $_SERVER['QUERY_STRING'] . '* is not valid.');
$equNumbers = preg_replace('/[^0-9]/', '', $equNumber);
if (strlen($equNumber) && !strlen($equNumbers)) die('Sorry ' . $_SERVER['REMOTE_ADDR'] . ', your location ID *' . $_SERVER['QUERY_STRING'] . '* is not valid.');
$equID = $equType;
if ($loci) $equID.= '-' . $loci;
if ($equNumbers) $equID.= '-' . $equNumbers;
//$mrange = stristr($_SERVER['SERVER_NAME'],'info-3')?'range':'mrange';
require_once (PATH_ICX . 'IC2_db_con.inc.php');
require_once (PATH_ICX . 'lib/mysql_simple.inc.php');
require_once (PATH_ICX . 'lib/utils.inc.php');
$tbl_instr = 'apps.instrument';
$tbl_suppliers = 'apps.companies';
$properties = array("CONVERT(equipmentNumber USING utf8) as equID", "CONVERT(type USING utf8) as type", "CONVERT(serialNumber USING utf8) as serialNr", "CONVERT(`range` USING utf8) as 'range'", "CONVERT(gain USING utf8) as gain", "CONVERT(filter USING utf8) as filter", "CONVERT(excitation USING utf8) as excitation", "CONVERT(calibrationFrequency USING utf8) as calFreq", "CONVERT(calibrationDate USING utf8) as calibrationDate", "ip as IP", "IF(Company,CONVERT(Company USING utf8),'') as supplier");
$properties = join(', ', $properties);
// get the equipment properties
$qry = "SELECT $properties FROM $tbl_instr LEFT OUTER JOIN $tbl_suppliers ON supplier = Company_ID WHERE equipmentNumber LIKE '$equID%'";
$res = dbs_get_all($qry);
$ret = array('equID' => '', 'type' => '', 'serialNr' => '', 'supplier' => '', 'range' => '', 'calFreq' => '', 'calibrationDate' => '', 'gain' => '', 'filter' => '', 'excitation' => '', 'excitationUnit' => '', 'IP' => '');
$retAll = array();
if ($res[0] !== false && is_array($res[0])) {
	for ($i = 0; $i < count($res); $i++) {
		if (strlen($res[$i]['range']) && $res[$i]['range'] [0] == '{') {
			$res[$i]['range'] = JSON_decode($res[$i]['range']);
			$rows = $res[$i]['range']->rows;
			$keys = array('name', 'min', 'minUnit', 'del', 'max', 'maxUnit', 'remark');
			if ($rows) {
				foreach ($rows as $ch) {
					$ar = array_combine($keys, $ch->data);
					unset($ar['del']);
					$ch->data = $ar;
				}
			}
		}
		if (strlen($res[$i]['gain']) && $res[$i]['gain'] [0] == '{') {
			$res[$i]['gain'] = JSON_decode($res[$i]['gain']);
			$rows = $res[$i]['gain']->rows;
			$keys = array('name', 'gain', 'remark');
			if ($rows) {
				foreach ($rows as $ch) {
					$ar = array_combine($keys, $ch->data);
					$ch->data = $ar;
				}
			}
		}
		if (strlen($res[$i]['filter']) && $res[$i]['filter'] [0] == '{') {
			$res[$i]['filter'] = JSON_decode($res[$i]['filter']);
			$rows = $res[$i]['filter']->rows;
			$keys = array('name', 'filter', 'remark');
			if ($rows) {
				foreach ($rows as $ch) {
					$ar = array_combine($keys, $ch->data);
					$ar['filterUnit'] = preg_replace('/\d/', '', $ar['filter']);
					$ar['filter'] = preg_replace('/\D/', '', $ar['filter']);
					$ch->data = $ar;
				}
			}
		}
		$res[$i]['excitationUnit'] = (strlen($res[$i]['excitation'])) ? preg_replace('/\d/', '', $res[$i]['excitation']) : '';
		$res[$i]['excitation'] = preg_replace('/\D/', '', $res[$i]['excitation']);
		// re-order array
		$dum = $ret;
		foreach ($res[$i] as $k => $v) {
			$dum[$k] = $v;
		}
		$retAll[] = $dum;
	}
	if (count($retAll)) {
		die(JSON_encode($retAll));
	}
} else {
	if (strlen($equID)) $calID = "*$equID*";
	die("Equipment $calID not available.");
}
