<?php

include_once 'defines.php';
if (strlen($_SERVER['QUERY_STRING'])) {
	$qry = urldecode($_SERVER['QUERY_STRING']);
	if (strlen($qry)) {
		//$qry = preg_replace('/[^0-9a-zA-Z _-\.\/]/','',$qry); // remove any disabled characters
		$qry = preg_replace('/[^0-9a-zA-Z-\.\/ ]/', '', $qry); // remove any disabled characters
		
	}
}
$now = date('d-m-Y H:i:s');
$received = $_SERVER['QUERY_STRING'];
function logRequest($calID = '')
{
	global $now, $received, $qry;
	file_put_contents('request.log', "\n" . $now . "\t" . $_SERVER['REMOTE_ADDR'] . ' ' . $_SERVER['REMOTE_HOST'] . "\t" . $received . "\t" . $qry . ($calID ? "\tcal-id:$calID" : ''), FILE_APPEND);
}
if (!strlen($qry)) {
	logRequest();
	if (!strlen($qry)) die('Sorry ' . $_SERVER['REMOTE_ADDR'] . ', your query string *' . $_SERVER['QUERY_STRING'] . '* is not valid.');
}
// ---- decode query (should be a valid instrumentation and a valid (approved) calibration) ------
// string could be: instrument id / specific calibration; if specific calibration is ommited, the most recent approved calibration will be selected
$qry_ar = explode('/', $qry);
$equID = array_shift($qry_ar);
$calID = join('/', $qry_ar);
if ($equID != 'PRS-E-1234') logRequest($calID); // don't log GAIUS check-requests
if (!strlen(str_replace(' ', '', $equID))) die('Equipment ID not valid.');

require_once (PATH_ICX . 'IC2_db_con.inc.php');
require_once (PATH_ICX . 'lib/mysql_simple.inc.php');
require_once (PATH_ICX . 'lib/utils.inc.php');
$tbl_instr = 'apps.instrument';
$tbl_cal = 'apps.instrument_calibration';
// Check for requested instrument
list($refId) = dbs_get("SELECT id FROM $tbl_instr WHERE equipmentNumber = '$equID'");
if (!$refId) die('Equipment ID not found.');
// get the requested calibration
$select = "SELECT cal_hrn,DATE(cal_date) as date,cal_report FROM $tbl_cal";
// if only instrument id received, send last approved calibration data else the specific calibration
if (!strlen(str_replace(' ', '', $calID))) $qry = "$select WHERE cal_fk_id='$refId' AND cal_appr ORDER BY cal_date DESC";
else $qry = "$select WHERE cal_fk_id='$refId' AND cal_hrn='$calID'";
$res = dbs_get_assoc($qry);
//if($_SERVER['REMOTE_ADDR']=='**************') echo '<pre>count:',count($res),"\n",print_r($res,1),"</pre><br>";
if (count($res) > 1) {
	// map
	$map_res['equipmentId'] = utf8_encode($equID);
	$map_res['calibrationName'] = utf8_encode($res['cal_hrn']);
	$map_res['date'] = $res['date'];
	$report = $res['cal_report'];
	if ($report[0] == '<') $map_res['rawData'] = htmlentities($res['cal_report'], ENT_COMPAT, 'UTF-8');
	elseif ($report[0] == '{') {
		$report = json_decode($report);
		$map_res['rawData'] = $report;
	}
	//print_r($qry);
	header("Content-type: application/json");
	die(JSON_encode($map_res, JSON_UNESCAPED_SLASHES));
} else {
	if (strlen($calID)) $calID = "*$calID*";
	die("Calibration $calID not available.");
}
