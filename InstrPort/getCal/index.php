<?php
include_once 'defines.php';
if (strlen($_SERVER['QUERY_STRING'])) {
	$qry = urldecode($_SERVER['QUERY_STRING']);
	if (strlen($qry)) {
		$qry = preg_replace('/[^0-9a-zA-Z-\.\/ ]/', '', $qry); // remove any disabled characters
		
	}
}
$now = date('d-m-Y H:i:s');
$received = $_SERVER['QUERY_STRING'];
if (!strlen($qry)) {
	logRequest();
	die('Sorry ' . $_SERVER['REMOTE_ADDR'] . ', your query string *' . $_SERVER['QUERY_STRING'] . '* is not valid.');
}
// ---- decode query (should be a valid instrumentation and a valid (approved) calibration) ------
// string could be: instrument id / specific calibration; if specific calibration is ommited, the most recent approved calibration will be selected
$qry_ar = explode('/', $qry);
$equID = array_shift($qry_ar);
//$calID = join('/',$qry_ar);
$calID = @array_shift($qry_ar);
if (strlen($calID) && strlen(str_replace(' ', '', $calID))) {
} else $calID = false;
$inv = ($qry_ar[0] && $qry_ar[0] == 'inv') ? true : false;
if ($equID != 'PRS-E-1234') logRequest($calID); // don't log GAIUS check requests
if (!strlen(str_replace(' ', '', $equID))) die('Equipment ID not valid.');

require_once (PATH_ICX . 'IC2_db_con.inc.php');
require_once (PATH_ICX . 'lib/mysql_simple.inc.php');
require_once (PATH_ICX . 'lib/utils.inc.php');
$tbl_instr = 'apps.instrument';
$tbl_cal = 'apps.instrument_calibration';
$tbl_cal_inv = 'apps.instrument_calibration_inv';
// Check for requested instrument
list($refId) = dbs_get("SELECT id FROM $tbl_instr WHERE equipmentNumber = '$equID'");
if (!$refId) die('Equipment ID not found.');
// get the requested calibration
$select = "SELECT cal_id, cal_hrn,DATE(cal_date) as date,cal_uncertainty,cal_inp_unit,cal_out_unit,cal_coeff,cal_formula, cal_range, cal_remark, cal_appr, cal_fk_application_id FROM $tbl_cal";
if ($calID === false) {
	$qry = "$select WHERE cal_fk_id='$refId' AND cal_appr ORDER BY cal_date DESC";
	$res = dbs_get_assoc($qry);
} elseif ($calID == 'all') {
	$inv = false;
	$qry = "$select WHERE cal_fk_id='$refId'";
	$res = dbs_get_all($qry);
} else {
	$qry = "$select WHERE cal_fk_id='$refId' AND cal_hrn='$calID'";
	$res = dbs_get_assoc($qry);
}
//if($_SERVER['REMOTE_ADDR']=='**************') echo '<pre>count:',count($res),"\n",print_r($res,1),"</pre><br>";
if (count($res) >= 1) {
	if ($inv && $calID != 'all') {
		$id = $res['cal_id'];
		$qry = "SELECT cal_inp_unit,cal_out_unit,cal_coeff,cal_formula,cal_out_vec FROM $tbl_cal_inv WHERE cal_fk_cal_id='$id'";
		$res_inv = dbs_get($qry);
	} else $res_inv = false;
	if ($calID == 'all') {
		foreach ($res as $row) {
			$map_res[] = map($row);
		}
	} else {
		$map_res = map($res, $res_inv);
	}
	header("Content-type: application/json");
	die(JSON_encode($map_res));
} else {
	if (strlen($calID)) $calID = "*$calID*";
	die("Calibration $calID not available.");
}
function map($res, $res_inv = false)
{
	global $equID, $inv;
	$inp_unit_inv = $out_unit_inv = $coeff_inv = $formula_inv = $out_vec = 'not available';
	$coeff_inv = '';
	if (is_array($res_inv)) list($inp_unit_inv, $out_unit_inv, $coeff_inv, $formula_inv, $out_vec) = $res_inv;
	// map
	$map_res['equID'] = utf8_encode($equID);
	$map_res['cal_name'] = utf8_encode($res['cal_hrn']);
	$map_res['cal_date'] = $res['date'];
	$map_res['equ_type'] = $inv ? 'L2R' : 'R2L';
	$map_res['equation_input_unit'] = $inv ? checkArray($inp_unit_inv) : checkArray($res['cal_inp_unit']);
	$map_res['equation_output_unit'] = $inv ? checkArray($out_unit_inv) : checkArray($res['cal_out_unit']);
	// check for json notation
	if ($res['cal_range'] [0] == '{' && ($ar = json_decode($res['cal_range']))) {
		$rows = $ar->rows;
		$keys = array('name', 'min', 'minUnit', 'del', 'max', 'maxUnit', 'remark');
		if ($rows) {
			foreach ($rows as $ch) {
				$ac = array_combine($keys, $ch->data);
				unset($ac['del']);
				$ch->data = $ac;
			}
		}
		$res['cal_range'] = $ar;
	}
	$map_res['cal_range'] = $res['cal_range'];
	// check for json notation
	if ($res['cal_uncertainty'] [0] == '{' && ($ar = json_decode($res['cal_uncertainty']))) {
		$rows = $ar->rows;
		$keys = array('name', 'uncertaintyFs', 'del', 'uncertaintyAbs', 'remark');
		if ($rows) {
			foreach ($rows as $ch) {
				$ac = array_combine($keys, $ch->data);
				$ch->data = $ac;
			}
		}
		$res['cal_uncertainty'] = $ar;
	}
	$map_res['uncertainty'] = $res['cal_uncertainty'];
	$map_res['equation'] = $inv ? checkArray($formula_inv) : checkArray($res['cal_formula']);
	//print_r($coeff_inv);
	$map_res['coefficients'] = $inv ? checkMatrix($coeff_inv) : checkMatrix($res['cal_coeff']);
	$qry = "SELECT cal_inp_unit,cal_out_unit,cal_coeff,cal_formula,cal_out_vec FROM apps.instrument_calibration_inv WHERE cal_fk_cal_id='".$res['cal_id']."'";
	$res_inv = dbs_get($qry);
	if(isset($res_inv) && is_array($res_inv) && $res_inv[0] != false) {
		$map_res['equation_inv'] = $res_inv['cal_formula'];
		$map_res['coefficients_inv'] = checkMatrix($res_inv['cal_coeff']);
	}
	$map_res['approved'] = $res['cal_appr'] ? $res['cal_appr'] : '';
	$map_res['remark'] = $res['cal_remark'] ? $res['cal_remark'] : '';
	return $map_res;
}
function checkArray($a)
{
	if ($ar = preg_grep('/\[.+?\]/', array($a))) {
		$ar = str_replace(array('[', ']', '"', ' '), '', $ar[0]);
		$a = explode(',', $ar);
	}
	return $a;
}
function checkMatrix($M)
{
	$ret = array();
	$M = preg_replace('/<.+?>/', '', $M);
	$M = str_replace(array("\n", ' '), '', $M);
	if (!strlen($M)) return 'not available';
	$M = explode('],[', $M);
	foreach ($M as $k => $ar) {
		$ret[$k] = checkArray("[$ar]");
	}
	foreach ($ret as $m => $row) {
		foreach ($row as $n => $v) $ret[$m][$n] = (float)$v;
	}
	return $ret;
}

function logRequest($calID = '')
{
	global $now, $received, $qry;
	file_put_contents('request.log', "\n" . $now . "\t" . $_SERVER['REMOTE_ADDR'] . ' ' . $_SERVER['REMOTE_HOST'] . "\t" . $received . "\t" . $qry . ($calID ? "\tcal-id:$calID" : ''), FILE_APPEND);
}
