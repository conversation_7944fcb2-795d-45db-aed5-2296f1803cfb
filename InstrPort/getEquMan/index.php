<?php
include_once 'defines.php';
if (!stristr($_SERVER['SERVER_NAME'], 'info-3') AND !stristr($_SERVER['SERVER_NAME'], 'infocenter')) exit($_SERVER['SERVER_NAME']);
if (strlen($_SERVER['QUERY_STRING'])) {
	$qry = urldecode($_SERVER['QUERY_STRING']);
	if (strlen($qry)) {
		//$qry = preg_replace('/[^0-9a-zA-Z _-\.\/]/','',$qry); // remove any disabled characters
		$qry = preg_replace('/[^0-9a-zA-Z-\.\/ ]/', '', $qry); // remove any disabled characters
		
	}
}
$now = date('d-m-Y H:i:s');
$received = $_SERVER['QUERY_STRING'];
file_put_contents('request.log', "\n" . $now . "\t" . $_SERVER['REMOTE_ADDR'] . ' ' . $_SERVER['REMOTE_HOST'] . "\t" . $received . "\t" . $qry, FILE_APPEND);
if (!strlen($qry)) die('Sorry ' . $_SERVER['REMOTE_ADDR'] . ', your query string *' . $_SERVER['QUERY_STRING'] . '* is not valid.');
// ---- decode query (should be a valid instrumentation and a valid (approved) calibration) ------
// string could be: instrument id; if id is ommited partly, all related instruments will be listed
list($equType, $loc, $equNumber) = explode('-', $qry);
// sanitize
$mySites = array('A', // Amsterdam
'B', // Braunschweig
'G', // G�ttingen
'K', // K�ln
'N'); // NOP (Marknesse)
$equType = (in_array($equType, array('CAL', // 	Calibration Devices
'PRM', // 	Pressure Modules
'PRS', // 	Pressure Sensors
'ICL', // 	Inclinometers
'TEM', // 	Temperature Module
'TES', // 	Temperature Sensor
'BAL', // 	Balances
'DAQ', // 	Data Acquisition Devices
'MIC', // 	Microphones
'ACC', // 	Accelerometers
'HUM', // 	Humidity sensors
'TPS', // 	TPS
'ALB', // 	Airline bridge
'TSE', // 	Test Sections
'VNT', // 	Venturi
'GEN'
//   General Equipment
))) ? $equType : false;
if (!$equType) die('Sorry ' . $_SERVER['REMOTE_ADDR'] . ', your query string *' . $_SERVER['QUERY_STRING'] . '* is not valid.');
$loci = (in_array($loc, $mySites)) ? $loc : false;
if (!$loci) die('Sorry ' . $_SERVER['REMOTE_ADDR'] . ', your location ID in *' . $_SERVER['QUERY_STRING'] . '* is missing or not valid.');
$equNumbers = preg_replace('/[^0-9]/', '', $equNumber);
if (strlen($equNumber) && !strlen($equNumbers)) die('Sorry ' . $_SERVER['REMOTE_ADDR'] . ', your location ID *' . $_SERVER['QUERY_STRING'] . '* is not valid.');
$equID = $equType;
if ($loci) $equID.= '-' . $loci;
//if($equNumbers) $equID .= '-'.$equNumbers;
// array position of $loci in $mySites
$loc = array_search($loci, $mySites);
$mrange = stristr($_SERVER['SERVER_NAME'], 'info-3') ? 'range' : 'mrange';
require_once (PATH_ICX . 'IC2_db_con.inc.php');
require_once (PATH_ICX . 'lib/mysql_simple.inc.php');
require_once (PATH_ICX . 'lib/utils.inc.php');
$tbl_instr = 'apps.instrument';
$properties = array("CONVERT(equipmentNumber USING utf8) as equipmentID", "CONVERT(typeID USING utf8) as typeID");
$properties = join(', ', $properties);
// get the equipment properties
$qry = "SELECT $properties FROM $tbl_instr WHERE equipmentNumber LIKE '$equID%'";
//die($qry);
$res = dbs_get_all($qry);
// scan result set for typeIDs
$typeID = array();
foreach ($res as $row) {
	if (!in_array($row['typeID'], $typeID)) $typeID[] = $row['typeID'];
}
// now we need the map from $type ID to tab number used in the Instrumentation Portal
$tab = getTab($typeID);
// get permission file
$pf = PATH_ICX . 'apps/IDB/perm.txt';
if (is_file($pf)) {
	$p = file_get_contents($pf);
} else die('Error: Permissions file not found.');
$p = preg_replace("/([a-zA-Z0-9_]+?):/", "\"$1\":", $p); // set property name in quotes
$p = JSON_decode(utf8_encode($p), true);
$p = $p["$loc"][$tab]['admName'] ? $p["$loc"][$tab]['admName'] : '?';
die($p);
function getTab($typeID = false)
{
	// currently only one tab will be supported
	$typeID = $typeID[0];
	$res = '';
	require_once (PATH_ICX . 'apps/IDB/GUIcfg.inc.php');
	// data are available in array $conf
	foreach ($conf['tab'] as $k => $v) {
		if ($v['filter'] == $typeID) {
			$res = preg_replace('/[^0-9]/', '', $k);
			break;
		};
	}
	return $res;
}
