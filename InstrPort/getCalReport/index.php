<?php

include_once 'defines.php';
if (strlen($_SERVER['QUERY_STRING'])) {
	$qry = urldecode($_SERVER['QUERY_STRING']);
	if (strlen($qry)) {
		//$qry = preg_replace('/[^0-9a-zA-Z _-\.\/]/','',$qry); // remove any disabled characters
		$qry = preg_replace('/[^0-9a-zA-Z-\.\/ ]/', '', $qry); // remove any disabled characters
		
	}
}
$now = date('d-m-Y H:i:s');
$received = $_SERVER['QUERY_STRING'];
file_put_contents('request.log', "\n" . $now . "\t" . $_SERVER['REMOTE_ADDR'] . ' ' . $_SERVER['REMOTE_HOST'] . "\t" . $received . "\t" . $qry, FILE_APPEND);
if (!strlen($qry)) die('Sorry ' . $_SERVER['REMOTE_ADDR'] . ', your query string *' . $_SERVER['QUERY_STRING'] . '* is not valid.');
// ---- decode query (should be a valid instrumentation and a valid (approved) calibration) ------
// string could be: instrument id / specific calibration; if specific calibration is ommited, the most recent approved calibration will be selected
$qry_ar = explode('/', $qry);
$equID = array_shift($qry_ar);
$calID = array_shift($qry_ar);
if (count($qry_ar)) {
	$pdf = strtolower(array_shift($qry_ar));
	if ($pdf == 'pdf') $pdf = true;
	else $pdf = false;
}
if (!strlen(str_replace(' ', '', $equID))) die('<h2 style="color:red">Error: Equipment ID not valid.');

require_once (PATH_ICX . 'IC2_db_con.inc.php');
require_once (PATH_ICX . 'lib/mysql_simple.inc.php');
require_once (PATH_ICX . 'lib/utils.inc.php');
$tbl_instr = 'apps.instrument';
$tbl_cal = 'apps.instrument_calibration';
// Check for requested instrument
list($refId) = dbs_get("SELECT id FROM $tbl_instr WHERE equipmentNumber = '$equID'");
if (!$refId) die('<h2 style="color:red">Error: Equipment ID not found.');
// get the id of the requested calibration
list($calId) = dbs_get("SELECT cal_id,cal_report FROM $tbl_cal WHERE cal_fk_id = '$refId' AND cal_hrn='$calID'");
// get the calibration report with the id $calId
require_once (PATH_ICX . 'apps/IDB/CalReport/report.inc.php');
