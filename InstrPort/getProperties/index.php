<?php
include_once 'defines.php';
include_once "DNW_Instrumentation.php";

$ins = new \DNW\DNW_Instrumentation();

//This file is the export file used by PTC to get the data it needs
define('PATH_IC', '../../ICx/');
if (!stristr($_SERVER['SERVER_NAME'], 'clown') AND !stristr($_SERVER['SERVER_NAME'], 'infocenter')) exit($_SERVER['SERVER_NAME']);
if (strlen($_SERVER['QUERY_STRING'])) {
	$qry = urldecode($_SERVER['QUERY_STRING']);
	if (strlen($qry)) {
		//$qry = preg_replace('/[^0-9a-zA-Z _-\.\/]/','',$qry); // remove any disabled characters
		$qry = preg_replace('/[^0-9a-zA-Z-\.\/ ]/', '', $qry); // remove any disabled characters
		
	}
}
$now = date('d-m-Y H:i:s');
$received = $_SERVER['QUERY_STRING'];
file_put_contents('request.log', "\n" . $now . "\t" . $_SERVER['REMOTE_ADDR'] . ' ' . $_SERVER['REMOTE_HOST'] . "\t" . $received . "\t" . $qry, FILE_APPEND);
if (!strlen($qry)) die('Sorry ' . $_SERVER['REMOTE_ADDR'] . ', your query string *' . $_SERVER['QUERY_STRING'] . '* is not valid.');
// ---- decode query (should be a valid instrumentation and a valid (approved) calibration) ------
// string could be: instrument id; if id is ommited partly, all related instruments will be listed
list($equType, $loc, $equNumber) = explode('-', $qry);
// Checking for Sanity. Instruments should always have a 3 char string at the beginning (e.g. prs for PressureSensors)
$equType = (strlen($equType) == 3) ? $equType : false;
if (!$equType) die('Sorry ' . $_SERVER['REMOTE_ADDR'] . ', your query string *' . $_SERVER['QUERY_STRING'] . '* is not valid.');
$loci = (in_array($loc, array('N', 'A', 'G', 'K', 'B', 'E'))) ? $loc : false; //location identifier:N=Noordoostpolder, A=Amsterdam, G=G�ttingen, K=K�ln, B=Braunschweig, E=not DNW (External)
if (strlen($loc) && !$loci) die('Sorry ' . $_SERVER['REMOTE_ADDR'] . ', your location ID *' . $_SERVER['QUERY_STRING'] . '* is not valid.');
$equNumbers = preg_replace('/[^0-9]/', '', $equNumber);
if (strlen($equNumber) && !strlen($equNumbers)) die('Sorry ' . $_SERVER['REMOTE_ADDR'] . ', your location ID *' . $_SERVER['QUERY_STRING'] . '* is not valid.');
$equID = $equType;
if ($loci) $equID.= '-' . $loci;
if ($equNumbers) $equID.= '-' . $equNumbers;
//$mrange = stristr($_SERVER['SERVER_NAME'],'info-3')?'range':'mrange';
require_once (PATH_ICx . 'IC2_db_con.inc.php');
require_once (PATH_ICx . 'lib/mysql_simple.inc.php');
require_once (PATH_ICx . 'lib/utils.inc.php');
$tbl_instr = 'apps.instrument';
$tbl_suppliers = 'apps.companies';
$properties = array(
	"CONVERT(equipmentNumber USING utf8) as equID",
	"CONVERT(type USING utf8) as type",
	"CONVERT(serialNumber USING utf8) as serialNr",
	"CONVERT(`range` USING utf8) as 'range2'",
	"CONVERT(accuracy USING utf8) as accuracy",
	"CONVERT(gain USING utf8) as gain",
	"CONVERT(filter USING utf8) as filter",
	"CONVERT(excitation USING utf8) as excitation",
	"CONVERT(calibrationFrequency USING utf8) as calFreq",
	"CONVERT(calibrationDate USING utf8) as calibrationDate",
	"ip as IP",
	"IF(Company,CONVERT(Company USING utf8),'') as supplier",
	"phys_status as phys_status",
	"cal_status as cal_status",
	"Company as supplier",
    "phys_units",
    "chan_properties"
);
$properties = join(', ', $properties);
// get the equipment properties
$qry = "SELECT id FROM apps.instrument WHERE equipmentNumber LIKE '$equID%' AND status < 9"; //don't list removed instruments
$res = dbs_get_all($qry);
header("Content-type: application/json");
foreach($res as $r)
    $instruments[] = $ins->exportToPTC($r['id']);
exit(json_encode($instruments));
//$ret = array('equID' => '','type' => '', 'serialNr'=>'', 'supplier'=>'', 'range'=>'', 'accuracy'=>'', 'calFreq'=>'', 'calibrationDate'=>'', 'gain'=>'', 'filter'=>'', 'excitation'=>'', 'excitationUnit'=>'', 'IP'=>'', 'phys_status'=>'', 'cal_status'=>'');
//$retAll = array();
//$statusMenu = array('ok','no valid cal.','in use', 'in use, no valid cal.','','','under calibration','out of cal./specs.','defect','removed');
//
//$PhysStatusMenu = array("", 'In use', 'Defect do not use', 'Removed', 'Under calibration', 'Ok');
//$CalStatusMenu = array("", 'Calibrated', 'Calibrate before use', 'Check calibration before use', 'No calibration required', 'Use only for indication', 'Defect do not use', "No valid calibration");
//if ($res[0] !== false && is_array($res[0])) {
//	for ($i = 0; $i < count($res); $i++) {
//	    if(strlen($res[$i]['phys_units']) && $res[$i]['phys_units'][0] == '{'){
//	        $res[$i]['phys_units'] = JSON_decode($res[$i]['phys_units']);
//	        $rows = $res[$i]['phys_units']->rows;
//	        $keys = array('min', 'max', 'unit', 'uncert. perc.', 'uncert. Quant', 'acc. min', 'acc. max', 'remark');
//	        if($rows){
//	            foreach($rows as $ch){
//	                $ar = array_combine($keys, $ch->data);
//	                $ar['minUnit'] = $ar['maxUnit'] = $ar['unit'];
//	                $ar['name'] = $ch->id;
//	                unset($ar['unit']);
//	                $ch->data = $ar;
//                }
//            }
//            $res[$i]['range'] = $res[$i]['phys_units']->rows;
//	        unset($res[$i]['phys_units']);
//        }
//        if(strlen($res[$i]['chan_properties']) && $res[$i]['chan_properties'][0] == '{'){
//            $res[$i]['chan_properties'] = JSON_decode($res[$i]['chan_properties']);
//            $rows = $res[$i]['chan_properties']->rows;
//            $keys = array('min', 'max', 'unit', 'uncert. perc.', 'uncert. Quant', 'acc. min', 'acc. max', 'remark');
//            if($rows){
//                foreach($rows as $ch){
//                    $ar = array_combine($keys, $ch->data);
//                    $ar['minUnit'] = $ar['maxUnit'] = $ar['unit'];
//                    $ar['name'] = $ch->id;
//                    unset($ar['unit']);
//                    $ch->data = $ar;
//                }
//            }
//            $res[$i]['range'] = $res[$i]['chan_properties']->rows;
//            unset($res[$i]['phys_units']);
//        }
////	    if(strlen($res[$i]['range']) && $res[$i]['range']{0}=='{'){
////			$res[$i]['range'] = JSON_decode($res[$i]['range']);
////			$rows = $res[$i]['range']->rows;
////			$keys = array('name', 'min', 'minUnit', 'del', 'max', 'maxUnit', 'remark');
////			if ($rows) {
////				foreach ($rows as $ch) {
////					$ar = array_combine($keys, $ch->data);
////					unset($ar['del']);
////					$ch->data = $ar;
////				}
////			}
////		}
//		if(strlen($res[$i]['accuracy']) && $res[$i]['accuracy']{0}=='{'){
//			$res[$i]['accuracy'] = JSON_decode($res[$i]['accuracy']);
//			$rows = $res[$i]['accuracy']->rows;
//			$keys = array('name', 'accuracyFs', 'del', 'accuracyAbs', 'remark', 'accuracyAbsUnit',);
//			if ($rows) {
//				foreach ($rows as $ch) {
//					$ch->data[1] = preg_replace('/[^0-9\.]/', '', $ch->data[1]);
//					$dum = $ch->data[3];
//					$ch->data[3] = preg_replace('/[^0-9\.]/', '', $dum);
//					$ch->data[5] = preg_replace('/[0-9\.]/', '', $dum);
//					$ar = array_combine($keys, $ch->data);
//					$ch->data = $ar;
//				}
//			}
//		}
//		if(strlen($res[$i]['gain']) && $res[$i]['gain']{0}=='{'){
//			$res[$i]['gain'] = JSON_decode($res[$i]['gain']);
//			$rows = $res[$i]['gain']->rows;
//			$keys = array('name', 'gain', 'remark');
//			if ($rows) {
//				foreach ($rows as $ch) {
//					$ar = array_combine($keys, $ch->data);
//					$ch->data = $ar;
//				}
//			}
//		}
//		if(strlen($res[$i]['filter']) && $res[$i]['filter']{0}=='{'){
//			$res[$i]['filter'] = JSON_decode($res[$i]['filter']);
//			$rows = $res[$i]['filter']->rows;
//			$keys = array('name', 'filter', 'remark');
//			if ($rows) {
//				foreach ($rows as $ch) {
//					$ar = array_combine($keys, $ch->data);
//					$ar['filterUnit'] = preg_replace('/\d/', '', $ar['filter']);
//					$ar['filter'] = preg_replace('/\D/', '', $ar['filter']);
//					$ch->data = $ar;
//				}
//			}
//		}
//		$res[$i]['excitationUnit'] = (strlen($res[$i]['excitation'])) ? preg_replace('/\d/', '', $res[$i]['excitation']) : '';
//		$res[$i]['excitation'] = preg_replace('/\D/', '', $res[$i]['excitation']);
//		$res[$i]['phys_status']= $PhysStatusMenu[$res[$i]['phys_status']];
//		$res[$i]['cal_status']= $CalStatusMenu[$res[$i]['cal_status']];
//		// re-order array
//		$dum = $ret;
//		foreach($res[$i] as $k=>$v){$dum[$k] = $v;}
//		$retAll[] = $dum;
//	}
//	if (count($retAll)) {
//		header("Content-type: application/json");
//		die(JSON_encode($retAll));
//	}
//} else {
//	if (strlen($equID)) $calID = "*$equID*";
//	die("Equipment $calID not available.");
//}
