<?php
define('PATH_IC', './');
define('URL_IC', './');
require 'IC2auth.inc.php'; // sets user id ($uid) and permission (rights), defines $script
$uid == 1 ? $admin = true : '';
switch ($rights) {
case 0: // no rights at all
	echo '<h3>Access denied!</h3>';
	exit;
case 1: // path view rights
	echo '<h3>Access denied!</h3>';
	exit;
case 2: // read rights on object
	
case 3: // write rights
	
case 4: // workflow edit rights
	
case 5: // edit rights
	
case 6: // owner rights
	
case 7: // administrator group
	
case 8: // root user
	$opts['options'] = 'LF';
	break;

default: // no rights at all
	echo '<h3>Access denied!</h3>';
	exit;
}

require_once (PATH_IC . 'lib/mysql_simple.inc.php');

if (strlen($_GET['getUsers'])) { // ajax request for user list
	$uid = preg_replace('/[^\d,]/', '', $_GET['getUsers']);
	$users = dbs_get_all("SELECT user_id, user_lname, user_fname_s FROM infocenter.info_user WHERE user_id IN($uid) ORDER BY user_lname");
	foreach ($users as $user) {
		$ret[] = $user['user_id'];
		$ret[] = $user['user_lname'];
		$ret[] = $user['user_fname_s'];
	}
	exit("o=['" . join("','", $ret) . "']");
}
if (is_array($_SESSION['user_groups'])) $group_ids = implode(',', $_SESSION['user_groups']);

$tbl_objects = $_SESSION['DB_IC'] . '.info_object';
$tbl_folders = $_SESSION['DB_IC'] . '.info_object_folder';
$ids = $obj_ids = array();
function stack($action = '', $id = 0, $name = '', $r = 0, $script = '')
{
	if (!is_array($_SESSION[$script . '_stack']['id'])) $_SESSION[$script . '_stack']['id'] = array();
	if (!is_array($_SESSION[$script . '_stack']['name'])) $_SESSION[$script . '_stack']['name'] = array();
	switch ($action) {
	case 'push':
		if ($_SESSION[$script . '_stack']['id'][0][0] != $id) {
			array_unshift($_SESSION[$script . '_stack']['id'], array($id, $r));
			array_push($_SESSION[$script . '_stack']['name'], $name);
		}
		return '/' . join('/', $_SESSION[$script . '_stack']['name']);
		break;

	case 'pop':
		if (is_array($_SESSION[$script . '_stack'])) {
			array_shift($_SESSION[$script . '_stack']['id']);
			array_pop($_SESSION[$script . '_stack']['name']);
			return !empty($_SESSION[$script . '_stack']['id'][0]) ? array($_SESSION[$script . '_stack']['id'][0][0], $_SESSION[$script . '_stack']['id'][0][1], '/' . join('/', $_SESSION[$script . '_stack']['name'])) : false;
		} else return false;
		break;

	case 'clear':
		unset($_SESSION[$script . '_stack']);
		return true;
		break;

	default:
		return false;
	}
}
// ----- get all child node ids
function readNodes($base)
{
	global $ids, $tbl_objects;
	$qry = "SELECT object_id FROM $tbl_objects WHERE object_parent_id = '$base'";
	($res = dbs_get_all($qry)) or die($qry . 'mysql_error(in linked_objects_list)');
	foreach ($res as $row){
		list($child) = $row;
		$ids[] = $child;
		readNodes($child);
	}
}
// get object name & object id of calling script
list($requester, $fk_oid, $obj_hrn) = $_SESSION['get_link'];
$del = 'off';
if (!empty($_REQUEST['show'])) { //request from database to show attached objects
	if ($fk_oid == 5608) { // if called by project database, get project & test number
		$_SESSION[$script]['blog_subt'] = $_GET['PN'] . ' (TN' . (int)$_GET['TN'] . ')';
	}
	$_SESSION[$requester . 'L_rec'] = (int)$_REQUEST['show'];
	stack('clear', '', '', '', $script);
}
$fk_key_id = $_SESSION[$requester . 'L_rec'];
if (isset($_REQUEST['nm'])) $_SESSION[$requester . 'rec_nm'] = $_REQUEST['nm'];
$fk_rec_name = $_SESSION[$requester . 'rec_nm'];
if (empty($fk_key_id) || empty($fk_oid) || empty($fk_rec_name)) {
	$lang = $_SESSION['language'];
	if (empty($lang)) {
		$lang = $_SERVER['HTTP_ACCEPT_LANGUAGE'];
		$lang = strtolower(substr($lang, 0, 2));
	}
	$noRefo = array('en' => 'Link ID inconsistent!', 'de' => 'Link-ID nicht konsistent!', 'nl' => 'Link-ID niet voldoende!');
	$noRefk = array('en' => 'No record selected!', 'de' => 'Kein Record gew�hlt!', 'nl' => 'Geen record geselecteerd');
	if (empty($fk_key_id)) exit('<h4><font color="red">Error: ' . $noRefk[$lang] . '</font></h4>');
	else exit('<h4><font color="red">Error: ' . $noRefo[$lang] . '</font></h4>');
}
$rights = $_SESSION['my_or_' . $requester];
$uid = $_SESSION['user_id'];
$_SESSION['my_or_s.php'] = $rights;
$_SESSION['my_or_get_file.php'] = $rights;
$_SESSION['my_or_show_pf.php'] = $rights;
//if(47==$_SESSION['user_id']) exit("<script>alert('$requester $rights')</script>");
// opens a linked folder and subsequent folders
if (isset($_REQUEST['xflo'])) {
	$xfl = (int)$_REQUEST['xflo'];
	$path = stack('push', $xfl, $_REQUEST['name'], (int)$_REQUEST['r'], $script);
}
// steps one folder level higher; if the link level is arrived $xfl becomes 'false' or 'empty'
if (isset($_REQUEST['upb'])) list($xfl, $ur, $path) = stack('pop', '', '', '', $script);
// on start-up no name is defined, thus:
if ($name == '') $name = "object_database.gif||" . $fk_rec_name . $path;
if ($_REQUEST['del'] == 'on') {
	$del = 'on';
}
$ur = $ur ? $ur : (int)$_REQUEST['r'];
// format the record buttons and delete entry if requested
//if($del AND empty($xfl) AND $fk_key_id>0) {
if (empty($xfl) && $del == 'on') {
	$colattrs = 'align="right" width="37px"';
	$concat = 'CONCAT("<a href=\"' . $script . '?del=on&fl=",lo_id,"\"><img src=\"img/unlink.gif\" border=\"0px\" alt=\"unlink document\" title=\"unlink object\"/></a>&nbsp;<img src=\"img/",object_type_icon,"\"/>")';
	// del entry if requested
	if (($fl = (int)$_REQUEST['fl']) > 0 AND $fk_key_id > 0) {
		$sql = 'DELETE FROM apps.linked_objects WHERE ((lo_id = ' . $fl . ') AND (lo_fk_ref_oid = ' . $fk_oid . ') AND (lo_fk_ref_key_id = ' . $fk_key_id . ')) LIMIT 1';
		$result = mysql_query($sql) or die('could not unlink the object! ' . mysql_error());
	}
} else {
	$colattrs = 'style="text-align:right;width:25pt;cursor:pointer" onClick="return Ocn(this);"';
	$concat = 'IF(xlink,CONCAT("<img src=\'img/xlink.png\' style=\'width:7px;height:7px;border:0px;position:relative;left:6px\'><img src=\"img/",object_type_icon,"\"/>"),CONCAT("<img src=\"img/",object_type_icon,"\"/>"))';
}
$opts['cgi']['persist']['xflo'] = $xfl;
if (0 && $uid == 47) {
	//  echo (int)$_REQUEST['show'];
	//  echo $_SESSION[$requester.'L_rec'];
	//echo $concat;
	// Debug query
	$debug_query = true;
	// Debug post variables
	//$debug_posts = true;
	// Debug get variables
	//  $debug_gets = true;
	
}
// MySQL host name, user name, password, database, and table
$opts['db'] = 'infocenter';
$opts['tb'] = 'info_object';
// current date
$today = date('Y-m-d');
// Name of field which is the unique key
$opts['key'] = 'object_id';
// Type of key field (int/real/string/date etc.)
$opts['key_type'] = 'int';
// Sorting field(s)
$opts['sort_field'] = array('-object_last_upd');
$opts['filter_btn'] = 'small'; // show small filter buttons
// Number of records to display on the screen
// Value of -1 lists all records in a table
$opts['inc'] = - 1;
// Set default prefixes for variables
$opts['js']['prefix'] = '';
$opts['dhtml']['prefix'] = '';
$opts['cgi']['prefix']['operation'] = 'PME::op::';
$opts['cgi']['prefix']['sys'] = '';
$opts['cgi']['prefix']['data'] = '';
$opts['buttons']['A']['up'] = array();
$opts['buttons']['C']['up'] = array();
$opts['buttons']['P']['up'] = array();
$opts['buttons']['D']['up'] = array();
$opts['buttons']['V']['up'] = array();
$opts['buttons']['L']['up'] = array();
$opts['buttons']['F']['up'] = array();
$opts['buttons']['V']['down'] = $opts['buttons']['V']['up'];
$opts['buttons']['L']['down'] = $opts['buttons']['L']['up'];
$opts['buttons']['F']['down'] = $opts['buttons']['F']['up'];
// Navigation style: B - buttons (default), T - text links, G - graphic links
// Buttons position: U - up, D - down (default)
$opts['navigation'] = 'UG';
// Display special page elements
$opts['display'] = array('form' => true, 'query' => false, 'sort' => true, 'time' => false, 'row_hl' => true, 'tabs' => true);
/* Get the user's default language and use it if possible or you can
   specify particular one you want to use. Refer to official documentation
   for list of available languages. */
$opts['language'] = $_SESSION['language'];
$sql_description = 'CASE 
					WHEN (PMEtable0.fk_object_type_id =1 && PMEtable0.xlink=0) THEN info_object_folder_description 
					WHEN (PMEtable0.fk_object_type_id IN (4,5,11,12) && PMEtable0.xlink=0) THEN info_object_url_description
					WHEN (fk_object_type_id IN(115,117,118) && PMEtable0.xlink=0) THEN CONCAT(info_object_file_description,"||",object_id)
					WHEN (PMEtable0.fk_object_type_id >12 && PMEtable0.xlink=0) THEN info_object_file_description
					WHEN (PMEtable0.fk_object_type_id =10 && PMEtable0.xlink=0) THEN object_mail_subject
					WHEN (PMEtable0.fk_object_type_id =7 && PMEtable0.xlink=0) THEN info_object_blog_description
					WHEN PMEtable0.xlink > 0 THEN CONCAT("Shortcut to \'",PMEtable0.object_hrn,"\'")
					ELSE CONCAT("") END';
$opts['fdd']['object_id'] = array( //0
'options' => '',);
$opts['fdd']['dummy'] = array( // used for sorting only		#1
'options' => '', 'input' => 'V', 'sql' => 'IF(fk_object_type_id=1,CONCAT("\0",object_hrn),object_hrn)',);
// join table linked_objects
$opts['fdd']['dummy2'] = array( //2
'options' => '', 'input' => 'V', 'values' => array('db' => 'apps', 'table' => 'linked_objects', 'column' => 'lo_link_oid', 'description' => 'lo_link_oid', 'join' => 'lo_link_oid=PMEtable0.object_id'));
$opts['fdd']['object_parent_id'] = array( //3
'name' => 'Parent Object', 'options' => 'AVCPDR', 'select' => 'T', 'maxlen' => 10, 'default' => '0', 'required' => true,);
$opts['fdd']['fk_object_type_id'] = array( //4
'options' => '', 'escape' => false, 'values' => array('db' => 'infocenter', 'table' => 'info_object_type', 'column' => 'object_type_id', 'description' => 'object_type_icon',),
//  'func'  => 'render_image',
);
//function render_image($s){
//	$ret='<img src="../img/'.$s.'">';
//	return $ret;
//}
$opts['fdd']['type'] = array( //5
'input' => 'V', 'escape' => false, 'colattrs' => $colattrs, 'sql' => $concat, 'sort' => true);
$opts['fdd']['object_hrn'] = array( //6
'name' => 'Name', 'select' => 'T', 'escape' => false,
//  'sql'		 => $concat_n,
'sql' => 'CONCAT_WS("||",PMEtable0.object_hrn,object_id,PMEtable0.fk_object_type_id,PMEtable0.object_parent_id,xlink,GREATEST(IFNULL(PMEjoin10.object_right,0),MAX(IFNULL(PMEjoin11.object_right,0))))', 'func|LVF' => 'encode_s',);
// user function used by table list to generate the link
function encode_s($s)
{
	global $del, $qlink;
	list($hrn, $object_id, $type_id, $parent, $xlink, $ur) = explode('||', $s);
	if ($xlink) {
		$object_id = $xlink;
	}
	if ((int)$type_id == 1) {
		//		$href='HREF="'.$script.'?xflo='.$object_id.'&name='.$hrn.'&r='.$ur;
		$href = 'onClick="location.href=\'' . $script . '?xflo=' . $object_id . '&name=' . $hrn . '&r=' . $ur;
		if ($del == 'on') $href.= '&del=on';
		$href.= '\'"';
		$msg = 'Open Folder';
	} else {
		//		$href='HREF="s.php?sq='.query_encode('xflv',$object_id).'"';
		$href = '"s.php?sq=' . query_encode('xflv', $object_id) . '"';
		if (($type_id > 12) && !in_array($type_id, array(144, 145))) {
			//			$target= 'dl';
			$msg = 'Open File';
		} else {
			//			$target='target="_blank"';
			$msg = 'Open Object';
		}
		if ($type_id == 10) {
			$href = "onClick='window.open($href)'";
			$qlink = '';
		} else {
			$href = $qlink = "onClick='dl.location.href=$href'";
		}
	}
	// limit length of name
	$trimlen = 30;
	if (strlen($hrn) > $trimlen) {
		$hrnv = substr($hrn, 0, $trimlen - 3) . '...';
	} else $hrnv = $hrn;
	$s = "<a title='Open $hrn' $href $target onMouseOver='return statusMsg(\"$msg\")' onMouseOut='return statusMsg(\"\")'>$hrnv</a>";
	return $s;
}
$opts['fdd']['object_dsc'] = array( //7
'name' => 'Description', 'input' => 'VF', 'select' => 'T', 'colattrs|L' => 'width="50%" align="left"', 'wordwrap|L' => 60, 'options' => 'VLCAD', 'sql' => $sql_description, 'func' => 'showThumb');
function showThumb($s)
{
	global $qlink;
	$s = explode('||', $s);
	if ($s[1]) return '<table><tr><td><img src="getThumb.php?t=' . $s[1] . '" ' . $qlink . '></td><td class=pme-cell-0 style="border:0">' . $s[0] . '</td></tr></table>';
	else return $s[0];
}
$opts['fdd']['object_last_upd'] = array( //8
'name' => 'Date', 'options' => 'VLDF', 'select' => 'N', 'colattrs' => 'width="80px" align="center" nowrap', 'sort' => true, 'datemask' => 'd-m-Y H:i', 'datemask|L' => 'd-m-Y');
// join table info_object_file
$opts['fdd']['file'] = array( //9
'options' => '', 'input' => 'V', 'values' => array('table' => 'info_object_file', 'column' => 'fk_object_id', 'description' => 'info_object_file_description', 'join' => 'PMEjoin9.fk_object_id=PMEtable0.object_id'),);
// join permission tables
$opts['fdd']['user_rights'] = array( //10
'options' => '', 'input' => 'V', 'values' => array('db' => 'apps', 'table' => 'user_rights', 'column' => 'fk_object_id', 'description' => 'object_right', 'join' => 'PMEjoin10.fk_object_id=PMEtable0.object_id AND PMEjoin10.fk_user_id=' . $uid),);
$opts['fdd']['group_rights'] = array( //11
'options' => '', 'input' => 'V', 'values' => array('db' => 'apps', 'table' => 'group_rights', 'column' => 'fk_object_id', 'description' => 'object_right', 'join' => 'PMEjoin11.fk_object_id=PMEtable0.object_id AND PMEjoin11.fk_group_id IN (' . $group_ids . ')'),);
$opts['fdd']['mails'] = array( //12   // added for e-mail
'options' => '', 'input' => 'V', 'values' => array('db' => 'infocenter', 'table' => 'info_object_mail', 'column' => 'fk_object_id', 'description' => 'object_mail_subject', 'join' => 'PMEjoin12.fk_object_id=PMEtable0.object_id'),);
$object_remark = '
CASE
WHEN(PMEtable0.fk_object_type_id >12) THEN (
	CONCAT("Rev.:&nbsp;",info_object_file_revision," Size:&nbsp;",
		CASE WHEN (info_object_file_size BETWEEN POW(2,10) AND POW(2,20))THEN CONCAT(ROUND(info_object_file_size/POW(2,10),1),"&nbsp;KB")
		WHEN (info_object_file_size BETWEEN POW(2,20) AND POW(2,30))THEN CONCAT(ROUND(info_object_file_size/POW(2,20),1), "&nbsp;MB")
		ELSE CONCAT(info_object_file_size,"&nbsp;B") END))
WHEN(PMEtable0.fk_object_type_id =7) THEN(
	CONCAT(" Size:&nbsp;",
		CASE WHEN (info_object_blog_size BETWEEN POW(2,10) AND POW(2,20))THEN CONCAT(ROUND(info_object_blog_size/POW(2,10),1),"&nbsp;KB")
		WHEN (info_object_blog_size BETWEEN POW(2,20) AND POW(2,30))THEN CONCAT(ROUND(info_object_blog_size/POW(2,20),1), "&nbsp;MB")
		ELSE CONCAT(info_object_blog_size,"&nbsp;B") END))
WHEN(PMEtable0.fk_object_type_id=10) THEN(
	CONCAT(" Size: ", 
		CASE WHEN (object_mail_size BETWEEN POW(2,10) AND POW(2,20))THEN CONCAT(ROUND(object_mail_size/POW(2,10),1),"&nbsp;KB",IF(object_mail_at>0,CONCAT(" <img src=\'images/paperclip.gif\' align=\'absmiddle\' title=\'",object_mail_attachments,"\'>"),""))WHEN (object_mail_size<POW(2,30))THEN CONCAT(ROUND(object_mail_size/POW(2,20),1),"&nbsp;MB",IF(object_mail_at>0,CONCAT(" <img src=\'images/paperclip.gif\' align=\'absmiddle\' title=\'",object_mail_attachments,"\'>"),""))ELSE CONCAT(object_mail_size,"&nbsp;B",IF(object_mail_at>0,CONCAT(" <img src=\'images/paperclip.gif\' align=\'absmiddle\' title=\'",object_mail_attachments,"\'>"),"")) END))
ELSE CONCAT("") END';
$opts['fdd']['object_remark'] = array( //13
'name' => 'Remark', 'options' => 'VLDF', 'select' => 'T', 'colattrs|LF' => 'align="left" width="100pt" nowrap', 'escape' => false, 'sql' => $object_remark,);
$opts['fdd']['fk_user_id'] = array('name' => 'Owner', 'options' => 'LF', 'select' => 'M', 'colattrs|L' => 'align="left" width="100pt" nowrap', 'values' => array('db' => 'infocenter', 'table' => 'info_user', 'column' => 'user_id', 'description' => array('columns' => array('0' => 'user_lname', '1' => 'user_fname_s', '2' => 'user_id'), 'divs' => array('0' => ', ', '1' => '<span class=uid>', '2' => '</span>'))), 'sort' => true,);
$opts['fdd']['folder'] = array( //14
'options' => '', 'input' => 'V', 'values' => array('table' => 'info_object_folder', 'column' => 'fk_object_id', 'description' => 'info_object_folder_description', 'join' => 'PMEjoin15.fk_object_id=PMEtable0.object_id'));
// url, application, database, agenda:type id in (4,5,11,12)
$opts['fdd']['url'] = array( //15
'options' => '', 'input' => 'V', 'values' => array('table' => 'info_object_url', 'column' => 'fk_object_id', 'description' => 'info_object_url_description', 'join' => 'PMEjoin16.fk_object_id=PMEtable0.object_id'),);
$opts['fdd']['blog'] = array( //16   // added for blog
'options' => '', 'input' => 'V', 'values' => array('db' => 'infocenter', 'table' => 'info_object_blog', 'column' => 'fk_object_id', 'description' => 'info_object_blog_description', 'join' => 'PMEjoin17.fk_object_id=PMEtable0.object_id'),);
$opts['group_field'] = 'PMEtable0.object_id';
if (!$admin) $permfilter = ' AND(PMEjoin10.object_right > 1 OR PMEjoin11.object_right > 1)';
if (empty($xfl)) {
	$opts['filters'] = '(lo_link_oid = PMEtable0.object_id) AND (lo_fk_ref_oid = ' . $fk_oid . ') AND (lo_fk_ref_key_id = ' . $fk_key_id . ')' . $permfilter;
} else {
	// get all document objects under this leaf
	readNodes($xfl);
	if (!empty($ids)) {
		$ids = array_diff($ids, array('')); // cleanup empty values
		$ids = join(',', $ids);
		$opts['filters'] = "FIND_IN_SET(PMEtable0.object_parent_id,'$ids') AND fk_object_type_id >1 $permfilter";
	}
}
// set the browser dependent css
if ($_SESSION['PMA_USR_BROWSER_AGENT'] == 'IE') {
	$css = 'icIE.css';
} elseif ($_SESSION['PMA_USR_BROWSER_AGENT'] == 'MOZILLA') {
	$css = 'icff.css';
} else {
	$css = 'icff.css';
}
// look for filled forms
// filled forms are available in apps.proj_forms
// check the user rights for the project database. If rights < 2 avoid access of restricted forms
if ($_SESSION['my_or_projects.php'] > 1) $rightsPdb = 1;
else $rightsPdb = 0;
$qry = "
	SELECT fk_form_tpl_id,form_id,form_tpl_hrn,form_status,projects.fk_facility_id,form_content FROM apps.proj_forms
	LEFT OUTER JOIN apps.proj_forms_tpl ON fk_form_tpl_id=form_tpl_id
	LEFT OUTER JOIN apps.projects ON project_id=fk_project_id
	WHERE fk_project_id='$fk_key_id' AND form_tpl_restricted <= '$rightsPdb' ORDER BY form_order,form_id
";
($rs = mysql_query($qry)) || die(mysql_error());
while ($row = mysql_fetch_row($rs)) {
	list($tpl[], $form_id[], $form[], $status[], $f_id, $content[]) = $row;
}
$count = count($tpl);
if ($count) {
	$tcount = 0;
	$facility_id = array('16' => 'LLF', //
	'20' => 'LST/ECF', //
	'26' => 'ECF', //
	'19' => 'NWB', //
	'17' => 'HST', //
	'21' => 'SST', //
	'27' => 'CSST', //
	'18' => 'TWG', //
	'22' => 'HDG', //
	'24' => 'KRG', //
	'25' => 'RWG', //
	'23' => 'KKK'
	//
	);
	$fac = $facility_id[$f_id];
	require_once ('qc.php');
	$echo = '<tr><td class="fav_txt">&nbsp;Project Forms:</td></tr>';
	while ($tcount < $count) {
		if ($form[$tcount][strlen($form[$tcount]) - 1] == '-') $form[$tcount].= $fac;
		// look for multiple forms
		if (!empty($frm_mul) && $tpl[$tcount] == $mtpl) $frm_mul++;
		else unset($frm_mul, $frm_no);
		if (!$frm_mul && (count(array_keys($tpl, $tpl[$tcount])) > 1)) {
			$frm_mul = 1;
			$mtpl = $tpl[$tcount];
		}
		$frm_mul && $frm_no = '#' . $frm_mul;
		$sqform = query_encode('fid', $form_id[$tcount], 'fac', $fac, 'rec', $fk_key_id, 'edt', '0');
		// look for remark
		if ($content[$tcount] && (strlen($content[$tcount]))) {
			$a = array();
			$print_status = '';
			$a = @unserialize($content[$tcount]);
			$a = (array)$a;
			$print_status.= '<span style="font-weight:normal;font-size:10px"> ' . ($a['h_remark'] ? '(' . $a['h_remark'] . ')' : '') . '</span>';
		}
		$echo.= "<tr><td style='font-size:7pt'><a style='cursor:pointer' onclick=\"show_form('$sqform','{$form_id[$tcount]}','$frm_no')\"><img src='images/docs.gif' align='absmiddle'>&nbsp;{$form[$tcount]} $frm_no $print_status</td></tr>\n";
		$tcount++;
	}
	$echo.= '<tr><td class="fav_txt"><br>&nbsp;Project Documents:</td></tr>';
} else $echo = '<tr><td class="fav_txt">&nbsp;No Project Forms available.</td></tr><tr><td class="fav_txt">&nbsp;Project Documents:</td></tr>';
ob_start();
?>
<!DOCTYPE HTML>
<HTML>
<HEAD>
<meta http-equiv="X-UA-Compatible" content="IE=8" />
<meta http-equiv="content-type" content="text/html; charset=UTF-8">
<meta name="author" content="Gert Lehmann">
<meta http-equiv="expires" content="0"> 
<title>Linked Documents List</title>
<script type="text/javascript" src="js/util_2.0.js"></script>
<script src="js/include.js"></script>
<link rel="stylesheet" type="text/css" href="pmelist.css">
<style type="text/css">
button {width:37px;height:22;font-size:7pt;line-height:7pt}
.ctrla,.ctrlb,.ctrl{border-spacing:0pt;font-family: Verdana, Sans-Serif;font-size:8pt;text-align:left;}
.ctrl {width:40px;border:0px;}
.ctrla {border:silver 1pt solid;background-color:White;white-space:nowrap;overflow:hidden}
.ctrlb {vertical-align:middle;cursor:pointer;white-space:nowrap}
.tabl{background:<?php
echo $_SESSION['BGROUND'] ?>; width:100%;border-collapse:collapse;table-layout:fixed;white-space:nowrap;}
.headtd{line-height:20pt;}
.uid{display:none}
input.pme-filter{width:99%}
</style>
	<script type="text/javascript" src="js/popwin/popwin2.10.js"></script>
<script>
var o;

if (!('indexOf' in Array.prototype)) {
  Array.prototype.indexOf= function(find, i /*opt*/) {
    if (i===undefined) i= 0;
    if (i<0) i+= this.length;
    if (i<0) i= 0;
    for (var n= this.length; i<n; i++)
    if (i in this && this[i]===find)
    return i;
    return -1;
  };
}
function checkMail(){return}
<?php
if ((int)$_REQUEST['show']) { // added for e-mail
	$action = isset($_GET['checkMail']) ? 'Fprojall' : 'Fproj';
	require_once 'qc.php';
	echo '
  function checkMail(){
	  dl.location.href="apps/imap/linkMail.php?sq=' . rawurlencode(query_encode('action', $action, 'xfl', $fk_key_id)) . '";
  }';
}
?>
function show_form(a,w,n) {
	if(!n) n="";
	var f_win=window.open("apps/project_db/show_pf.php?sq="+a+"&frm_no="+escape(n),w,"width=700,height=800,scrollbars=1,menubar=1,resizable=1");
	f_win.focus();
}
function Ocn(o){
	var lnk = document.all ? o.nextSibling.firstChild.getAttribute('onClick'):o.nextSibling.nextSibling.firstChild.getAttribute('onClick');
	if(typeof lnk == 'string') eval(lnk); else if(typeof lnk == 'function') lnk();
}
function listRefresh(){
  location.reload();
}
function filterMselect(){
  var sel='<select id="qf14_id" class="pme-filter" size="3" multiple="" name="qf14_id[]">';
  var elm = document.querySelector('#qf14_id');
  if(!elm) return;
  elm.options.length=1;
  var uid = document.querySelectorAll('.uid');
  var id=[], u;
  for(var i=0;i<uid.length;i++){
    u = uid[i].innerHTML;
    if(id.indexOf(u)>-1) continue;
    id.push(u);
  }
  include.js([location.href.split('?')[0]+'?getUsers='+id.join(',')],function(){
    if(o.length){
      for(var i=0;i<o.length;i=i+3){
        elm.options[elm.options.length] = new Option(o[i+1]+', '+o[i+2],o[i]); 
      }
    }

  });
}
</script>

</HEAD>
<BODY style="margin:0px;overflow:hidden;padding-right:0px;padding-top:61px;" onload="checkMail();filterMselect();" >
<div style="position:absolute;top:0;padding-left:2px;
	width: 100%;
	height: 60px;
	overflow: hidden;
	border-bottom:1px solid black;
	white-space:nowrap;
	background: #e4e4cb;">
<table class="tabl">
<colgroup>
  <col width="3px">
  <col width="*">
  <col width="78px">
</colgroup>
<?php
//echo 'upload_ui.php?path='.$path;
echo '<tr class="headtd"><td colspan="2">Linked&nbsp;Documents&nbsp;';
if ($fk_rec_name != '') echo "of&nbsp;$obj_hrn";
echo '</td>';
if ($ur > 2) echo '
<td class="ctrlb"><img src="img/tool_folder.gif" title="new folder" onClick="var f=open(\'folder_ui.php?xfl=' . $xfl . '&path=' . urlencode($name) . '\',\'inf\',\'width=370,height=300,resizable=1,top=150,left=400\');f.focus()">
<img src="img/tool_upload.gif" title="File upload" onClick="popwin.open(\'jUpload\',\'up/?mode=pdb2&xfl=' . $xfl . '&path=' . urlencode($name) . '\',\'width=720,height=450,resizable=1,top=100,left=5,iframe=1,modalbg=1,title=Upload&nbsp;Files\')">
<img src="img/obj_msg.gif" title="add blog" onClick="var upload = open(\'blog_add.php?addSubT=' . $_SESSION[$script]['blog_subt'] . '&xfl=' . $xfl . '&path=' . urlencode($name) . '\',\'blg\',\'width=370,height=300,resizable=1,top=130,left=450\');upload.focus()">';
else echo '<td>';
echo '</td></tr><tr><td>&nbsp;</td>';
// address field and button
list($icon, $name) = explode('||', $name);
echo '<td class="ctrla"><img src="img/' . $icon . '" align="left" hspace="3px" vspace="0px" >' . $name . '</td>';
echo '<td class="ctrlb"><button type="button" name="up" value="up" onClick="self.location.href=\'' . $script . '?upb=1';
if ($del == 'on') echo '&del=on';
echo '\'" title="one level up" ';
if (empty($xfl)) echo 'disabled';
echo '><img src="images/folderup.gif" width="16" height="16" border="0" alt="one level up"></button>';
echo '</td></tr><tr style="line-height:5px"><td colspan="3">&nbsp;</td></tr></table></div>', "\n\r";
echo '<div id="content" style="position:absolute;top:62px;bottom:1px;left:0;right:0;overflow-y:scroll;padding:1px">';
// if table with forms
if (!$xfl) {
	echo '<table class="tabl">';
	echo $echo;
	echo '</table>';
}
// table
require_once 'extensions/phpMyEdit-htmlcal.class164js.php';
//echo '<div >';
$myedit = new phpMyEdit_htmlcal($opts);
echo '</div>';
ob_flush();
?>
<iframe src='empty.html' name='dl' style='display:none'></iframe>
</BODY>
</HTML>


