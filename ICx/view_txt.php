<?php
if (!require 'IC2auth.inc.php') exit; // sets user id ($uid) and permission (rights), defines $script
$object_id = $_SESSION['view_txt']['oid'];
if (!empty($object_id)) {
	$query = 'SELECT info_object_msg_body FROM infocenter.info_object_msg WHERE fk_object_id=' . $object_id;
	$result = mysql_query($query);
	if (!$result) die('Could not query:' . mysql_error());
	$txt = mysql_fetch_row($result) or die(mysql_error());
	if ($txt[0]) {
		echo $txt[0];
	} else echo "no html message object to view: $object_id, $xflt";
} else die('no object to view');
?>