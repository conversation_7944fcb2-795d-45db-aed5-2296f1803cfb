<?php
if (!require 'IC2auth.inc.php') exit; // sets user id ($uid) and permission (rights), defines $script
define('PATH_IC', './');
define('URL_IC', './');
// language
$opts['language'] = $lang = $_SESSION['language'];
// Debug query
//$debug_query = true;
// Debug post variables
//$debug_posts = true;
// Debug get variables
//$debug_gets = true;
$opts['options'] = 'VF';
//print_r($_REQUEST);
if (isset($_REQUEST['xfl'])) {
	$xfl = $_REQUEST['xfl'];
}
if ((!isset($xfl)) || (@$_POST['navop'] == 'All')) {
	$xfl = - 1;
}
if ($xfl > 0) {
	$_SESSION[$script]['persist']['xfl'] = $xfl;
	$opts['cgi']['persist']['xfl'] = $xfl;
}
if ($_REQUEST['pg'] == 1) $xfl = $_SESSION[$script]['persist']['xfl'];
if (empty($lang)) {
	$opts['language'] = $_SERVER['HTTP_ACCEPT_LANGUAGE'];
	$lang = strtolower(substr($opts['language'], 0, 2));
}
$opts['filters'] = 'comp_deleted != "Y"';
if ($_REQUEST['sdo'] || $_REQUEST['sdo_x']) {
	$s = strip_tags($_REQUEST['searchc']);
	$p = strip_tags($_REQUEST['searchp']);
	$opts['cgi']['persist']['search_c'] = $s;
	$opts['cgi']['persist']['search_p'] = $p;
} else {
	$s = $_REQUEST['search_c'];
	$p = $_REQUEST['search_p'];
	$opts['cgi']['persist']['search_c'] = $s;
	$opts['cgi']['persist']['search_p'] = $p;
}
if ($s) $opts['filters'].= " AND Company LIKE '%" . addslashes($s) . "%'";
if ($p) $opts['filters'].= " AND Name LIKE '%" . addslashes($p) . "%'";
// MySQL host name, user name, password, database, and table
$opts['db'] = $_SESSION['DB_APPS'];
$opts['tb'] = 'companies';
// Name of field which is the unique key
$opts['key'] = 'Company_ID';
// Type of key field (int/real/string/date etc.)
$opts['key_type'] = 'int';
// Sorting field(s)
$opts['sort_field'] = 'Company';
// Number of records to display on the screen
// Value of -1 lists all records in a table
$opts['inc'] = 15;
// current date
$today = date('Y-m-d');
// Name of field which contains the record-owner userid
$opts['useridfield'] = 'fk_uid';
// Number of lines to display on multiple selection filters
$opts['multiple'] = '5';
// image url
$opts['url'] = array('images' => URL_IC . 'imgIClist/');
$print_label = array('en' => 'Print', 'de' => 'Drucken', 'nl' => 'Afdrukken');
$back_label = array('en' => 'Close', 'de' => 'Schlie�en', 'nl' => 'Sluiten');
$report_label = array('en' => 'Report', 'de' => 'Report', 'nl' => 'Rapport');
$reset_label = array('en' => 'Reset', 'de' => 'Zur�ck', 'nl' => 'Terug');
$search_label = array('en' => 'Search', 'de' => 'Suche', 'nl' => 'Zoek');
$clear_search_label = array('en' => 'Clear search fields', 'de' => 'Sucheintr�ge l�schen', 'nl' => 'Zoekworden wissen');
$reset_btn = array('code' => '<input class="reset_btn" type="button" name="reset" value="' . $reset_label[$lang] . '" onClick="self.location.href=\'' . $script . '?pg=2\'">');
$print_btn = array('code' => '<input class="print_btn" type="button" name="print" value="' . $print_label[$lang] . '" onClick="window.print()">');
$report_btn = array('code' => '<input class="report_btn" type="button" name="reset" value="' . $report_label[$lang] . '" onClick="self.location.href=\'' . $script . '?rp=2\'">');
$n_row_btn = array('function' => 'nrow_btn');
$cr_lf = array('code' => '<br />');
$search_c = array('code' => ' Company:<input name="searchc" value="' . $s . '" onKeyPress="fsub(event)"> Person:<input name="searchp" value="' . $p . '" onKeyPress="fsub(event)">&nbsp;<input type=image name=sdo src="' . $opts['url']['images'] . '/search.gif" style="vertical-align:middle;" title="' . $search_label[$lang] . '">&nbsp;
<img src="' . $opts['url']['images'] . '/search.gif" style="vertical-align:middle;"><input type=image name=sdo src="' . $opts['url']['images'] . '/pme-delete.png" style="vertical-align:middle;cursor:pointer;position:relative;left:-14px" title="' . $clear_search_label[$lang] . '" onclick="return document.form[\'searchc\'].value=document.form[\'searchp\'].value=\'\';"><input type=hidden name=sdo>');
$opts['buttons']['L']['up'] = array('<<', '<', '>', '>>', $search_c);
$opts['buttons']['F']['up'] = $opts['buttons']['L']['up'];
$opts['buttons']['A']['up'] = array('save', 'more', 'cancel');
$opts['buttons']['C']['up'] = array('save', 'more', 'cancel');
$opts['buttons']['P']['up'] = array('save', 'cancel');
$opts['buttons']['D']['up'] = array('save', 'cancel');
$opts['buttons']['V']['up'] = array('cancel');
$opts['buttons']['L']['down'] = $opts['buttons']['L']['up'];
$opts['buttons']['V']['down'] = $opts['buttons']['V']['up'];
$opts['buttons']['F']['down'] = $opts['buttons']['F']['up'];
// buttons for view/change if not owner of the record
$opts['alt_buttons']['V']['up'] = $opts['alt_buttons']['V']['down'] = array('cancel');
$opts['alt_buttons']['C']['up'] = $opts['alt_buttons']['C']['down'] = array('cancel');
$opts['filter_btn'] = 'small'; // show small filter buttons
$opts['excel_btn'] = 'Export to Excel';
if (!empty($_REQUEST['xls'])) $opts['excel'] = ' Adress Database';
// Navigation style: B - buttons (default), T - text links, G - graphic links
// Buttons position: U - up, D - down (default)
$report_hrn = array('en' => 'Report of ', 'de' => 'Report der ', 'nl' => 'Rapport van ');
if (($rp = $_REQUEST['rp']) == 1) {
	$report = $report_hrn[$lang];
	$opts['cgi']['persist']['rp'] = '1';
}
$opts['navigation'] = !$rp ? ($op ? 'DG' : 'UG') : 'UG';
// Display special page elements
$opts['display'] = array('form' => true, 'query' => false, 'sort' => true, 'time' => false, 'tabs' => true, 'xnav' => array('V' => true, 'C' => true, 'P' => true, 'D' => false));
// Set default prefixes for variables
$opts['js']['prefix'] = '';
$opts['dhtml']['prefix'] = '';
$opts['cgi']['prefix']['operation'] = 'PME::op::';
$opts['cgi']['prefix']['sys'] = '';
$opts['cgi']['prefix']['data'] = '';
echo '
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
<title>DNW Database Editor: Addresses</title>
<link href="' . URL_IC . '/pme.css" rel="stylesheet">
<style>#tab0{border-bottom:1px solid white}</style>
<script type="text/javascript" src="' . URL_IC . '/js/utils.js' . '"></script>
<script>
  window.focus();
  function fsub(e){
	  if(e.keyCode==13){document.form.sdo.value=1;document.form.submit();}
  } 
</script>';
echo '
</HEAD>
<BODY style="background-color:#ebebeb">
';
if ($_REQUEST['operation'] == $opts['cgi']['prefix']['operation'] . 'View') $view = true;
else $view = false;
// render tab's
$inc = '';
$supplier = $customer = $other = true;
echo '<table class="body" style="border:1px silver solid;margin-top:5px;border-top:0px;width:100%;"><tr><td>';
$opts['fdd']['Company_ID'] = array( //0
'options' => '');
$opts['fdd']['Company'] = array( //0
'name' => 'Company', 'select' => 'T', 'maxlen' => 125, 'required' => true, 'sort' => true, 'report' => true,);
$tab = array('en' => 'Mail Address', 'de' => 'Postadresse', 'nl' => 'Postadres');
$opts['fdd']['Mail_Address'] = array( //1
'tab' => $tab[$lang], 'name' => 'Address', 'select' => 'T', 'maxlen' => 75, 'report' => true, 'sort' => true,);
$opts['fdd']['Mail_Postal_code'] = array( //2
'name' => 'Postal Code', 'select' => 'T', 'maxlen' => 12, 'report' => true, 'sort' => true,);
$opts['fdd']['Mail_Town'] = array( //3
'name' => 'Place', 'select' => 'T', 'maxlen' => 50,
//  'size'	 => '25',
'report' => true, 'sort' => true,);
$opts['fdd']['Mail_Country'] = array( //4
'name' => 'Country', 'select|F' => 'M', 'select' => $rp ? 'M' : 'D', 'maxlen' => 75, 'size|F' => '5', 'report' => true, 'values2' => '?', 'sort' => true);
$opts['fdd']['Mail_Country']['values']['table'] = 'def_country';
$opts['fdd']['Mail_Country']['values']['column'] = 'Country_ID';
$opts['fdd']['Mail_Country']['values']['description'] = 'Country_Name';
$opts['fdd']['dummy'] = array( //5
'name' => 'Persons', 'values' => array('db' => $_SESSION['DB_APPS'], 'table' => 'persons', 'column' => 'fk_Company_ID', 'description' => 'fk_Company_ID', 'join' => 'Company_ID=fk_Company_ID'), 'options' => '');
$opts['group_field'] = 'Company_ID'; // GROUP field
$opts['count'] = 'COUNT(DISTINCT Company_ID)'; // COUNT (DISTINCT group field) is required because of grouping
$opts['fdd']['Mail_Zip'] = array( //7
'name' => 'Zip', 'select' => 'T', 'maxlen' => 12, 'report' => true, 'options' => 'ACPV',);
$tab = array('en' => 'Visit Address', 'de' => 'Besucheradr.', 'nl' => 'Bezoekersadres');
$opts['fdd']['Vis_Address'] = array('name' => 'Address', 'select' => 'T', 'maxlen' => 75, 'sort' => true, 'report' => true, 'options' => 'AVCD', 'tab' => $tab[$lang]);
$opts['fdd']['Vis_Postal_Code'] = array('name' => 'Postal Code', 'select' => 'T', 'maxlen' => 12, 'options' => 'AVCD', 'default' => trim($addsupl[2]), 'report' => true, 'sort' => true);
$opts['fdd']['Vis_Zip'] = array('name' => 'Zip', 'select' => 'T', 'maxlen' => 12, 'options' => 'AVCD', 'report' => true, 'sort' => true);
$opts['fdd']['Vis_City'] = array('name' => 'City', 'select' => 'T', 'maxlen' => 50, 'options' => 'AVCD', 'report' => true, 'sort' => true, 'default' => trim($addsupl[3]));
$opts['fdd']['Phone'] = array('name' => 'Phone', 'select' => 'T', 'maxlen' => 25, 'options' => 'AVCDL', 'report' => true, 'sort' => true, 'default' => trim($addsupl[6]));
$opts['fdd']['Fax'] = array('name' => 'Fax', 'select' => 'T', 'maxlen' => 25, 'options' => 'AVCDL', 'report' => true, 'sort' => true, 'default' => trim($addsupl[7]));
$opts['fdd']['Email'] = array('name' => 'Email', 'select' => 'T', 'maxlen' => 50, 'options' => 'AVCD', 'URLprefix' => 'mailto:', 'url' => '$link', 'report' => true, 'default' => trim($addsupl[8]), 'sort' => true);
$opts['fdd']['Homepage'] = array('name' => 'Homepage', 'select' => 'T', 'maxlen' => 50, 'options' => 'AVCD', 'escape' => false, 'report' => true, 'sort' => true, 'func' => 'extLink');
function extLink(&$s)
{
	if (!strlen($s)) return '';
	if (!stristr($s, 'http://') && !stristr($s, 'https://')) $file = 'http://' . $s;
	else $file = $s;
	return "<a href=\"$file\" target='_blank' onclick=\"if(external.ShellExecute && external.ShellExecute(external.HWND, 'open', '$file', '', '', 1)) return false\">$file</a>";
}
$tab = array('en' => 'Miscell.', 'de' => 'Verschiedenes', 'nl' => 'Diverse');
$opts['fdd']['Commentary'] = array('name' => 'Comments', 'select' => 'T', 'maxlen' => 255, 'options' => 'AVCD', 'tab' => $tab[$lang], 'report' => true, 'textarea' => array('rows' => 3, 'cols' => 40), 'default' => trim($addsupl[9]));
$opts['fdd']['comp_sc'] = array('name' => 'Suppl. Code', 'select' => 'N', 'input' => 'V', 'size' => '10', 'colattrs' => 'align="center"', 'maxlen' => '50', 'options' => $supplier ? 'AVCD' : '', 'report' => $supplier ? true : false, 'sort' => true, 'sql' => 'CONCAT(SuppliersCode,"|",comp_sc_nwb,"|",comp_sc_guk)', 'func' => 'render_code');
function render_code(&$s)
{
	$sc = explode('|', $s);
	return ($sc[0] ? 'NOP:&nbsp;' . $sc[0] : '') . ($sc[1] ? '<br>NWB:&nbsp;' . $sc[1] : '') . ($sc[2] ? '<br>GUK:&nbsp;' . $sc[2] : '');
}
$opts['fdd']['VAT'] = array('name' => 'VAT', 'select' => 'T', 'maxlen' => 25, 'options' => $supplier ? 'AVCD' : '', 'report' => true, 'sort' => true);
$opts['fdd']['comp_dn'] = array('name' => 'Deb-No.', 'select' => 'N', 'input' => 'V', 'size' => '10', 'colattrs' => 'align="center"', 'maxlen' => '50', 'report' => $customer ? true : false, 'sort' => true, 'options' => $customer ? 'AVCD' : '', 'sql' => 'CONCAT(deb_no,"|",comp_dn_nwb,"|",comp_dn_guk)', 'func' => 'render_code');
$opts['fdd']['BU'] = array('name' => 'BU', 'select' => 'T', 'maxlen' => 5, 'options' => '', 'sort' => true);
$opts['fdd']['companies_lup'] = array('name' => 'last update', 'select' => 'N', 'maxlen' => 14, 'options' => 'VD', 'datemask' => 'D, d M Y H:i', 'sort' => true);
// this field is used to set the owner of the record. If $rights >= 5 the field content is replaced by the current user id.
$opts['fdd']['fk_uid'] = array('name' => 'edited by', 'select' => 'T', 'default' => "$uid", 'maxlen' => 4, 'options' => 'VACPD', 'values' => array('db' => $_SESSION['DB_IC'], 'table' => 'info_user', 'column' => 'user_id', 'filters' => "user_id=$uid", 'description' => array('columns' => array('0' => 'user_fname', '1' => 'user_mname', '2' => 'user_lname'), 'divs' => array('0' => ' ', '1' => ' ')),));
if ($view) $_GET['cur_tab'] = 'tab2';
if ($rp) {
	require_once PATH_IC . '/extensions/phpMyEdit_report.class.php';
	new phpMyEdit_report($opts);
} else {
	require_once PATH_IC . '/extensions/phpMyEdit-htmlcal.class164js.php';
	new phpMyEdit_htmlcal($opts);
}
//ob_end_flush();
echo '</td></tr></table>';
if ($view) {
	if (!strlen($_GET['sq'])) return;
	else {
		require_once 'qc.php';
		$rec = query_sdecode($_GET['sq']);
		$rec = $rec['rec'];
		if ($rec) $where = "WHERE fk_Company_ID='$rec'";
	}
	// show person information of this company
	if ($where) {
		$link = mysql_connect($_SESSION['DB_HOST'], $_SESSION['DB_UN'], $_SESSION['DB_PW']) or die("Could not connect: " . mysql_error());
		echo '<div style="background-color:#ebebeb">';
		$qry = "SELECT Concat_WS(' ',Title,Initials,Name),Phone,Fax,Mobile_Number,Dep_Code,Email FROM {$_SESSION['DB_APPS']}.persons $where ORDER BY Name";
		($rs = mysql_query($qry, $link)) || die(mysql_error());
		if ($rs) {
			// first record is always header
			$header['en'] = array('Name', 'Phone', 'Fax', 'Mobile', 'Dpmt.', 'e-mail');
			$header['de'] = array('Name', 'Telefon', 'Fax', 'Mobil', 'Abt.', 'e-mail');
			$header['nl'] = array('Naam', 'Telefoon', 'Fax', 'Mobiel', 'Afd.', 'e-mail');
			$persons = array('en' => 'Persons', 'de' => 'Personen', 'nl' => 'Personen');
			$nopersons = array('en' => 'No entry found in database', 'de' => 'Keinen Eintrag in der Datanbank gefunden.', 'nl' => 'Geen gegevens gevonden in de database.');
			$table = '<p style="font:11pt Sans-serif;font-weight:bold">&nbsp;' . $persons[$lang] . ':</p>';
			$table.= '<table style="margin-left:5px;border-collapse:collapse;width:99%"><colgroup><col width="150px"><col width="130px"><col width="130px"><col width="*"><col width="*"><col width="*"></colgroup><thead><tr class="pme-header">';
			foreach ($header[$lang] as $h) $table.= '<th class="pme-header">' . $h . '</th>';
			$table.= '</tr></thead>';
			$i = $r = 0;
			while ($row = mysql_fetch_row($rs)) {
				// loop over the data records
				$i++;
				$r = $i % 2;
				$cols = count($row);
				$table.= '<tr class="pme-row-' . $r . '">';
				for ($j = 0; $j < $cols; $j++) {
					if (($j + 1) == $cols) $table.= '<td class="pme-cell-' . $r . '" ' . $style[$j] . '><a href="mailto:' . $row[$j] . '">' . $row[$j] . '</a></td>';
					else $table.= '<td class="pme-cell-' . $r . '" ' . $style[$j] . '>' . $row[$j] . '</td>';
				}
				$table.= '</tr>';
			}
			if (!@mysql_num_rows($rs)) $table.= '<tr><td class="pme-cell-' . $r . '" colspan=6><br>' . $nopersons[$lang] . '</td></tr>';
			echo $table.= '</table>';
		}
		echo '</div>';
	}
}
echo '</BODY>
</HTML>';
?>

