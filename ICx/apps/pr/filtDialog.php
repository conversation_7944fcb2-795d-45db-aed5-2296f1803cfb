<?php
require 'IC2auth.inc.php';
define('PATH_IC', '../../');
switch ($rights) {
case 0:
	echo '<h3>Access denied!</h3>';
	exit;
case 1:
	echo '<h3>Database maintenance. Please try again later or contact the administrator for details.</h3>';
	exit;
case 2:
	$opts['options'] = 'P';
	break;

case 3:
	$opts['options'] = 'VACPD';
	break;

case 4:
	$opts['options'] = 'VACPD';
	break;

case 5:
	$opts['options'] = 'VACPD';
	break;

case 6:
	$opts['options'] = 'VACPD';
	break;

case 7:
	$opts['options'] = 'VACDP';
	break;

case 8:
	$opts['options'] = 'VACDP';
	break;

default:
	echo '<h3>Access denied!</h3>';
	exit;
}
require_once('DNW_DB.php');
$DB = new DNW\DNW_DB();
require 'qc.php';
$opts['language'] = $lang = $_SESSION['language'];
if (empty($lang)) {
	$lang = $_SERVER['HTTP_ACCEPT_LANGUAGE'];
	$lang = strtolower(substr($lang, 0, 2));
}
if (!isset($_REQUEST['sq_rp'])) die('something wrong.');
if (!($params = extract(query_decode('sq_rp')))) die('invalid params');
if (($rpscript && $act == 'save') || $act == 'update') $_SESSION[$script . '_save'] = $_SESSION[$rpscript . '_rp'];
if (!empty($_REQUEST['saveadd'])) $act = 'saveadd';
if (!empty($_REQUEST['savechange'])) $act = 'savechange';
if (!empty($_REQUEST['operation'])) {
	if ($_REQUEST['operation'] == 'Copy') {
		$act = 'set';
		unset($_REQUEST['operation']);
	}
	if ($_REQUEST['operation'] == 'Change') $act = 'change';
}
//echo "$params, $ID, $act, $rpscript; $filtID";
//print_r($_REQUEST);
// MySQL host name, user name, password, database, and table
//require 'IC2_db_con.inc';
$opts['db'] = 'apps';
$opts['tb'] = 'my_filters';
$opts['myscript'] = $script;
$qry = "SELECT object_hrn FROM infocenter.info_object WHERE object_id='$ID'";
if (($rs = $DB->Select($qry,[],false)) === false) exit('mysql_error(@71)');;
list($filter_cat) = $rs[0];
$titel = array('en' => 'Filter for ', 'de' => 'Filter f�r ', 'nl' => 'Filter voor ');
ob_start();
?>
<!DOCTYPE HTML>
<HTML>
<HEAD>
<title>My Filters</title>
<script type="text/javascript" src="../../IC2/js/utils.js"></script>
<link rel="stylesheet" type="text/css" href="../../IC2/pme.css">
<style type="text/css">
.ctrla,.ctrlb,.ctrl{border-spacing:0pt;font-family: Verdana, Sans-Serif;font-size:8pt;text-align:left;}
.ctrl {width:40px;border:0px;}
.ctrla {width:250px;border:silver 1pt solid;background-color:White;}
.ctrlb {vertical-align:middle;}
.tabl{background:<?php
echo $_SESSION['BGROUND'] ?>; width:100%;border:white 2px solid;border-collapse:collapse;padding:4pt;}
.headtd{line-height:20pt;}
</style>
</HEAD>
<BODY style="margin:5px;background:<?php
echo $_SESSION['BGROUND'] ?>" onload="self.focus()">
<table class="tabl"><tr class="headtd"><td><?php
echo "{$titel[$lang]}\"$filter_cat\""; ?></td></tr>
<tr><td>
<div style="overflow:auto;height:350px">
<?php
switch ($act) {
case 'save':
	$opts['cgi']['overwrite']['operation'] = 'Add';
	break;

case 'saveadd':
	$_POST['my_filt_fk_uid'] = $uid;
	$_POST['my_filt_fk_oid'] = $ID;
	$rpscript = $_SESSION[$script . '_save']['script'];
	$_SESSION[$script . '_save']['name'] = $_POST['my_filt_name'];
	$_SESSION[$rpscript . '_rp']['name'] = $_POST['my_filt_name'];
	$_POST['my_filt_filt'] = serialize($_SESSION[$script . '_save']);
	unset($_SESSION[$script . '_save']);
	break;

case 'savechange':
	$_SESSION[$script . '_save']['name'] = $_POST['my_filt_name'];
	$_POST['my_filt_filt'] = serialize($_SESSION[$script . '_save']);
	unset($_SESSION[$script . '_save']);
	break;

case 'update':
	if (empty($filtID)) break;

	$my_filt_filt = serialize($_SESSION[$script . '_save']);
	$qry = "UPDATE {$opts['db']}.{$opts['tb']} SET my_filt_filt='$my_filt_filt' WHERE my_filt_id='$filtID' AND my_filt_fk_oid='$ID' LIMIT 1";
	if (($rs = $DB->Update($qry,[])) == false) exit('mysql_error(@125)');;
	echo "
		<p class='ctrla' style='font-size:10pt;'>Filter {$_SESSION[$script . '_save']['name']} successfully updated.</p>
		<script>window.setTimeout('self.close()', 3000);</script>";
	unset($_SESSION[$script . '_save']);
	echo '
		</div></td></tr></table>
		</BODY>
		</HTML>';
	ob_flush();
	exit;
case 'change':
	$rec = query_decode('sq');
	$rec = (int)$rec['rec'];
	$qry = "SELECT my_filt_filt from {$opts['db']}.{$opts['tb']} WHERE my_filt_id='$rec'";
	if (($rs = $DB->Select($qry,[],false)) === false) exit('mysql_error(@140)');;
	list($filter) = $rs[0];
	$_SESSION[$script . '_save'] = unserialize($filter);
	unset($filter);
	break;

case 'load':
	$opts['cgi']['persist']['action'] = 'load';
	break;

case 'set':
	if (!$_REQUEST['action'] == 'load') exit('no load request');
	$query = query_decode('sq');
	if (!($rec = $query['rec'])) exit('invalid sq');;
	$qry = "SELECT my_filt_filt FROM {$opts['db']}.{$opts['tb']} WHERE my_filt_id='$rec' AND my_filt_fk_oid='$ID'";
	if (($rs = $DB->Select($qry,[],false)) === false) exit('mysql_error(@155)');;
	list($filter) = $rs[0];
	$filter = unserialize($filter);
	$filter['filtID'] = $rec;
	$_SESSION[$filter['script'] . '_rp'] = $filter;
	isset($filter['mode']) && $mode = $filter['mode'];
	echo '<script>var lnk=opener.location.href.split("?");opener.location.replace(lnk[0]+"?rp=1' . $mode . '");self.close();</script>';
	exit;
}
if ($_REQUEST['action'] == 'load') $opts['cgi']['persist']['action'] = 'load';
$opts['cgi']['persist']['sq_rp'] = query_encode('ID', $ID);
// Name of fields that contains the record-owner userid
$opts['useridfield'] = 'my_filt_fk_uid';
// Name of field which is the unique key
$opts['key'] = 'my_filt_id';
// Type of key field (int/real/string/date etc.)
$opts['key_type'] = 'int';
// Sorting field(s)
$opts['sort_field'] = array('my_filt_name');
// Number of records to display on the screen
// Value of -1 lists all records in a table
$opts['inc'] = - 1;
// image url
$opts['url'] = array('images' => PATH_IC . 'imgIClist/');
$opts['navigation'] = 'UG';
$opts['filter_btn'] = 'small'; // show small filter buttons
$n_row_btn = array('function' => 'nrow_btn');
$cr_lf = array('code' => '<br />');
$opts['buttons']['L']['up'] = array('<<', '<', 'goto_combo', '>', '>>', $cr_lf, $n_row_btn);
$opts['buttons']['F']['up'] = $opts['buttons']['L']['up'];
$opts['buttons']['A']['up'] = array('save', 'cancel');
$opts['buttons']['C']['up'] = array('save', 'cancel');
$opts['buttons']['P']['up'] = array('save', 'cancel');
$opts['buttons']['D']['up'] = array('save', 'cancel');
$opts['buttons']['V']['up'] = array('change', 'delete', 'cancel');
$opts['buttons']['L']['down'] = $opts['buttons']['L']['up'];
$opts['buttons']['V']['down'] = $opts['buttons']['V']['up'];
$opts['buttons']['F']['down'] = $opts['buttons']['F']['up'];
// buttons for view/change if not owner of the record
$opts['alt_buttons']['V']['up'] = $opts['alt_buttons']['V']['down'] = array('cancel');
$opts['alt_buttons']['C']['up'] = $opts['alt_buttons']['C']['down'] = array('cancel');
// Display special page elements
$opts['display'] = array('form' => true, 'query' => false, 'sort' => true, 'time' => false, 'tabs' => true);
$opts['display']['xtabs'] = 'up'; // location of tabs at top of table
$opts['display']['xnav'] = array('V' => false, 'C' => true, 'P' => true, 'D' => true); // graphic nav links in list page
$opts['display']['xnavtip'] = array('V' => 'showDetails', 'C' => 'changeRecord', 'P' => 'loadFilter', 'D' => 'delRecord');
// Set default prefixes for variables
$opts['js']['prefix'] = '';
$opts['dhtml']['prefix'] = '';
$opts['cgi']['prefix']['operation'] = '';
$opts['cgi']['prefix']['sys'] = '';
$opts['cgi']['prefix']['data'] = '';
$opts['filters'] = "my_filt_fk_oid='$ID' AND (my_filt_fk_uid='$uid' OR my_filt_public='1')";
// Table field settings
$opts['fdd']['my_filt_id'] = array( //0
'options' => ''
// auto increment
);
$opts['fdd']['my_filt_fk_uid'] = array( //1
'name' => 'Owner', 'options' => 'AV', 'input|A' => 'H', 'default' => '', 'values|V' => array('db' => $_SESSION['DB_IC'], 'table' => 'info_user', 'column' => 'user_id', 'filters' => "user_id=$uid", 'description' => array('columns' => array('0' => 'user_fname', '1' => 'user_mname', '2' => 'user_lname'), 'divs' => array('0' => ' ', '1' => ' ')),));
$opts['fdd']['my_filt_fk_oid'] = array( //2
'options' => 'A', 'input' => 'H', 'default' => '');
$opts['fdd']['my_filt_filt'] = array( //3
'options' => 'AC', 'input' => 'H',
//  'input|C'  => 'R',
'default' => '', 'textarea' => array('rows' => 3, 'cols' => '40" style="width:100%'), 'sql' => 'CONCAT("")');
$opts['fdd']['my_filt_name'] = array( //4
'name' => 'Name', 'select' => 'T', 'size' => '40" style="width:100%', 'colattrs|L' => 'width="30%"', 'maxlen' => '255', 'required' => true, 'sort' => true);
$opts['fdd']['my_filt_desc'] = array( //5
'name' => 'Description', 'select' => 'T', 'maxlen' => 255, 'colattrs|L' => 'width="50%"', 'textarea' => array('rows' => 3, 'cols' => '40" style="width:100%'),);
$yes = array('en' => 'yes', 'de' => 'ja', 'nl' => 'ja');
$no = array('en' => 'no', 'de' => 'nein', 'nl' => 'nee');
$opts['fdd']['my_filt_public'] = array( //6
'name' => 'Public', 'select' => 'D', 'checkbox' => true, 'default' => '0', 'colattrs|L' => 'width="10%" align="center"', 'maxlen' => 1, 'sort' => true, 'values2' => array('0' => $no[$lang], '1' => $yes[$lang]));
$opts['triggers']['insert']['after'] = 'setID';
function setID($id)
{
	if (!$id || !$rpscript) return true;
	$_SESSION[$rpscript . '_rp']['filtID'] = $id;
	return true;
}
// table
require_once PATH_IC.'extensions/phpMyEdit-htmlcal.class164js.php';
$myedit = new phpMyEdit_htmlcal($opts);
if ($act == 'saveadd' && strlen($rpscript)) echo '<script>var lnk=opener.location.href.split("?");opener.location.replace(lnk[0]+"?rp=1");</script>';
ob_flush();
?>
</div></td></tr></table>
</BODY>
</HTML>
