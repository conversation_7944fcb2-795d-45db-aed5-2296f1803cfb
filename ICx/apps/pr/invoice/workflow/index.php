<?php
/**
 *  provides interface and methods to link invoices to related purchase orders and to sign for payment
 *  is used by AD/LAD and D/DD
 *  input: scanned invoices in pdf format available on scan folders of AD. Because these are network folders,
 *         the access is done via a server running as localhost with permissions of user dnw\ic_user
 *  output: records in database apps.invoices with link details
 *  by: <PERSON><PERSON>
 *  last modification: 01.04.2022
 *
 */
require("defines.php");
require 'IC2auth.inc.php'; // sets user id ($uid) and permission (rights), defines $script

switch ($rights) {
case 0: // no rights at all
	
case 1: // path view rights
	exit('<h3>Access denied!</h3>');
case 2: // read rights on object
	
case 3: // write rights
	
case 4: // workflow edit rights
	
case 5: // edit rights
	
case 6: // owner rights
	
case 7: // administrator group
	
case 8: // root user
	break;

default: // no rights at all
	exit('<h3>Access denied!</h3>');
}
$URL_IC = URL_ICx;
$scan_dir = PATH_SCAN_STORE;
$tbl_invc = 'apps.invoices';
$webix = '<script src="' . URL_ICx . 'webix/webix_debug1.7.js" type="text/javascript" charset="utf-8"></script>';
$webixCSS = '<link rel="stylesheet" href="' . URL_ICx . 'webix/skins1.7/air.css" type="text/css" media="screen" charset="utf-8">';
$process_js = 'process.js';
// banned chars
$remove = '/[\\x00-\\x19]/';
$debug = false;
if (0 && $uid == 47) {
	//$debug = true;
	$tbl_invc = 'apps.invoices_t';
	//$process_js = 'process_dev.js';
	
}
function paraf()
{
	// build the paraph string.
	$fn = $_SESSION['user_fname'];
	$fns = $_SESSION['user_fname_s'];
	$mn = $_SESSION['user_mname'];
	$ln = $_SESSION['user_lname'];
	$nf = $fn[0] . '.';
	if ($mn) {
		$mni = '';
		$mna = explode(' ', $mn);
		foreach ($mna as $v) $mni.= $v[0] . '.';
	}
	$nm = $mn ? $mni : '';
	$nl = $ln[0] . '.';
	return $nf . $nm . $nl;
}
//--------------- get permissions
require_once (PATH_ICx . 'lib/mysql_simple.inc.php');
require_once ('qc.php');

$qry = "SELECT * FROM apps.invoice_processing_perm";
list($id, $process200, $process400, $process500, $appr_ad, $appr_ad_oi, $appr_dd) = dbs_get($qry);
$books = array();
if ($id !== false && $process200) {
	$process = explode(',', $process200);
	if (in_array($uid, $process)) $books[] = 2;
}
if ($id !== false && $process400) {
	$process = explode(',', $process400);
	if (in_array($uid, $process)) $books[] = 4;
}
if ($id !== false && $process500) {
	$process = explode(',', $process500);
	if (in_array($uid, $process)) $books[] = 5;
}
if ($id !== false && $appr_ad) {
	$appr_ad = explode(',', $appr_ad);
	if (in_array($uid, $appr_ad)) $appr_ad = 1;
	else $appr_ad = 0;
}
if ($id !== false && $appr_dd) {
	$appr_dd = explode(',', $appr_dd);
	if (in_array($uid, $appr_dd)) $appr_dd = 1;
	else $appr_dd = 0;
}
//  if(!count($books) && !$appr_ad && !$appr_dd ) exit('No permission');
//--------------- ajax request for a specific scanned document
if (isset($_POST['getDoc']) && ($dir = $_POST['getDoc'])) {
	$dir = str_replace('.pdf', '', $dir); // remove *.pdf* to get folder name
	$img_dir = $scan_dir . "$dir"; // configuration
	if(is_dir($img_dir)) $file_r = scandir($img_dir); else $file_r = false;
	if(is_array($file_r)){
		$files = array_diff($file_r, array('..', '.'));
		if (count($files)) {
			foreach ($files as $img) { // could be more than one page
				list($w, $h) = getimagesize("$img_dir/$img");
				$img = query_encode('src', "$img_dir/$img");
				echo "<img src='$URL_IC../scan/?sq=$img' class=img data-w=$w data-h=$h onload='scaleImg(this)'>";
			}
		}
	}
	exit;
}
//--------------- ajax request for requesting specific document data
if (isset($_POST['getData']) && ($rec = $_POST['getData'])) {
	require_once ('qc.php');
	require_once (PATH_ICx . 'lib/mysql_simple.inc.php');
	$qry = "SELECT invc_data FROM $tbl_invc WHERE invc_id='$rec'";
	list($data) = dbs_get($qry);
	if ($data === false) {
		$data = '{error:"mysql_error(@124)"}';
	} elseif (!$data) $data = '{}';
	$data = json_decode($data, true);
	$part = (int)$_POST['part'];
	if (!$part) $part = 0;
	$ret = $data['lnk'][$part];
	$ret['invId'] = $data['invId'];
	$ret['booking'] = $data['booking'];
	exit(json_encode($ret));
}
//--------------- ajax request to save changes
if (isset($_POST['saveData']) && ($save = $_POST['saveData']) && ($rec = $_POST['saveTo'])) {
	$data = json_decode($save, true);
	//check for data complete
	$recc = $data['id'];
	$part = $data['part'];
	$status = $data['status'];
	$remark = $data['remark'];
	if ($rec != $recc) exit('data tampered with.');
	// get current record
	$qry = "SELECT invc_data,invc_ready_to_flow FROM $tbl_invc WHERE invc_id='$rec'";
	$res = dbs_get($qry);
	if ($res) {
		$invc_data = json_decode($res[0], true);
		$po_data = $invc_data['lnk'];
		$flow = explode(',', $res[1]);
		// update with new values
		if (strlen($status)) {
			$po_data[$part]['rem'] = $remark;
			if ($status == 'IA') { // invoice accepted
				$po_data[$part]['sign'] = date('d-m-Y H:i') . ', ' . paraf();
				$flow[$part] = 'A';
			} elseif ($status == 'IR') { // invoice rejected
				$flow[$part] = 'R';
				$po_data[$part]['sign'] = '';
				$po_data[$part]['rejected'] = date('d-m-Y H:i') . ', ' . paraf();
			}
			$invc_data['lnk'] = $po_data;
			$invc_data = json_encode($invc_data);
			$flow = join(',', $flow);
		}
	}
	$invc_data = addslashes($invc_data);
	$qry = "UPDATE $tbl_invc SET invc_ready_to_flow='$flow', invc_data='$invc_data', invc_lup_by='$uid' WHERE invc_id='$rec'";
	if (dbs_update($qry)) exit('done ' . $rec);
	else exit(dbs_update(false));
}
//--------------- ajax request for user names
if (isset($_POST['getUsers'])) {
	$tbl_user = 'infocenter.info_user';
	$qry = "SELECT user_id, user_lname, user_mname, user_fname_s FROM $tbl_user WHERE user_active AND NOT user_sysaccount AND user_func ORDER BY user_lname";
	$all = dbs_get_all($qry);
	$ret = array('0: ');
	foreach ($all as $user) {
		$ret[] = $user['user_id'] . ':' . $user['user_lname'] . ', ' . $user['user_fname_s'] . ($user['user_mname'] ? ' ' . $user['user_mname'] : '');
	}
	exit(join(';', $ret));
}
//--------------- ajax request to list available invoices for processing
if ($_GET['get'] == 'myInvoices') {
	// read signing permissions
	$tbl_user = 'infocenter.info_user';
	$qry = "SELECT user_id as 'uid' FROM $tbl_user WHERE user_signing = '$uid'";
	$users = dbs_get_all($qry);
	foreach($users as $user){
		$all_uid[] = $user['uid'];
		$find_all_uid[] = 'FIND_IN_SET('.$user['uid'].',fk_buyer_id)';
	}
	$row = array();
	if(isset($find_all_uid)) {
		$find_all_uid = join(' OR ', $find_all_uid);
		// read the invoices
		$qry = "SELECT invc_id, invc_po_no as 'PO',invc_ready_to_flow as 'flow',fk_buyer_id as 'id',invc_data as 'data',invc_doc as 'scan' FROM $tbl_invc WHERE NOT invc_paymnt_prepared AND ($find_all_uid) AND invc_ready_to_flow REGEXP '[0-9R]'";
		$invoices = dbs_get_all($qry);

		foreach ($invoices as $invoice) {
			$invoice['data'] = preg_replace($remove,'',$invoice['data']);
			//echo '<pre>', $invoice['data'];
			//echo '<pre>',$invoice['invc_id'],"; {$invoice['flow']}","; {$invoice['id']}; ","{$invoice['scan']}; ",print_r(json_decode($invoice['data'],true),1);
			$PO = explode(',', $invoice['PO']);
			$flow = explode(',', $invoice['flow']);
			$id = explode(',', $invoice['id']);
			$scan = str_replace($scan_dir, '', $invoice['scan']);
			$data = json_decode($invoice['data'], true);
			$comp = $data['supplier'];
			foreach ($flow as $i => $fl) {
				if ($fl == '-' OR $fl == 'A') {
					continue;
				}
				if (!in_array($id[$i], $all_uid)) {
					continue;
				}
				$row[] = array("invcId" => $invoice['invc_id'], "count" => $i, "PO" => $PO[$i], "invc" => $scan, "lnkPR" => $data['lnk'][$i]['prId'], "Comp" => $comp, "sign" => $data['lnk'][$i]['sign'], "Rem" => $data['lnk'][$i]['rem'], "stat" => $fl);
			}
		}
	}
	if ($debug) file_put_contents('debug_workflow.txt', date("Y-m-d H:i:s") . " $uid ({$_SESSION[user_lname]}):\n" . print_r($invoices, 1) . "\n" . print_r($row, 1) . "\n\n", FILE_APPEND);
	if (count($row)) {
		$txt = array('en' => 'Please check and sign following invoices:', 'de' => 'Bitte folgende Rechnungen &uuml;berpr&uuml;fen und ggf. abzeichnen:', 'nl' => 'A.u.b. volgende facturen checken en paraferen:');
		$bg = strstr($_SESSION['BGROUND'], '#') ? 'background-color:' . (!empty($m_portal) ? '#F2F2F2' : $_SESSION['BGROUND']) : 'background-image:' . $_SESSION['BGROUND'];
		$echo1 = <<<HTML1
  <!DOCTYPE HTML>
  <HTML>
    <HEAD>
      <meta http-equiv="content-type" content="text/html; charset=UTF-8">
      <style>
        body{{$bg}}
        .invc-h{font:0.9em sans-serif;font-weight:bold}
        .invc-li{font:0.9em sans-serif;}
        .r{color:red}
        li.r a{color:red}
      </style>
      <script>
        var PRhead = parent.document.querySelector('.PRhead');
        var PRmain = parent.document.querySelector('.PRmain');
        var prTogl = function(a){
          if(PRhead.style.display=='none'){
            PRhead.style.display="";
            PRmain.style.display="";
          }
          else{
            location.href='?get=myInvoices&invcProcess='+a;
          }
        }
      </script>
      </head>
      <body>
      <div class=invc-h>{$txt[$_SESSION['language']]}</div>
      <div class=invc-li>
HTML1;
		$i = 0;
		$echoCont = $echo = '<ul>';
		foreach ($row as $r) {
			if ($r['stat'] == 'R') {
				$c = "class='r'";
				$t = "title='open rejected invoice'";
			} else {
				$c = '';
				$t = "title='open invoice'";
			}
			$echoCont.= "<li $c onclick='prTogl($i);' $t><a href='#'>PO {$r['PO']}: {$r['Comp']}</a></li>";
			$echo.= "<li $c id='doc_$i' onclick='getDoc(this);' $t><a href='#'>PO {$r['PO']}: {$r['Comp']}</a></li>";
			$i++;
		}
		$echoCont.= '</ul>';
		$echo.= '</ul>';
		$echo1.= $echoCont . '</div></body></html>';
		if (!isset($_GET['invcProcess'])) exit("$echo1");
		$data = json_encode($row);
	} else exit;
}
//--------------- build the page -----------------------------------
$msg1 = array('en' => 'Back to PRs', 'de' => 'Back to PRs', 'nl' => 'Back to PRs');
$msg2 = array('en' => 'Go back to Purchase Requests', 'de' => 'Zur&uuml;ck zu den Purchase Requests', 'nl' => 'Go back to Purchase Requests');
$help1 = array('en' => 'Check this field if you accept the invoice', 'de' => 'Dieses Feld ankreuzen, wenn die Rechnung akzeptiert wird.', 'nl' => 'Check this field if you accept the invoice');
$help2 = array('en' => 'Check this field if you reject the invoice, because items received or costs are not as ordered.', 'de' => 'Dieses Feld ankreuzen, wenn die Rechnung zur&uuml; wird, weil Lieferung oder Kosten nicht der Bestellung entsprechen.', 'nl' => 'Check this field if you reject the invoice, because items received or costs are not as ordered.');
$lang = $_SESSION['language'];
$msg1 = $msg1[$lang];
$msg2 = $msg2[$lang];
$help1 = $help1[$lang];
$help2 = $help2[$lang];
echo <<<HTML
  <!DOCTYPE HTML>
  <HTML>
    <HEAD>
      <meta http-equiv="content-type" content="text/html; charset=UTF-8">
      <title>Invoice processing</title>
      $webixCSS
      <link rel="stylesheet" href="{$URL_IC}js/lib/g2p.css" type="text/css" media="screen" charset="utf-8">
      <style>
        body{font:9pt Arial,sans-serif;padding:0;background-color:#f1f1f1}
        #iv{width:49.5%;height:99%;overflow:auto;text-align:center;position:absolute;top:0px;left:0px;border:1px solid silver}
        #slider{position:absolute;top:5px;left:13%;vertical-align:middle;z-index:1000}
        #parm{position:absolute;top:0px;left:50%;height:200px;width:49.5%;overflow:auto}
        #prView{position:absolute;left:50%;height:790px;width:49.5%;top:209px;border:1px solid silver;}
        #input{position:relative;display:none;width:100%;height:100%;overflow:auto;background-color:#f9f9f9}
        #invoices{position:relative;width:100%;height:100%;overflow:auto}
        #procMessage{width:100%;text-align:center;display:none}
        #procMessage p{font-weight:bold;margin-top:8px;}
        #procMessage img{vertical-align:middle}
        #rejDate{color:red;margin-bottom:4px}
        #tglBtn{margin-left:28px}
        #propsBtn{margin-left:20px;width:450px;clear:both}
        #comment_{background-color:#FFFFCC}
        .parm{overflow:auto;background-color:white;border:1px solid silver}
        .props{padding-left:20px;font-weight:bold}
        .props_w{width:470px}
        .props_w textarea{width:100%;height:50px;font:13px sans-serif}
        .props div{display:inline;width:230px;float:left;font:9pt Arial,sans-serif;}
        .props span{padding-left:10px;padding-right:30px;font-weight:normal;line-height:25px;white-space:nowrap;vertical-align:middle}
        .a{cursor:pointer;color:blue;padding:5px;display:inline}
        .accept{background-color:green;color:white;padding:5px !important;cursor:pointer;}
        .reject{background-color:red;color:white;padding:5px !important;cursor:pointer}
        label{;cursor:pointer}
        .r{color:red}
        li.r a{color:red}
        .img{width:900px;height:1272px;position:relative}
        .webix_template{padding:0}
        .webix_el_text input, .webix_inp_static, .webix_el_combo input{padding:0 5px;font-size:9pt;}
        .webix_inp_static{padding-top: 2px;}
        .webix_el_checkbox{text-align:right}
        .webix_list_item{font-size:9pt}
        .addPO{color:silver;cursor:pointer}
        .delX{vertical-align:middle;color:red;font-weight:bold;cursor:pointer}
        .webix_view > .webix_disabled{
            filter:progid:DXImageTransform.Microsoft.Alpha(opacity=40);
            height:24px;
          }
        .rot0{
          left:0px !important;
         }
        .rot90{
          -moz-transform: rotate(90deg);
          -ms-transform: rotate(90deg);
          -o-transform: rotate(90deg);
          -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
         }
        .rot270{
          -moz-transform: rotate(270deg);
          -ms-transform: rotate(270deg);
          -o-transform: rotate(270deg);
          -webkit-transform: rotate(270deg);
          transform: rotate(270deg);
         }

      </style>
      <script src='{$URL_IC}js/lib/utils.js' type="text/javascript" charset="utf-8"></script>
      <script src='{$URL_IC}js/lib/include.js' type="text/javascript" charset="utf-8"></script>
      $webix
      <script>
        var URL_IC = '{$URL_IC}';
        var processJS = '{$process_js}';
        var help1 = '{$help1}';
        var help2 = '{$help2}';
        var prDataUrl= URL_IC+'apps/pr/';
        var data = $data;
        var called="{$_GET['invcProcess']}";
        var PRhead = parent.document.querySelector('.PRhead');
        var PRmain = parent.document.querySelector('.PRmain');
        var prTogl = function(a){
          if(PRhead.style.display=='none'){
            PRhead.style.display="";
            PRmain.style.display="";
            location.href='?get=myInvoices';
          }
          else{
            location.href='?get=myInvoices&invcProcess='+a;
          }
        }
      </script>
    </HEAD>
  <BODY>
    <div id=iv></div>
    <div id=parm class=parm>
      <div id=invoices>$echo <button id="tglBtn" type=button onclick="prTogl();" title="{$msg2}">{$msg1}</button></div>
      <div id=input></div>
    </div>
    <iframe name="prView" id="prView" src='{$URL_IC}empty.html' allowTransparency="true" frameborder=0></iframe>
    <div id="slider"></div>
    <div id="procMessage"><p><img src='{$URL_IC}images/ajax-loader.gif'> Processing invoices ...</p></div>
HTML;

?>


<script src='../rotate.js' type="text/javascript" charset="utf-8"></script>
<script type="text/javascript" charset="utf-8">
  document.querySelector('#procMessage').style.display='block';
  if (!webix.env.isIE || (webix.env.isIE && navigator.appVersion.match(/MSIE \d+./)[0].replace(/\D/g,'')>9)){include.js(URL_IC+'js/lib/g2p.min.js',function(){include.js(processJS);})}
  else include.js(processJS);
  PRhead.style.display="none";
  PRmain.style.display="none";
</script>
</BODY>
</HTML>
