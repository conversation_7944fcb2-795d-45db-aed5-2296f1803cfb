<?php
session_start();
$path = './;../.;../../;../../../;apps/';
set_include_path(get_include_path() . PATH_SEPARATOR . $path);
require_once 'IC2_db_con.inc.php';
class clsImapHandler
{
	var $login = ""; // The login name
	var $password = ""; // Password to log in into the account
	var $servername = "localhost"; // the server to access
	//	var $protocol = "110/pop3"; // default pop protocol
	var $protocol = "143/tls"; // default imap protocol
	var $folder = ""; // default folder
	var $mbox; // internal use - the mbox identifier
	var $Messages; // the number of messages in the folder
	var $dispos; // internal use - used to process attachments
	var $currMsg; // internal use - the number of current processing message
	/*
	Constructor.
	Sets up the internal fields.
	Receives as parameters:
	
	login 			: as explained above
	password		: as explained above
	server			: optional. allows to set a explicit server to access
	proto			: the protocol to use
	folder			: the folder to access
	*/
	function clsImapHandler($login, $password, $server = "", $proto = "", $folder = "")
	{
		$this->login = $login;
		$this->password = $password;
		if (!empty($server)) $this->servername = $server;
		if (!empty($proto)) $this->protocol = $proto;
		if (!empty($folder)) $this->folder = $folder;
		$this->mbox = - 1;
		$this->Messages = 0;
		$this->dispos = 0;
	}
	/*
	function Open
	
	used to establish a connection to the mail server. Once connected succesfully, (it retrieves 
	the number of messages), sets the mailbox identifier, and returns true. If unsuccesfull, 
	returns false
	*/
	function open()
	{
		$fqsn = "{" . $this->servername . ":" . $this->protocol . "}" . $this->folder;
		$_mbox = imap_open($fqsn, $this->login, $this->password);
		if ($_mbox) {
			//		$_headers=imap_headers($_mbox);
			//		$this->Messages=imap_num_msg($_mbox);
			$this->mbox = $_mbox;
			return true;
		} else {
			return false;
		}
	}
	/*
	Function Close
	
	used to close the connection to the server
	*/
	function close()
	{
		imap_close($this->mbox);
	}
}
//#####################################
// Characterset decoding              #
//#####################################
function mimeHeaderDecode($string)
{
	if (strstr($string, "=?")) {
		$explode = explode("=?", $string);
		$newString = $explode[0];
		for ($i = 1; $i < sizeof($explode); $i++) {
			preg_match('/\?([Qq]|[Bb])\?/', $explode[$i], $matches); //echo '<pre>',print_r($matches,1),'</pre>';
			list($charcoding, $subj) = explode($matches[0], $explode[$i]);
			list($subj, $rest) = explode('?=', $subj);
			$str = (strtolower($matches[0][1]) == "q") ? quoted_printable_decode(strtr($subj, '_', ' ')) : base64_decode($subj);
			switch (strtolower($charcoding)) {
			case 'utf-8':
				$str = utf8_decode($str);
				break;
			}
			$newString.= $str . $rest;
		}
	} else return $string;
	return $newString;
}
function dump_var($theVar, $indent = 0)
{ // for debug only
	$theVar = (array)$theVar;
	while (list($k, $v) = each($theVar)) {
		for ($p = 0; $p < ($indent * 4); $p++) echo "&middot;";
		//		$ks = int $k;
		if ($k >= 0) $s = '#';
		echo "<b> $s $k:</b> ";
		if (is_array($v) or is_object($v)) {
			echo (is_array($v) ? "Array (" . count($v) . ") elements" : "Object") . "<br>";
			dump_var($v, $indent + 2);
		} else {
			echo htmlspecialchars($v) . "<br>";
			//			echo $v."<br>";
			
		}
	}
}
$user = "dnw\infocenter";
$pw = "2005&IC";
$server = "owa.dnw.aero";
$mtbl = 'info_object_mail';
$tbl = "infocenter.$mtbl";
checkMail();
exit('ready');
function checkMail()
{
	global $user, $pw, $server, $mtbl, $tbl;
	$mailbox = new clsImapHandler($user, $pw, $server);
	if (!$mailbox->open()) {
		print ('<script>alert("Could not establish a connection to the mail server!")</script>');
		return;
	}
	$mbox = $mailbox->mbox;
	$headers = $structure = $msguid = array();
	$unseen = array();
	//echo "The mail account has $msgno messages<br>";
	$unseen = imap_search($mbox, 'FLAGGED');
	echo 'Updated:<pre>', print_r($unseen, 1), '</pre><br>';
	if (!empty($unseen)) {
		foreach ($unseen as $msgnr) if ($msgnr) {
			$structure["$msgnr"] = imap_fetchstructure($mbox, $msgnr);
			$msguid["$msgnr"] = imap_uid($mbox, $msgnr);
		}
		//	$status = imap_setflag_full($mbox, join(',',$unseen), "\\Seen \\Flagged"); // indicate that message is recorded to database
		$new = count($unseen);
		echo $new . ' message' . ($new > 1 ? 's' : '') . ' read!';
	} else {
		//print('<script>alert("No new messages on the mail server!")</script>');
		
	}
	foreach ($structure as $msgnr => $p) {
		$parts = $p->parts;
		foreach ($parts as $i => $part) {
			if (strtolower($part->disposition) == 'attachment') {
				$at["$msgnr"]['filename'][$i] = mimeHeaderDecode($part->dparameters[0]->value);
			}
		}
	}
	//		if($_SESSION['user_id']==47) {
	//			echo "The headers are: <pre>",print_r($headers,1),'</pre>';
	//			echo "The uids are: <pre>",print_r($msguid,1),'</pre>';
	//			echo "The structures are: <pre>",print_r($structure,1),'</pre>';
	//			echo "The attachments are: <pre>",print_r($at,1),'</pre>';
	//		}
	if ($new) {
		require_once 'db_util.inc.php';
		foreach ($structure as $msgnr => $header) {
			if (!count($at[$msgnr]['filename'])) continue;
			//				echo("<script>alert('DB #".addslashes(print_r($header,1))."')</script>");
			//				$val['object_mail_date'] = date('Y-m-d H:i:s', $header->udate);
			//				$val['object_mail_subject'] = mimeHeaderDecode($header->subject);
			//				$val['object_mail_from_p'] = mimeHeaderDecode($header->from[0]->personal);
			//				$val['object_mail_from_h'] = $header->from[0]->host;
			//				$val['object_mail_size'] = $header->Size;
			//				$val['object_mail_msguid'] = imap_uid($mbox,$msgnr);
			$where = 'object_mail_msguid = "' . $msguid[$msgnr] . '"';
			$val['object_mail_at'] = count($at[$msgnr]['filename']);
			$val['object_mail_attachments'] = join(', ', $at[$msgnr]['filename']);
			echo '<br>' . $val['object_mail_at'] . ' attachment(s) found in message #' . $msgnr;
			$ret = db_update('infocenter', $mtbl, $val, $where, array('object_mail_at', 'object_mail_attachments'));
			if ((int)$ret) {
				echo '<br>message #' . $msgnr . ' updated with attachments ' . $val['object_mail_attachments'];
				//					$msguid = $val['object_mail_msguid'];
				//					imap_setflag_full($mbox, "$msguid", "\\Seen",ST_UID);
				
			} else echo ("<script>alert('" . addslashes($ret) . "')</script>");
		}
	}
	$mailbox->close();
	return;
}
?>
