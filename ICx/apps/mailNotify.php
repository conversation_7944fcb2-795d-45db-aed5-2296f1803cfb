<?php
use DNW\DNW_User;

require_once 'DNW_User.php';
$DNW_user = new DNW_User();
define('PATH_IC', '../');
require PATH_IC . 'IC2auth.inc'; // sets user id ($uid) and permission (rights), defines $script
$lang = $_SESSION['language'];
if (empty($lang)) {
	$lang = $_SERVER['HTTP_ACCEPT_LANGUAGE'];
	$lang = strtolower(substr($lang, 0, 2));
}
require_once PATH_IC . 'nameUtils.inc.php';
$nf = trim($_SESSION['user_fname']);
$nf = $nf[0] . '. ';
$nm = trim($_SESSION['user_mname']);
$nm = $nm ? $nm[0] . '. ' : '';
$nl = trim($_SESSION['user_lname']);
$sender = array('mailaddr' => $_SESSION['email_addr'], 'name' => $nf . $nm . $nl);
$ret = '';
//$to = utf8_decode($_POST['To']);
//$cc = utf8_decode($_POST['CC']);
$to = $_POST['To'];
$cc = $_POST['CC'];
$subject = $_POST['Subject'];
$plainbody = $_POST['plainbody'];
$htmlbody = $_POST['htmlbody'];
$htmlbody.= "\n\nThis mail was generated by InfoCenter Notifications.";
$a = explode(';', $to);
// check for valid e-mails. Recipients are in array $a.
$mail = $DNW_user->getUserEmailByName($a);
if ($mail[0] === false) $ret.= $mail[1];
else $to = $mail;
if (strlen($cc)) {
	$a = explode(';', $cc);
	// check for valid e-mails. Recipients are in array $a.
	$mail = $DNW_user->getUserEmailByName($a);
	if ($mail[0] === false) $cc = '';
	else $cc = $mail;
}
if ($ret) die('No or invalid e-mail address. No mail submitted.');
$cc = (array)$cc;
array_push($cc, '<EMAIL>');
require_once (PATH_IC . 'IC2mailer.php');
$res = IC2smtp_mail($to, $cc, $subject, $plainbody, nl2br($htmlbody), '', '', $sender);
if ($res !== true) {
	die('Mail error: ' . $res . join('; ', (array)$to));
}
die("Mail sent to:\n" . join('; ', (array)$to) . "\n" . join('; ', (array)$cc) . "\n" . $plainbody);
?>