<?php
use DNW\DNW_User;

require_once 'DNW_User.php';
$DNW_user = new DNW_User();
function alert($str = '')
{
	$str = preg_replace(array('/\n/', '/\s/', "/'/"), array('\n', ' ', "\\'"), $str);
	echo "<script>alert('$str');</script>";
}
$fn = $_SESSION['user_fname'];
$mn = $_SESSION['user_mname'];
$ln = $_SESSION['user_lname'];
$lang = $_SESSION['language'];
$msg = '';
// extract facility
$facar = explode('/', $fac);
$facar = $facar[0];
// get company address/fax
mysql_select_db('apps');
$qry = "
	SELECT address,postal_code,town, fax,fk_country_id
	FROM dnw_locations,dnw_facilities
	WHERE location_ID=fk_location_id AND facility_name LIKE '$facar'
";
$rs = mysql_query($qry);
if ($row = mysql_fetch_row($rs)) {
	list($address, $p_code, $town, $fax, $country) = $row;
} else exit('no valid facility');
// $country: 3=NL => language=EN, 4=DE
$country == 3 ? $country = 'EN' : $country = 'DE';
//###########################
// look for submitted form  #
//###########################
if (stristr($_POST['expo'], 'exp') || stristr($_POST['expo'], 'e-mail')) { // used for project evaluation form
	$export = substr($_POST['expo'], 0, 6);
	if (strstr($_POST['expo'], 'DE')) $expcountry = 'DE';
	else $expcountry = 'EN';
	unset($_POST['expo']);
	// if export == expwrd don't save a form
	if ($export == 'expwrd') $_POST['send'] = '';
	// if not a valid e-mail address don't save anything
	if ($export == 'e-mail') {
		if (!preg_match('/\b[A-Za-z0-9._%-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,4}\b/', $_POST['mailaddr'])) {
			echo alert("Invalid e-mail address");
			$_POST['send'] = '';
			$export = '';
			$_POST['mailaddr'] = '';
		}
	}
	if ($_POST['send'] == 'ready') {
		$old_formuid = $_POST['formuid'];
		$formuid = md5(uniqid(rand(), true));
		$final = $formuid;
	}
}
//####################################
// get and store the new form data   #
//####################################
if ($_POST['send'] == 'ready' || ($sign = $_POST['send']) == 'sign') {
	if ($pk_req && ($_SESSION[$script . '_pagekey'][$pkey] != $_REQUEST['pk'][$pkey])) exit('<script>self.close();alert("Error: Invalid form submittal! Form not saved.");</script>');
	$_POST['final'] && $final = true;
	unset($_POST['pk'], $_POST['final'], $_POST['send'], $_POST['sq']);
	$a0 = array();
	foreach ($_POST as $key => $val) $a0[$key] = $val;
	// if sign, then $a0['has_signed'] contains the department string where signing was performed.
	if ($sign && $a0['has_signed']) {
		$signed_for = $a0['has_signed'];
	}
	if (isset($a0['has_signed'])) unset($a0['has_signed']); // don't save this field
	// look whether record exists
	if ($fid && mysql_num_rows(mysql_query("SELECT form_status FROM proj_forms WHERE fk_project_id='$rec' AND form_id='$fid'"))) {
		// update with lock table
		if (mysql_query("LOCK TABLES proj_forms WRITE")) {
			// get the current data set
			list($cont, $frm_status) = mysql_fetch_row(mysql_query("SELECT form_content,form_status FROM proj_forms WHERE fk_project_id='$rec' AND form_id='$fid'"));
			$ac = (array)$a0 + (array)unserialize($cont); // update only the content part, which is just submited
			foreach ($ac as $k => $v) if (!strlen($v)) unset($ac[$k]); // remove empty elements
			$s = mysql_real_escape_string(serialize($ac));
			if (!$frm_status || (strlen($final) > 2)) $frm_status = $final; // if $final = $formuid, save in any case
			if ($reset) $frm_status = '';
			if (!$frm_status && $sign) $msg = '<script>alert("Error: Form not final to sign! Form not updated.");</script>';
			elseif (mysql_query("UPDATE proj_forms SET form_content='$s',form_fk_user_id='$uid',form_status='$frm_status' WHERE fk_project_id='$rec' AND form_id='$fid'")) $msg = '<script type="text/javascript">if(opener && opener.refresh_form)try{opener.refresh_form()}catch(e){};alert("Form updated.");</script>';
			mysql_query("UNLOCK TABLES");
		} else $msg = '<script>alert("Error: Could not lock database! Form not updated.");</script>';
	}
	// insert a new record
	else {
		if (mysql_query("INSERT INTO proj_forms SET form_content='$s',fk_project_id='$rec',fk_form_tpl_id='$tpl',form_fk_user_id='$uid',form_status='$final'")) {
			$fid = mysql_insert_id();
			$sq0 = query_encode('fac', $fac, 'rec', $rec, 'tpl', $tpl, 'fid', $fid, 'pkr', $pk_req);
			$a0['sq'] = $sq0;
			$s = mysql_real_escape_string(serialize($a0));
			mysql_query("UPDATE proj_forms SET form_content='$s' WHERE form_id='$fid'");
			$msg = '<script type="text/javascript">alert("Form saved.");</script>';
		} else $msg = '<script>alert("Error: Could not insert. Form not saved!");</script>';
	}
	$_SESSION[$script . '_pagekey'][$pkey] = rand(); // add page key to cgi

}
//######################################################
// get and store the new form data during edit session #
//######################################################
elseif ($_POST['send'] == 'draft') {
	$draft = true;
	unset($_POST['pk'], $_POST['final'], $_POST['send'], $_POST['sq']);
}
//#################################
// handle request to reset a form #
//#################################
elseif ($_POST['send'] == 'reset' && $_POST['res']) {
	$frm_status = '';
	list($cont, $rem) = mysql_fetch_row(mysql_query("SELECT form_content,form_reminder FROM proj_forms WHERE fk_project_id='$rec' AND form_id='$fid'"));
	$a = (array)unserialize($cont);
	foreach ($a as $k => $v) $a[$k] = rawurldecode($v);
	// look for permissions
	if ($a['paraf']) {
		require_once PATH_IC . 'nameUtils.inc.php';
		$perm_ar_str = explode(';', $a['paraf']);
		foreach ($perm_ar_str as $v) {
			if (!$v) continue;
			if (!strpos(trim($v), '=')) {
				$msg.= '<script>alert(unescape("Permission entry \'' . rawurlencode($v) . '\' invalid !\\nFor each department it should have the format like:\\nDEPARTMENT = name (e.g.: PA = ' . $ln . ';)"))</script>';
				break;
			}
			list($dptmt, $member) = explode('=', $v);
			$dptmt = trim($dptmt);
			if ($dptmt == '') continue;
			// if found, delete the paraphe
			$a[$dptmt] && ($a[$dptmt] = '');
		}
	}
	$a['final'] && ($a['final'] = '');
	$s = mysql_real_escape_string(serialize($a));
	if (mysql_query("UPDATE proj_forms SET form_content='$s',form_fk_user_id='$uid',form_status='$frm_status',form_reminder='' WHERE fk_project_id='$rec' AND form_id='$fid'")) {
		$msg = '<script type="text/javascript">if(opener && opener.refresh_form)try{opener.refresh_form()}catch(e){};alert("Form reset executed!");</script>';
		// delete reminder
		$rem = trim($rem);
		mysql_query("DELETE FROM apps.reminders WHERE rem_id='$rem' LIMIT 1");
	}
}
//#####################
// handle remind now! #
//#####################
elseif ($_POST['send'] == 'remind') {
	list($rem_id) = mysql_fetch_row(mysql_query("SELECT form_reminder FROM apps.proj_forms WHERE form_id='$fid'"));
	require_once PATH_IC . 'IC2reminder.php';
	if ($rem_id && remind($rem_id, 'now')) echo "<script>alert('Reminder sent!');</script>";
	exit();
}
//#####################################
// get and store the data in template #
//#####################################
elseif ($_POST['send'] == 'tpl') {
	if ($_POST['tpl']) {
		unset($_POST['pk'], $_POST['final'], $_POST['send'], $_POST['sq'], $_POST['tpl']);
		$a0 = array();
		foreach ($_POST as $key => $val) $a0[$key] = $val;
		$s = mysql_real_escape_string(serialize($a0));
		$msg = '<script>alert("Form template updated.");</script>';
		if (!mysql_query("UPDATE proj_forms_tpl SET form_tpl_content='$s' WHERE form_tpl_id='$tpl'")) $msg = '<script>alert("Error: Form template not updated.");</script>';
	} else $msg = '<script>alert("Template update disabled for this form!\n\nForm template not updated.");</script>';
}
//###############################
// start with building the form #
// 1) get the header data       #
//###############################
$qry = "
	SELECT project_testname, project_testnumber,project_number, ppm, ppm_id,pm,pm_id, dep_pm,dep_pm_id, Company, pm_client, DATE_FORMAT(project_start_date,'%d-%m-%Y'), DATE_FORMAT(project_end,'%d-%m-%Y'),project_duration,fk_test_class_id 
	FROM apps.projects,apps.companies
	WHERE Company_ID=fk_company_id AND project_id='$rec'
";
$rs = mysql_query($qry);
if ($row = mysql_fetch_row($rs)) {
	list($project_name, $tn, $project_number, $ppm, $ppm_id, $pm, $pm_id, $dpm, $dpm_id, $customer, $poc, $start, $end, $duration, $test_class) = $row;
} else exit('no valid project');
// if PM or deputy PM or admin, enable edit
$edt = $pm_edt = false;
if ($uid == 1) $edt = true; // admin
elseif ($pm_id) {
	if (in_array($uid, explode(',', $pm_id))) $edt = true;
} elseif (strlen($pm)) {
	require_once PATH_IC . 'nameUtils.inc.php';
	$pmar = preg_split('/[,;\/]/', $pm, -1, PREG_SPLIT_NO_EMPTY);
	foreach ($pmar as $name) if (in_array($uid, $DNW_user->getUserIdByName($name, 1, 1))) $edt = true;
}
if (!$edt) {
	if ($dpm_id) {
		if (in_array($uid, explode(',', $dpm_id))) $edt = true;
	} elseif (strlen($dpm)) {
		require_once PATH_IC . 'nameUtils.inc.php';
		$pmar = preg_split('/[,;\/]/', $dpm, -1, PREG_SPLIT_NO_EMPTY);
		foreach ($pmar as $name) if (in_array($uid, $DNW_user->getUserIdByName($name, 1, 1))) $edt = true;
	}
}
$edt && ($pm_edt = true);
//###################################
// 2) get the template and the data #
//###################################
if ($fid) { // read filled form; form id is record id in table proj_forms
	$qry = "
		SELECT form_tpl_path,form_tpl_file_name,form_tpl_hrn,form_tpl_pk,form_content,DATE_FORMAT(form_lup,'%d-%m-%y %H:%i'),form_status,form_tpl,form_tpl_source,form_tpl_rperm,form_tpl_sign,fk_form_tpl_id 
		FROM proj_forms_tpl 
		LEFT OUTER JOIN proj_forms ON fk_form_tpl_id=form_tpl_id AND fk_project_id='$rec'
		WHERE form_id='$fid'
	";
	$rs = mysql_query($qry);
	list($path, $file, $title, $pk_req, $content, $lup, $status, $tpl_code, $source, $req_perm, $sign_on, $tpl) = mysql_fetch_row($rs);
} else { // read empty template = new document
	$newdocument = true;
	$qry = "
		SELECT form_tpl_path,form_tpl_file_name,form_tpl_hrn,form_tpl_perm,form_tpl_pk,form_tpl_content,form_tpl,form_tpl_source,form_tpl_rperm, form_tpl_sign
		FROM proj_forms_tpl 
		WHERE form_tpl_id='$tpl' 
	";
	$rs = mysql_query($qry);
	list($path, $file, $title, $def_perm, $pk_req, $content, $tpl_code, $source, $req_perm, $sign_on) = mysql_fetch_row($rs);
}
// copy content from other project
if (strlen($_POST['copy']) == 9) {
	$copy_rec = $_POST['copy'];
	list($copy_rec) = mysql_fetch_row(mysql_query("SELECT project_id FROM projects WHERE project_number='$copy_rec'"));
	if ((int)$copy_rec) {
		$qry = "
			SELECT form_content 
			FROM proj_forms 
			WHERE fk_form_tpl_id='$tpl' AND fk_project_id='$copy_rec'
		";
		if (($rs = mysql_query($qry))) list($content) = mysql_fetch_row($rs);
		if ($content) {
			$a = @unserialize($content);
			$a = (array)$a;
			foreach ($a as $k => $v) $a[$k] = strstr($k, 'JSON') ? $v : rawurldecode($v);
			$a['ITG'] = '';
			$a['PM'] = '';
			$a['PMG'] = '';
			$a['LAD'] = '';
			$a['MTG'] = '';
			$a['TOG'] = '';
		}
	} else echo "<script>alert('Project not found');</script>";
} elseif ($content) {
	$a = @unserialize($content);
	$a = (array)$a;
	foreach ($a as $k => $v) $a[$k] = strstr($k, 'JSON') ? $v : rawurldecode($v); //strstr($k,'JSON') ? rawurldecode($v):rawurldecode($v);

}
//if($draft) { // if draft values available fill in these values
//	foreach($_POST as $key => $val) $a[$key] =  $val;//strstr($k,'JSON') ? $val: htmlspecialchars(rawurldecode($val),ENT_QUOTES);
//}
$sq = query_encode('fac', $fac, 'rec', $rec, 'tpl', $tpl, 'fid', $fid, 'pkr', $pk_req, 'edt', $edt);
!$lup && $lup = date('d-m-y H:i'); // used for print out only
if ($sign_on) { //
	//if(stristr($file,'wce')) {
	$wce = true; // work & cost estimation shall be processed
	// check required departments to sign -> $req_perm
	$rperma = explode(';', str_replace(' ', '', trim($req_perm)));
	if (!in_array('AD', $rperma)) $ad_dpmt = false;
	$a['final'] = $status;
	// look for type of duration (d,s,h)
	if (!$a['final']) {
		if (strpos($duration, 'd')) $a['days'] = (int)$duration;
		elseif (strpos($duration, 'h')) $a['hours'] = (int)$duration;
		elseif (strpos($duration, 's')) $a['shifts'] = (int)$duration;
		else $a['days'] = (int)$duration;
	}
	//# IMPORTANT! If work & cost estimation is saved by the PM once as final, changes can only be made by the persons, which have permission
	if ($a['final'] && $edt && $pm_edt = true) $edt = false;
} elseif (stristr($file, 'pes')) { // project evaluation sheet
	$pes = true;
	$formuid = $status;
}
//######################################################################################
// if the form is build the first time, set default users to edit & sign the wce form  #
//######################################################################################
if ($wce && $def_perm) {
	if ($def_perm) $a['paraf'] = $def_perm;
	else $msg.= '<script>alert("Default permission entry missing in database!\\nPlease, inform the InfoCenter administrator.\\n Thank you!")</script>';
	require_once PATH_IC . 'nameUtils.inc.php';
	$perm_ar_str = explode(';', $a['paraf']);
	// remove empty elements
	function remove_invalid_elements($e)
	{
		return (trim($e) != '');
	}
	$perm_ar_str = array_filter($perm_ar_str, 'remove_invalid_elements');
	$pa_entr_found = $pa_found = false;
	foreach ($perm_ar_str as $k => $v) { // look whether PA/PM is in permission list
		if (!$v) continue;
		if (!strpos(trim($v), '=')) {
			$msg.= '<script>alert(unescape("Permission entry \'' . rawurlencode($v) . '\' in database invalid !\\nFor each department it should have the format like:\\nDEPARTMENT = name (e.g.: PA = ' . $ln . ')\\nPlease, inform the InfoCenter administrator.\\n Thank you!"))</script>';
			break;
		} // only occures if database entry is wrong
		$dptmt = $member = '';
		list($dptmt, $member) = explode('=', $v);
		$dptmt = trim($dptmt);
		if ($dptmt == '') continue;
		if (in_array($dptmt, array('PA', 'PM', 'PMG'))) {
			$pa_entr_found = true;
			break;
		}
	}
	if ($pa_entr_found && strlen(trim($member))) {
		// check for valid PA names
		if (strpos($member, ',') !== false) {
			$ret = splitStr(',', $member);
			if ($ret[0] === false) return;

			$members = $ret;
		} else $members = (array)$member;
		foreach ($members as $member) {
			$user_id = $DNW_user->getUserIdByName($member,1,1 );
			if (!$user_id[0]) continue;
			else $pa_found = true;
		}
	}
	if ($pa_found == false) { //if a PA or PM entry not found, set the current user as PA (is for those facilities where the PM has to sign the form)
		if (!$pa_entr_found) { // extend the array
			$dptmt = (in_array(strtoupper($fac), array('NWB', 'TWG'))) ? 'PM' : 'PA'; // that is because NWB,TWG form uses PM and not PA
			$perm_ar_str[] = $dptmt . '=' . $pm;
		} else // replace the existing array element
		$perm_ar_str[$k] = $dptmt . '=' . $pm;
	}
	$a['paraf'] = join(';', $perm_ar_str);
}
//#################################################################################################################################
// if a form is final and requires signing and if the current user has permission then the form shall include the signing feature #
//#################################################################################################################################
if($wce && !$a['final']) {
    $perm_ar_str = explode(';', $a['paraf']);
    foreach ($perm_ar_str as $v) {
        if (!$v) continue;
        if (!strpos(trim($v), '=')) {
            $msg .= '<script>alert(unescape("Permission entry \'' . rawurlencode($v) . '\' invalid !\\nFor each department it should have the format like:\\nDEPARTMENT = name (e.g.: PA = ' . $ln . ';)"))</script>';
            break;
        }
        $found = false;
        list($dptmt, $member) = explode('=', $v);
        $dptmt = trim($dptmt);
        if ($dptmt == '') continue;
        if ($dptmt == 'AD') $ad_dpmt = $member;
        $perm_ar[$dptmt] = '';

        if (strpos($member, ',') !== false) {
            $ret = splitStr(',', $member);
            if ($ret[0] === false) break;

            $members = $ret;
        } else $members = (array)$member;
        // gather the user ids
        foreach ($members as $member) {
            $user_id = $DNW_user->getUserIdByName($member, 1, 1);
            if (!$user_id[0]) continue;
            if (in_array($user_id[0], $para->uids)) $found = true;
        }
        if (!$found) continue;
        $nm = $mn ? $mni : '';
        $nl = $ln{0} . '.';
        $perm_ar[$dptmt] = date("d.m.y") . ' / ' . $nf . $nm . $nl;

    }
}
if ($wce && $a['final']) {
	require_once PATH_IC . 'nameUtils.inc.php';
	$para = new elPara($uid);
	$para_msg_txt = array('en' => 'You may sign for:', 'de' => 'Sie d�rfen abzeichnen f�r:', 'nl' => 'U mag aftekenen voor:');
	$perm_ar_str = explode(';', $a['paraf']);
	$no_tplsave = false;
	// generate the permission array (array of fields were the current user have permission to sign)
	foreach ($perm_ar_str as $v) {
		if (!$v) continue;
		if (!strpos(trim($v), '=')) {
			$msg.= '<script>alert(unescape("Permission entry \'' . rawurlencode($v) . '\' invalid !\\nFor each department it should have the format like:\\nDEPARTMENT = name (e.g.: PA = ' . $ln . ';)"))</script>';
			break;
		}
		$found = false;
		list($dptmt, $member) = explode('=', $v);
		$dptmt = trim($dptmt);
		if ($dptmt == '') continue;
		if ($dptmt == 'AD') $ad_dpmt = $member;
		$perm_ar[$dptmt] = '';
		if ($a[$dptmt]) $no_tplsave = true; // if signed, don't allow to save the form as new template
		// now we split string with multiple names
		if (strpos($member, ',') !== false) {
			$ret = splitStr(',', $member);
			if ($ret[0] === false) break;

			$members = $ret;
		} else $members = (array)$member;
		// gather the user ids
		foreach ($members as $member) {
			$user_id = $DNW_user->getUserIdByName($member);
			if (!$user_id[0]) continue;
			if (in_array($user_id[0], $para->uids)) $found = true;
		}
		if (!$found) continue;
		// now we are sure that the current user has permission to sign a specific part. Build the paraph string.
		$pedt = true;
		$nf = $fn{0} . '.';
		if ($mn) {
			$mni = '';
			$mna = explode(' ', $mn);
			foreach ($mna as $v) $mni.= $v{0} . '.';
		}
		$nm = $mn ? $mni : '';
		$nl = $ln{0} . '.';
		$perm_ar[$dptmt] = date("d.m.y") . ' / ' . $nf . $nm . $nl;
		// if not already signed, do an pop-up alert
		if (!$a[$dptmt]) $para_msg[] = $dptmt;
	}
	// generate the pop-up alert
	if (is_array($para_msg)) {
		$para_msg = join(', \\n  - ', $para_msg);
		$msg.= "<script>alert('{$para_msg_txt[$lang]} \\n  - $para_msg');</script>";
	}
}
//#####################################################################
// the following action is taken once if user submits a form as final #
//#####################################################################
if ($wce && $final) {
	// send mail to all colleagues, which have are listed in the list with permissons. Check for valid names.
	require_once PATH_IC . 'nameUtils.inc.php';
	$perm_ar_str = explode(';', $a['paraf']);
	$member_ar = array();
	$remind_to = array();
	foreach ($perm_ar_str as $v) {
		if (!$v) continue;
		if (!strpos(trim($v), '=')) {
			$msg.= '<script>alert(unescape("Permission entry \'' . rawurlencode($v) . '\' invalid !"))</script>';
			break;
		}
		list($dptmt, $member) = explode('=', $v);
		$dptmt = trim($dptmt);
		$member = trim($member);
		if ($dptmt == '' || $member == '') continue;
		if ($dptmt == 'AD') $ad_dpmt = $member;
		// if multiple names, split
		if (strpos($member, ',') !== false) {
			$ret = splitStr(',', $member);
			if ($ret[0] === false) $msg.= "<script>alert(unescape('" . rawurlencode($ret[1]) . "'))</script>";
			else {
				$member_ar = array_merge($member_ar, $ret);
				if (!in_array($dptmt, array('AD', 'SCR'))) $remind_to = array_merge($remind_to, $ret); // build the reminder array, don't remind AD and SCR

			}
		} else {
			$member_ar[] = $member;
			if (!in_array($dptmt, array('AD', 'SCR'))) $remind_to[] = $member; // build the reminder array, don't remind AD and SCR

		}
	}
	// if $member_ar has entries, send mail and set reminder. Delete old reminder if exists
	if (join('', $member_ar)) {
		require_once './project_mail.php';
		// look whether already a reminder exists for the current form
		list($old_reminder) = mysql_fetch_row(mysql_query("SELECT form_reminder FROM proj_forms WHERE form_id='$fid'"));
		// remind frequency: every second day
		$remind = 2;
		// add a uniqe id to that reminder as identifier
		$uniqeID = uniqid('');
		$remind.= $uniqeID;
		// note: mail recipients are given by their names ($pm, $member_ar, $remind_to, $ad_dpmt)
		$ret = send_project_mail($fac, $project_name, $tn . " ($project_number)", array($pm, $dpm), '', $member_ar, 'final', $ad_dpmt, $rec, $remind, $remind_to, $title . $frm_no, $country, $old_reminder);
		if ($ret[0] !== true) {
			mysql_query("UPDATE proj_forms SET form_status='' WHERE form_id='$fid'");
			$a['final'] = '' && $edt = true && $pedt = false;
			unset($perm_ar);
			$msg.= "<script>alert(unescape('" . rawurlencode($ret[1]) . "')+'\\n Please, check the names in the permission field of the document.')</script>";
		} else mysql_query("UPDATE proj_forms SET form_reminder='$uniqeID' WHERE form_id='$fid'");
		$msg.= "<script>alert(unescape('" . rawurlencode($ret[1]) . "'))</script>";
	} else {
		mysql_query("UPDATE proj_forms SET form_status='' WHERE form_id='$fid'");
		$a['final'] = '' && $edt = true && $pedt = false;
		unset($perm_ar);
		$msg.= "<script>alert('No user found for signing the document! \\n Please, enter the names of related users into the permission field of the document.\\n\\n HELP:\\n You can open and close the permission field if you\\n click onto the button \"Permission\"')</script>";
	}
}
//##########################################################################################################################
// the following action is taken if somebody has signed a form: update the reminder and inform the project manager by mail #
//##########################################################################################################################
if ($wce && ($sign == 'sign' || $sign == 'remind')) { // to-do: check whether 'remind' is still possible
	$perm_k = array_keys($perm_ar);
	// look whether all paraphes are done, exclude SCR
	$aOpen = array();
	foreach ($perm_k as $k) {
		if ($k == 'SCR') continue;
		if ($a[$k] == '') $aOpen[] = $k;
	}
	// look for reminder entry and if one replace recipient list
	$rs = mysql_query("SELECT form_reminder FROM proj_forms WHERE form_id='$fid'");
	if ($rs) {
		list($uniqID) = mysql_fetch_row($rs);
		$remind = '#' . $uniqID;
	}
	// build the updated reminder array based on open paraphes
	$perm_ar_str = explode(';', $a['paraf']);
	$member_ar = $remind_to = array();
	require_once PATH_IC . 'nameUtils.inc.php';
	foreach ($perm_ar_str as $va) {
		if (!$va) continue;
		if (!strpos(trim($va), '=')) {
			$msg.= '<script>alert(unescape("Permission entry \'' . rawurlencode($va) . '\' invalid !"))</script>';
			break;
		}
		list($dptmt, $member) = explode('=', $va);
		$dptmt = trim($dptmt);
		$member = trim($member);
		if ($dptmt == '' || $member == '') continue;
		if (!in_array($dptmt, $aOpen)) continue;
		// if multiple names, split
		if (strpos($member, ',') !== false) {
			$ret = splitStr(',', $member);
			if ($ret[0] === false) $msg.= "<script>alert(unescape('" . rawurlencode($ret[1]) . "'))</script>";
			else {
				if (!in_array($dptmt, array('AD', 'SCR'))) $remind_to = array_merge($remind_to, $ret); // build the reminder array

			}
		} else {
			if (!in_array($dptmt, array('AD', 'SCR'))) $remind_to[] = $member; // build the reminder array

		}
	}
	// send mail to pm and, if $a0pen empty or $a0pen=AD, to AD
	require_once './project_mail.php';
	$ret = send_project_mail($fac, $project_name, $tn . " ($project_number)", $pm, $signed_for, $aOpen, $sign, $ad_dpmt, '', $remind, $remind_to, $title . $frm_no, $country);
	if ($ret[0] !== true) $msg.= "<script>alert(unescape('" . rawurlencode($ret[1]) . "'))</script>";
	else $msg.= "<script>alert(unescape('" . rawurlencode($ret[1]) . "'))</script>";
}
//############################################################################
// the following action is taken if the questionnaire form shall be exported #
//############################################################################
if ($pes && ($export == 'expxls')) {
	if ($expcountry == 'DE') $xls_form = 'Questionnairede.mht';
	else $xls_form = 'Questionnaire.mht';
	ob_start();
	include $path . $xls_form;
	$page = ob_get_contents();
	ob_end_clean();
	if (!$a['h_remark']) $a['h_remark'] = '';
	if ($frm_no) $fach = $fac . $frm_no;
	else $fach = $fac;
	$page = str_replace(array('$$fac', '$$project_name', '$$customer', '$$project_number', '$$poc', '$$pm', '$$start', '$$end', '$$dpm', '$$remark', '$$address', '$$p_code', '$$fax', '$$tn'), array($fach, $project_name, $customer, $project_number, $poc, $pm, $start, $end, $dpm, $a['h_remark'], $address, $p_code, $fax, $tn), $page);
	$doc = 'Questionnaire.xls';
	require_once PATH_IC . 'write_office.inc.php';
	write_office($doc, $page, 'excel');
} elseif ($pes && ($export == 'e-mail' || $export == 'exphtm' || $export == 'expopn')) {
	$form_server = 'http://lehmanns-web.net/frm-proxy/customer';
	$action = $form_server . '/forms.php';
	$edt_current = $edt;
	$edt = false;
	$nam = ' name';
	$cb = '"checkbox"';
	$logo = 'src="' . $form_server . '/img/logo_n.gif" style="float:left;margin-left:-10px"';
	ob_start();
	echo '
	<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
	<html>
	<head>
	<meta http-equiv="content-type" content="text/html; charset=utf-8">
	<meta http-equiv="expires" content="0">
	<style>
	';
	require PATH_IC . 'help/docs/pf.css';
	echo '</style>';
	echo "<title>$title$fac</title>";
	echo '</head>
	<body>
	<div id="A4portr">
	';
	if ($source && strlen($tpl_code)) eval('?>' . $tpl_code);
	elseif (is_file("$path$file")) include "$path$file";
	else {
		echo '<script type="text/javascript">window.close();alert("Could not find requested project form!");</script>';
		exit;
	}
	echo '<p style="text-align:center"><input type="submit" name="send" value="Continue" style="border:1px solid red;background-color:navy;color:white;font-weight:bolder;cursor:pointer" title="send the information to DNW"></p>';
	echo '<input type="hidden" name="lang" value="' . $expcountry . '">';
	echo '<input type="hidden" name="formuid" value="' . $formuid . '">';
	echo '</form>';
	echo '</div></body></html>';
	$page = ob_get_contents();
	ob_end_clean();
	$edt = $edt_current;
	// provide formuid to form server
	if (!enter_formuid($form_server, $old_formuid, $formuid)) die('Form server not reachable. Please, try again later.');
	$subject = array('EN' => 'DNW Project evaluation form', 'DE' => 'Fragebogen f�r Kunden des DNW');
	if ($export == 'expopn') {
		echo $page;
		if (strlen($msg)) echo $msg;
		exit;
	}
	// zip the page to avoid problems with OWA
	$zip = new ZipArchive();
	if ($zip->open($subject[$expcountry] . '.zip', ZIPARCHIVE::CREATE) !== TRUE) {
		exit("cannot open <$filename>\n");
	}
	$zip->addFromString($subject[$expcountry] . '.html', $page);
	$zip->close();
	$page = file_get_contents($subject[$expcountry] . '.zip');
	unlink($subject[$expcountry] . '.zip');
	if ($export == 'exphtm') {
		header('Expires: 0', false);
		header('Pragma: public');
		header('Cache-Control: private');
		header('Accept-Ranges: bytes');
		//		Header('Content-type: text/html');
		Header('Content-type: application/zip');
		header("Content-length:" . (string)(strlen($page)));
		header('Content-disposition: attachment; filename="' . $subject[$expcountry] . '.zip"');
		sleep(1);
		echo $page;
		exit;
	}
	if ($export == 'e-mail') {
		require_once 'nameUtils.inc.php';
		require_once ('IC2mailer.php');
		$contentfile = './templates/formail.' . strtolower($country) . '.txt';
		$plainbody = file_get_contents($contentfile);
		$sender['name'] = $fn{0} . '. ';
		if ($mn) $sender['name'].= $mn . ' ';
		$sender['name'].= $ln;
		$sender['mailaddr'] = join('', $DNW_user->getUserEmail($uid));
		$res = IC2smtp_mail($_POST['mailaddr'], $Cc = array_merge($DNW_user->getUserEmailByName($pm), array('<EMAIL>')), $subject[$country], $plainbody, $htmlbody, $subject[$expcountry] . '.zip', $page, $sender);
		if ($res !== true) {
			$msg.= 'Mail error: ' . $res;
		} else $msg.= "<script>alert(\"Form send by e-mail to " . $_POST['mailaddr'] . ".\");</script>";
		unset($export);
	}
}
//#############################################################
// standard form display                                      #
//#############################################################
if (!$export) {
	// and now the output of the form

?>
	<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
	<html>
	<head>
	  <meta http-equiv="content-type" content="text/html; charset=utf-8">
	  <meta http-equiv="expires" content="0">
	  <link rel="stylesheet" href="<?php
	echo PATH_IC; ?>help/docs/pf.css" type="text/css">
	  <link rel="stylesheet" media="print" href="<?php
	echo PATH_IC; ?>help/docs/pfp.css" type="text/css">
	  <title> <?php
	echo "$title$fac" ?></title>
	<?php
	if ($wce && $edt) echo '<script type="text/javascript">
	function final() {
		if(!confirm("This is the FINAL save!\n - the Project form will be opened for signing.\n The related persons will be informed by e-mail.\n - No further changes are possible by the Project Manager.\n Do you want to proceed?")) return;
		document.pform.final.value="final"; 
		document.pform.send.value="ready";
		if(typeof modified != \'undefined\') modified=false;
		document.pform.submit();
	}
	</script>
	';
	if ($pes && !$export && $status != '1') {
		echo '<script type="text/javascript">
		function export_form(s,mode) {
			if(mode==2)document.pform.expo.value="e-mail"+s; 
			else if(mode==3)document.pform.expo.value="exphtm"+s;
			else if(mode==0)document.pform.expo.value="expxls"+s;
			else document.pform.expo.value="expopn"+s;
			document.pform.send.value="ready";
			if(typeof modified != \'undefined\') modified=false;
			document.pform.submit();
			document.pform.expo.value="";
		}
		</script>
		';
	}
	if ($pedt) echo '<script type="text/javascript">
	function para(id,name) {
		if(!id || !name) return;
		if(!confirm("You are going to sign the document electronically. That means that:\n - yor initials will be added to the document\n - the input/changes you have made will be saved to the document\n - the project manager will be informed immedeatly by e-mail that you have signed the document.\n Do you want to proceed?")) return;
		try{saveElm()} catch(er){};
		document.pform.elements[id].value=name;
		document.pform.send.value="sign";
		document.pform.has_signed.value=id;
		if(typeof modified != "undefined") modified=false;
		if(typeof fields=="undefined" || !fields[id])
		document.pform.submit();
		else
			submitSigning(id);
	}
	function submitSigning(id){
		if(typeof fields=="undefined" || !fields[id]) return alert("Field definition missing!");
		var f=document.forms["signingform"];
		copyFields(f,"header");
		copyFields(f,id);
		f.submit();
	}
	function copyFields(f,id){
		var start=fields[id]["start"];
		var end=fields[id]["end"];
		var found = false;

		for(var i=0;i<document.pform.elements.length;i++){
			var append=true;
			var formfield=document.pform.elements[i];
			if(!found && (!formfield.name || formfield.name!=start)) continue;
			found=true;
			var new_elm = document.createElement("input");
			new_elm.name=formfield.name;
			new_elm.type="hidden";
			switch(formfield.type){
				case "hidden":
				case "text":
				case "textarea":
					new_elm.value=formfield.value;
					break;
				case "checkbox":
					if(formfield.checked)new_elm.value = formfield.value; else append=false;
			}
			if(append)
				f.appendChild(new_elm);
			new_elm=null;
			if(formfield.name==end) break;
		}
	}
	</script>';
	if (!$pedt && $pm_edt) echo '<script type="text/javascript">
	function para(id,name) {
		if(!id || !name) return;
		document.pform.elements[id].value=name;
		document.pform.send.value="ready";
		if(typeof modified != \'undefined\') modified=false;
		document.pform.submit();
	}
	</script>';
	if ($wce && $a['final'] && ($edt || $pm_edt)) {
		echo '<script type="text/javascript">';
		echo '
		function reset(){
			if(!confirm("You are going to RESET the form back to EDIT MODE. That means that:\n - ALL paraphes (date and sign) will be removed !! \n - You can edit or modify all content of the form\n - You must click again on FINAL in order to initiate the signing procedure.\n Do you want to proceed?")) return;
			document.pform.la.value="";
			document.pform.send.value="reset";
			document.pform.submit();
		};
		function remind(){
			if(!confirm("You are going to REMIND your colleagues to sign the document.\n Do you want proceed?")) return;
			document.pform.la.value="";
			document.pform.send.value="remind";
			document.pform.target="help";
			if(typeof modified != \'undefined\') modified=false;
			document.pform.submit();
		};';
		echo '</script>';
	}
	if (!$edt) echo '<style>input,textarea,.empty,.emptyct,.emptyr {background:white;}.sel{display:none}</style>';
	if ($pedt) $name = 'style="background:lightyellow" name';
	else $name = 'name';
	$logo = 'src="' . PATH_IC . 'help/docs/img/dnw.gif" style="float:left;"';
	$nonam = 'readonly';
	$nam = $name;
	$cb = $cbx = '"checkbox"';
	$cbxno = '"checkbox" onclick="this.checked=!this.checked"';
	if (!$edt) {
		$cb = $cbxno;
		$nam = $nonam;
	}
	if ($edt && !$export) { ?>
		<script type="text/javascript">
		function check_cbx() {
		if(!document.getElementById('hidden')) return;
			var f=document.pform;
			var k=f.length;
			var html='';
			for (var i = 0; i < k; ++i) {
			elm = f.elements[i];
			  if(elm.type == 'checkbox' && elm.name && !elm.checked) html +='<input type="hidden" name="' + elm.name+'" value="">';
			}
			document.getElementById('hidden').innerHTML=html;
		}
		</script>
	<?php
	};
	$pk = $_SESSION[$script . '_pagekey'][$pkey];
	$action = $script;
	echo '</head>
	<body onunload="try{if(opener && opener.refresh_form)opener.refresh_form()}catch(e){};">
	<div id="A4portr">
	';
	if ($source && strlen($tpl_code)) eval('?>' . $tpl_code);
	elseif (is_file("$path$file")) include "$path$file";
	else {
		echo '<script type="text/javascript">window.close();alert("Could not find requested project form!");</script>';
		exit;
	}
	if ($wce) {
		if ($edt || $pm_edt) {
			$paraf = 'name';
			$bg = 'background:lightyellow';
		} else $paraf = 'readonly';
		require_once 'apptop2.inc';
		echo '<div id="permid">' . apptop2('Permissions', "document.getElementById('permid').style.display='none'", 'close');
		echo '<textarea id="perm" ' . $paraf . '="paraf" class="empty" cols="40" rows="8" wrap="virtual" style="' . $bg . '">';
		echo $a['paraf'];
		echo '</textarea></div>';
		echo '<input type="hidden" name="la" value="' . $draft . '">';
	}
	if ($pes && ($fid > 0) && $status != '1') {
		// some language stuff
		$help0 = array('EN' => 'Please, chose one of the following options:', 'DE' => 'Bitte w�hlen Sie aus folgenden Optionen:');
		$help1 = array('EN' => 'Open the form in a browser window. You can work with this document immediately or you can save it as html-file on disk for later use.', 'DE' => '&Ouml;ffne Form im Browser. Sie k&ouml;nnen mit der Vorlage direkt arbeiten oder aber als html-File abspeichern f&uuml;r eine sp&auml;tere Bearbeitung');
		$help2 = array('EN' => 'You will send the form (packed in a zip-archive) by e-mail to the recipient you enter in the mask. Be sure to have entered a valid e-mail address!<br><b>To:</b> ', 'DE' => 'Sie m&ouml;chten die Vorlage (als zip-Archiv gepackt) via e-mail an den Empf&auml;nger schicken, dessen Adresse Sie in der Eingabemaske eingeben. Bitte achten Sie auf darauf, da&szlig; die e-mail Adresse korrekt/g&uuml;ltig ist!<br><b>An:</b> ');
		$help3 = array('EN' => 'You will download and store the form as zip-packed html-file for later use.', 'DE' => 'Sie m&ouml;chten die Vorlage downloaden und f&uuml;r die sp&auml;tere Bearbeitung als zip-komprimiertes html-File speichern.');
		$help4 = array('EN' => 'You will download and store the form as MS-Excel file for later use.<br><span style="color:red">Attention: This option has no on-line features. You have to fill the information given by the customer into the project database manually!</span>', 'DE' => 'Sie m&ouml;chten die Vorlage downloaden und f&uuml;r die sp&auml;tere Bearbeitung als MS-Excel File speichern.<br><span style="color:red">Achtung: Diese Option hat keine on-line Unterst&uuml;tzung. Sie m&uuml;ssen die vom Kunden eingef&uuml;llte Information manuell in die Projektdatenbank &uuml;bertragen!</span>');
		$echo.= '<div id="pefmnu" style="position:absolute;top:500px;left:100px;width:400px;background-color:beige;border:10px solid #e4e4cb;display:none">';
		$echo.= '<table style="font-size:8pt;" cellpadding="10"><tr><td colspan="2" style="font-weight:bolder">' . $help0[$country] . '</td></tr>';
		//		$echo .= '<tr><td>'.$help1[$country].'</td><td><button type="button" style="font-size:8pt" onclick="export_form(\''.$country.'\',1)">Go </button></td></tr>';
		$echo.= '<tr><td>' . $help2[$country] . ' <input style="font-size:8pt;color:blue;background-color:white;border:1px solid silver" name="mailaddr" size="40"></td><td><button type="button" style="font-size:8pt" onclick="document.getElementById(\'pefmnu\').style.display=\'none\';export_form(\'' . $country . '\',2)">Go</button></td></tr>';
		$echo.= '<tr><td>' . $help3[$country] . '</td><td><button type="button" style="font-size:8pt" onclick="document.getElementById(\'pefmnu\').style.display=\'none\';export_form(\'' . $country . '\',3)">Go</button></td></tr>';
		$echo.= '<tr><td>' . $help4[$country] . '</td><td><button type="button" style="font-size:8pt" onclick="document.getElementById(\'pefmnu\').style.display=\'none\';export_form(\'' . $country . '\',0)">Go</button></td></tr></table>';
		$echo.= '<input type="hidden" name="expo">';
		$echo.= '<input type="hidden" name="formuid" value="' . $formuid . '">';
		$echo.= '</div>';
		echo $echo;
		$echo = '';
	}
	echo '</form>';
	// form for signing submission
	echo '<form name="signingform" id="signingform" method="post" style="display:none"></form>';
	echo '</div>';
	echo '<div id="footer"><span id="footnote">', $fac, '-project forms v.2006/1; last update:', $lup, '; printed:', date('d-m-y H:i'), '</span></div>';
	// buttons
	echo '<div id="btn">';
	if ($wce) echo '<button type="button" onclick="var elm=document.getElementById(\'permid\');if(elm.style.display==\'block\')elm.style.display=\'none\';else elm.style.display=\'block\'">Permission</button>';
	if ($edt || $pm_edt) {
		if ($wce && $a['final']) {
			$echo.= '<button type="button" title="Remind Now!" onclick="remind()">Remind</button>';
			if ($a['res']) $echo.= '<button type="button" style="color:red;font-weight:bolder" title="Set form back to edit mode." onclick="reset()">RESET</button>';
		} elseif (!$pes || ($pes && ($status != '1'))) $echo = '<button type="button" id="savebtn" onclick="document.pform.send.value=\'ready\'; try{if(typeof check_cbx==\'function\')check_cbx()}catch(e){alert(e)};try{if(typeof save==\'function\')save()}catch(e){alert(e)};if(typeof modified != \'undefined\') modified=false; document.pform.submit()">Save</button>';
		if (!$no_tplsave) {
			$echo.= '<button id="tplbtn" style="display:none" type="button" onclick="document.pform.send.value=\'tpl\'; try{if(typeof check_cbx==\'function\')check_cbx()}catch(e){alert(e)};try{if(typeof save==\'function\')save()}catch(e){alert(e)};if(typeof modified != \'undefined\') modified=false;
				document.pform.submit()">Save as Template</button>';
			$echo.= '<button id="copybtn" style="display:none" type="button" onclick="var elm=document.createElement(\'INPUT\');elm.name=\'copy\';document.pform.appendChild(elm);if((elm=prompt(\'Please enter project number were you want to copy from:\',\'\')) && elm.length==9){document.pform.copy.value=elm;document.pform.submit();}">Copy from</button>';
		}
		if ($wce && !$a['final']) $echo.= '<button type="button" style="color:red;font-weight:bolder" onclick="final()">FINAL</button>';
		if ($pes && ($fid > 0) && $status != '1') {
			$echo.= '<button type="button" style="color:red;font-weight:bolder"';
			if (strlen($formuid) > 1) $cf.= 'cf=confirm("This questionnaire is already exported.\n If you export the same form again, the earlier exported form will be deleted from the database and will be no longer usable by the customer.\n\n Do you really want to proceed?");';
			$echo.= 'onclick=\'var cf=true;' . $cf . 'if(cf==false) return; document.getElementById("pefmnu").style.display="block"; document.pform.mailaddr.focus();\'>Export</button>';
		}
	}
	echo $echo, '<button type="button" onclick="self.close()">Close</button>';
	echo '</div>';
	if (strlen($msg)) echo $msg;
	echo '<script type="text/javascript">';
	echo 'onload=function(){self.resizeTo(740,screen.availHeight*0.95); if(document.pform.tpl && document.pform.tpl.value=="1" && document.getElementById("tplbtn")) document.getElementById("tplbtn").style.display=document.getElementById("copybtn").style.display=""; if(typeof(init) != "undefined")init();};';
	echo 'onbeforeunload=function(){try{if((document.pform.la && document.pform.la.value && (document.pform.send.value!="draft")) || modified)return "You have not saved changes. If you leave the page these changes will be lost.\n"}catch(e){alert(e)}}';
	echo '</script>';
	echo '
	</body>
	</html>
	<iframe name="help" src="../empty.html" style="display:none"></iframe>
	';
	//	ob_end_flush();
	header('Content-Length: ' . ob_get_length());
	header('Content-Type: text/html');
	header('Connection: close');
	ob_end_flush();
}
function enter_formuid($form_server = '', $old_formuid = '', $formuid = '')
{
	if (empty($form_server)) return false;
	require_once "qc.php";
	require_once "Request.php";
	$req = new HTTP_Request($form_server . '/src/enter_formuid.php');
	$req->setMethod(HTTP_REQUEST_METHOD_POST);
	$req->addPostData('sq', query_encode('old_formuid', $old_formuid, 'formuid', $formuid, 'key', time()));
	$req->sendRequest();
	return $req->getResponseBody();
}
?>
