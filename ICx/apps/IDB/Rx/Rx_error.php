<?php
/* Rx.php is the main server side script for IC ajax serving related requests
   cgi parameter:
   no parameter: nothing is issued
   $_POST['L'] integer; parent object id (i.e. folder id), "0" or ""=root folder, "-1" = one level higher
   $_POST['op'] string; operation: V=view details, A=add record, C=change record, D=delete record

Version 0.1, G.L.	18 May 2011
*/
define('PATH_IC', '../../');
require PATH_IC . 'IC2auth.inc.php'; // sets user id ($uid) and permission (rights), defines $script
//file_put_contents('log.txt',"\n".date('d-m-y H:i:s').' user:'.$_SESSION['user_lname'].' Rx rights:'.$rights."\n".print_r($_REQUEST,1)."\n".$_SERVER['HTTP_HOST']."\n", FILE_APPEND);
switch ($rights) {
case 0: // no rights at all
	echo '<h3>Access denied!</h3>';
	exit;
case 1: // path view rights
	echo '<h3>Access denied!</h3>';
	exit;
case 2: // read rights on object
	$opts['options'] = 'V';
	break;

case 3: // write rights
	
case 4: // workflow edit rights
	
case 5: // edit rights
	
case 6: // owner rights
	
case 7: // administrator group
	
case 8: // root user
	$opts['options'] = 'AVcd'; // enables add, view, change & delete only via view
	break;

default: // no rights at all
	echo '<h3>Access denied!</h3>';
	exit;
}
$_SESSION['my_or_getThumb.php'] = $rights;
//--------------------------- general stuff -------------------------------------------------------
$uid == 1 ? $admin = true : '';
if ($admin) $rights = 7;
$ie = ($_SESSION['browser']['USR_BROWSER_AGENT'] == 'IE') ? true : false;
$tbl_objects = $_SESSION['DB_IC'] . '.info_object';
ob_start();
// ----- gathers all child node ids
function readNodes($base)
{
	global $ids, $node_names;
	$tbl_objects = $_SESSION['DB_IC'] . '.info_object';
	$tbl_folders = $_SESSION['DB_IC'] . '.info_object_folder';
	$qry = "SELECT object_id,object_hrn FROM $tbl_objects, $tbl_folders  WHERE object_parent_id='$base' AND info_object_folder_typ>10 AND fk_object_id=object_id";
	$res = mysql_query($qry) or die($qry . mysql_error());
	while (list($child, $hrn) = mysql_fetch_row($res)) {
		$ids[] = $child;
		$node_names[$child] = $hrn;
		readNodes($child);
	}
}
function getChilds($base, $reset = false, $n = '')
{
	static $childs;
	$reset && $childs = array();
	$tbl_objects = $_SESSION['DB_IC'] . '.info_object';
	$tbl_folders = $_SESSION['DB_IC'] . '.info_object_folder';
	$qry = "SELECT object_id,info_object_folder_typ,object_hrn FROM $tbl_objects, $tbl_folders  WHERE object_parent_id='$base' AND info_object_folder_typ>10 AND fk_object_id=object_id";
	$res = mysql_query($qry) or die($qry . mysql_error());
	while (list($child, $type, $name) = mysql_fetch_row($res)) {
		if ($type == 11) $n = $name;
		$childs["$child"] = $n;
		getChilds($child, false, $n);
	}
	return $childs;
}
// ----- follows the path up to facility node (info_object_folder_typ == 11)
function getPath($base, $reset = false, $n = '')
{
	static $childs;
	$reset && $childs = array();
	$tbl_objects = $_SESSION['DB_IC'] . '.info_object';
	$tbl_folders = $_SESSION['DB_IC'] . '.info_object_folder';
	$qry = "SELECT object_parent_id,info_object_folder_typ,object_hrn FROM $tbl_objects, $tbl_folders  WHERE object_id='$base' AND info_object_folder_typ>10 AND fk_object_id=object_id";
	$res = mysql_query($qry) or die($qry . mysql_error());
	while (list($child, $type, $name) = mysql_fetch_row($res)) {
		$childs["$base"] = $name;
		if ($type != 11) getPath($child);
	}
	return $childs;
}
// show error messages
echo $_SESSION['errorsetr'];
unset($_SESSION['errorsetr']);
// language
$lang = $_SESSION['language'];
require_once PATH_IC . 'qc.php';
$query = '';
if (!empty($_REQUEST['sq'])) {
	$query = query_decode('sq', $_REQUEST['sq']);
	$_POST['L'] = $query['xflv'];
	$_POST['op'] = $query['op'];
}
(isset($_POST['L']) && ($oid = (int)$_POST['L']) >= 0) || $oid = 0; // valid oid provided
if (isset($_POST['Rx']) && $_POST['Rx'] >= 0) $oid = (int)$_POST['Rx']; // valid oid provided
elseif (isset($_GET['Rx']) && $_GET['Rx'] >= 0) $oid = (int)$_GET['Rx']; // valid oid provided
elseif (isset($_GET['RxL']) && $_GET['RxL'] >= 0) $oid = (int)$_GET['RxL']; // valid oid provided
elseif (isset($_GET['RxLtpl']) && $_GET['RxLtpl'] >= 0) $oid = (int)$_GET['RxLtpl']; // valid oid provided
elseif (isset($_GET['RxLidb']) && strlen($_GET['RxLidb']) >= 0) $oids = $_GET['RxLidb']; // valid oid provided
elseif (isset($_GET['RxMo']) && $_GET['RxMo'] >= 0) $oid = (int)$_GET['RxMo']; // valid oid provided
elseif (isset($_GET['RxPo']) && $_GET['RxPo'] >= 0) $oid = (int)$_GET['RxPo']; // valid oid provided
elseif (isset($_GET['RxSyo']) && $_GET['RxSyo'] >= 0) $oid = (int)$_GET['RxSyo']; // valid oid provided
elseif (isset($_GET['RxLog']) && $_GET['RxLog'] >= 0) $oid = (int)$_GET['RxLog']; // valid oid provided
elseif (isset($_GET['RxLall']) && $_GET['RxLall'] >= 0) $oid = (int)$_GET['RxLall']; // valid oid provided
elseif (isset($_GET['RxPath']) && $_GET['RxPath'] >= 0) $oid = (int)$_GET['RxPath']; // valid oid provided
//print_r($_POST);
$_POST['C'] && $_POST['op'] = 'C';
$_POST['A'] && $_POST['op'] = 'A';
$_POST['D'] && ($_POST['OP'] == 'D') && $_POST['op'] = 'D';
$_POST['D'] && ($_POST['OP'] == 'P') && $_POST['op'] = 'P';
$_POST['D'] && ($_POST['OP'] == 'M') && $_POST['op'] = 'M';
//$_POST['Rx'] && $_POST['op']='Rx';
empty($_POST) && $_POST['op'] = 'L';
if (isset($_GET['Rx'])) {
	$_POST['op'] = 'Rx';
}
if (isset($_GET['RxL'])) {
	$_POST['op'] = 'RxL';
}
if (isset($_GET['RxLtpl'])) {
	$_POST['op'] = 'RxLtpl';
}
if (isset($_GET['RxLidb'])) {
	$_POST['op'] = 'RxLidb';
}
if (isset($_GET['RxMo'])) {
	$_POST['op'] = 'RxMo';
}
if (isset($_GET['RxPo'])) {
	$_POST['op'] = 'RxPo';
}
if (isset($_GET['RxSyo'])) {
	$_POST['op'] = 'RxSyo';
}
if (isset($_GET['RxLog'])) {
	$_POST['op'] = 'RxLog';
}
if (isset($_GET['RxLall'])) {
	$_POST['op'] = 'RxLall';
}
if (isset($_GET['RxPath'])) {
	$_POST['op'] = 'RxPath';
}
if (isset($_GET['L'])) {
	$_POST['op'] = 'RL';
	$oid = (int)$_GET['L'];
}
if (isset($_GET['op']) && ($_GET['op'] == 'VOR')) $_POST['op'] = 'VOR';
if (isset($_GET['op']) && ($_GET['op'] == 'VV')) $_POST['op'] = 'VV';
if (isset($_GET['op']) && ($_GET['op'] == 'VW')) $_POST['op'] = 'VW';
if (isset($_GET['op']) && ($_GET['op'] == 'PG')) {
	$_POST['op'] = 'PG';
	$_POST['g'] = (int)$_GET['g'];
}
if (isset($_GET['op']) && ($_GET['op'] == 'CP')) {
	$_POST['op'] = 'CP';
	$_POST['sq'] = $_GET['CPSQ'];
	$_POST['t'] = $_GET['t'];
	$_POST['all'] = $_GET['x'];
}
if (isset($_GET['op']) && ($_GET['op'] == 'CPA')) {
	$_POST['op'] = 'CPA';
	$_POST['sq'] = $_GET['CPA'];
	$_POST['t'] = $_GET['t'];
	$_POST['all'] = $_GET['x'];
}
if (isset($_GET['op']) && ($_GET['op'] == 'CPV')) {
	$_POST['op'] = 'CPV';
	$_POST['sq'] = $_GET['CPV'];
	$_POST['mode'] = $_GET['m'];
}
if (isset($_GET['op']) && ($_GET['op'] == 'COW')) {
	$_POST['op'] = 'COW';
	$_POST['sq'] = $_GET['xfl'];
	$_POST['fns'] = $_GET['fns'];
}
switch ($_POST['op']) {
	//	 provide list page and list content of a folder
	
case 'L':
	require_once PATH_IC . 'get_obj_right.php';
	echo '<link rel="stylesheet" type="text/css" href="list.css">';
	echo '<script type="text/javascript" src="js/listframe.js"></script>';
	echo '<script>';
	if (!$oid) {
		echo 'parent.addrcont="' . $_SESSION['SERVER_HRN'] . '";';
		$_SESSION['IClist_oid'] = 0;
	} else $_SESSION['IClist_oid'] = $oid;
	echo 'parent.L.config("listFrame","list");';
	echo 'parent.L.config("downloadFrame","dl");';
	echo 'parent.L.config("popwinWin","self");';
	echo 'parent.or=' . IC2_get_obj_right($oid) . ';';
	echo 'parent.sq="' . query_encode('rec', $oid) . '";';
	echo "try{parent.addr($oid,'');parent.btn_up();}catch(e){alert(e)}";
	echo content($oid, $uid, $group_ids, 'parent.L.data', 'tbl.object_parent_id = "' . $oid . '"');
	echo 'function refresh(){try{L.L(L.currentId);}catch(e){alert("In refresh: "+e)}}';
	echo '</script>';
	echo "</HEAD>
	<BODY onLoad='parent.L.listonload(parent.L.data,\"parent\")'>";
	if (!$oid) {
		// image preloding to sail around IE image cache bug
		echo '
	<div style="display:none"><img src="imgIClist/pme-view.png"><img src="img/obj_folder.gif"><img src="img/obj_word.gif"><img src="img/obj_excel.gif"><img src="img/obj_pdf.gif"><img src="img/obj_bin.gif"><img src="img/obj_ppt.gif"><img src="img/obj_gif.gif"><img src="img/obj_pdf.gif"><img src="img/obj_html.gif"><img src="img/obj_text.gif"><img src="img/obj_zip.gif"><img src="img/obj_file.gif"><img src="img/php.gif"><img src="img/object_database.gif"><img src="img/ICapps.gif"></div>';
	}
	echo '
	<table class="body" width="100%"><tr><td id="BG" class="bodycell" style="background:' . $_SESSION['BGROUND'] . '"><div id="cont"></div></td></tr></table>
	</BODY>
	</HTML>';
	break;

case 'RL': // list content of a folder
	require_once PATH_IC . 'get_obj_right.php';
	if (!$oid) {
		echo 'parent.addrcont="' . $_SESSION['SERVER_HRN'] . '";';
		$_SESSION['IClist_oid'] = 0;
	} else $_SESSION['IClist_oid'] = $oid;
	echo 'parent.or=' . IC2_get_obj_right($oid) . ';';
	echo 'parent.sq="' . query_encode('rec', $oid) . '";';
	echo "try{parent.addr($oid,'');parent.btn_up();}catch(e){alert(e)}";
	echo content($oid, $uid, $group_ids, 'parent.L.data', 'tbl.object_parent_id = "' . $oid . '"');
	echo 'parent.L.listonload(parent.L.data,"parent");';
	break;

case 'Rx': // list subfolder of a folder for maintenance portal, look for associated technical data/master card, docs, work-orders
	require_once PATH_IC . 'get_obj_right.php';
	//	$r = 'parent.or='.IC2_get_obj_right ($oid).';';
	$echo = array();
	$res1 = content($oid, $uid, $group_ids, '', 'tbl.object_id = "' . $oid . '"', true);
	$res1 = str_replace('data:[', 'selected:true, data:[', $res1);
	if (!is_array($res1)) $res1 = array();
	$res2 = content($oid, $uid, $group_ids, '', 'tbl.object_parent_id = "' . $oid . '" AND tbl.fk_object_type_id = 1', true);
	if (!is_array($res2)) $res2 = array();
	$rowids = array_keys($res2);
	array_unshift($rowids, $oid);
	$keys = join(',', $rowids);
	//file_put_contents('rx_log.txt', '*** '.print_r($keys,1)."\n",FILE_APPEND);
	// ----- look for documents
	$qry = "SELECT object_parent_id FROM $tbl_objects WHERE FIND_IN_SET(object_parent_id,'$keys') AND fk_object_type_id > 12 GROUP BY object_parent_id";
	($rs = mysql_query($qry)) || die(mysql_error());
	while ($rs && ($row = mysql_fetch_row($rs))) $res_doc[$row[0]] = $row[0];
	//echo ' 2*** ',print_r($res_doc,1);
	// ----- look for technical data
	$tbl_maintenance = $_SESSION['DB_APPS'] . '.maintenance';
	$qry = "SELECT fk_obj_id FROM $tbl_maintenance WHERE FIND_IN_SET(fk_obj_id,'$keys') AND m_type='' GROUP BY fk_obj_id";
	($rs = mysql_query($qry)) || die(mysql_error());
	while ($rs && ($row = mysql_fetch_row($rs))) $res_techdata[$row[0]] = $row[0];
	//echo ' 3*** ',print_r($res_techdata,1);
	// ----- look for logs
	$qry = "SELECT fk_obj_id FROM $tbl_maintenance WHERE FIND_IN_SET(fk_obj_id,'$keys') AND m_type = 'log' GROUP BY fk_obj_id";
	($rs = mysql_query($qry)) || die(mysql_error());
	while ($rs && ($row = mysql_fetch_row($rs))) $res_log[$row[0]] = $row[0];
	//echo ' 4*** ',print_r($res_log,1);
	// ----- look for work orders
	$qry = "SELECT fk_obj_id FROM $tbl_maintenance WHERE FIND_IN_SET(fk_obj_id,'$keys') AND m_type = 'wo' GROUP BY fk_obj_id";
	($rs = mysql_query($qry)) || die(mysql_error());
	while ($rs && ($row = mysql_fetch_row($rs))) $res_wo[$row[0]] = $row[0];
	//echo ' 5*** ',print_r($res_wo,1);
	// ----- extend individual rows with found information
	foreach ($res1 as $key => $val) {
		$val.= $res_doc[$key] ? "<cell>docs.gif^Documents</cell>" : "<cell>e.png^No documents</cell>";
		$val.= $res_techdata[$key] ? "<cell>techdatac.gif^Technical Data</cell>" : "<cell>e.png^No technical data</cell>";
		$val.= $res_log[$key] ? "<cell>lbook.gif^Logbook</cell>" : "<cell>e.png^No logbook</cell>";
		$val.= $res_wo[$key] ? "<cell>wo.gif^Work orders</cell>" : "<cell>e.png^No work orders</cell>";
		$res1[$key] = $val . '</row>';
	}
	foreach ($res2 as $key => $val) {
		$val.= $res_doc[$key] ? "<cell>docs.gif^Documents</cell>" : "<cell>e.png^No documents</cell>";
		$val.= $res_techdata[$key] ? "<cell>techdatac.gif^Technical Data</cell>" : "<cell>e.png^No technical data</cell>";
		$val.= $res_log[$key] ? "<cell>lbook.gif^Logbook</cell>" : "<cell>e.png^No logbook</cell>";
		$val.= $res_wo[$key] ? "<cell>wo.gif^Work orders</cell>" : "<cell>e.png^No work orders</cell>";
		$res2[$key] = $val . '</row>';
	}
	$echo[] = join('', $res1);
	$echo[] = join('', $res2);
	echo '<?xml version="1.0" ?><rows>' . $echo[0] . $echo[1] . '</rows>';
	break;

case 'RxL': // list document objects of a maintenance item (folder) including associated template documents
	//	require_once 'get_obj_right.php';
	$echo = '';
	// documents linked to that maintenance item and maybe to the used template
	$tbl = $_SESSION['DB_APPS'] . '.maintenance';
	$where = "fk_obj_id = '$oid' AND m_type=''";
	$order = 'ORDER BY m_changed DESC';
	$qry = "SELECT m_tpl_id, m_lnk_obj_id FROM $tbl WHERE $where $order";
	$rs = mysql_query($qry);
	if ($rs) list($tpl_id, $obj_ids[]) = mysql_fetch_row($rs);
	else exit('Error: ' . mysql_error());
	// documents specific for the template of that maintenance item
	if ($tpl_id) {
		$where = "m_id = '$tpl_id'";
		$qry = "SELECT m_lnk_obj_id FROM $tbl WHERE $where";
		$rs = mysql_query($qry);
		if ($rs) list($obj_ids[]) = mysql_fetch_row($rs);
		else exit('Error: ' . mysql_error());
	}
	if (!empty($obj_ids)) {
		$obj_ids = array_diff($obj_ids, array('')); // cleanup empty values
		$obj_ids = join(',', $obj_ids);
		if (strlen($obj_ids)) {
			$echo.= content($oid, $uid, $group_ids, '', "tbl.object_id IN ($obj_ids)");
		}
	}
	// documents specific for that maintenance item
	$echo.= content($oid, $uid, $group_ids, '', 'tbl.object_parent_id = "' . $oid . '" AND tbl.fk_object_type_id > 12');
	if (empty($echo)) $echo = '<row id="new"><cell>../../img/16x1.gif</cell><cell>No documents attached.</cell><cell></cell><cell></cell><cell></cell><cell></cell><cell></cell><cell></cell></row>';
	echo '<?xml version="1.0" ?><rows>' . $echo . '</rows>';
	break;

case 'RxLall': // list document objects of all maintenance items (folder)
	//	require_once 'get_obj_right.php';
	$echo = '';
	$ids = $obj_ids = array();
	// documents linked to maintenance items and maybe to templates/master cards
	$tbl = $_SESSION['DB_APPS'] . '.maintenance';
	$where = "m_lnk_obj_id != ''";
	$order = 'ORDER BY m_changed DESC';
	$qry = "SELECT m_lnk_obj_id FROM $tbl WHERE $where $order";
	$rs = mysql_query($qry);
	if ($rs) list($obj_ids[]) = mysql_fetch_row($rs);
	else exit('Error: ' . mysql_error());
	if (!empty($obj_ids)) {
		$obj_ids = array_diff($obj_ids, array('')); // cleanup empty values
		$obj_ids = join(',', $obj_ids);
		if (strlen($obj_ids)) {
			$echo.= content($oid, $uid, $group_ids, '', "tbl.object_id IN ($obj_ids)", false, true);
		}
	}
	// documents specific for all maintenance items
	readNodes($oid);
	if (!empty($ids)) {
		$ids = array_diff($ids, array('')); // cleanup empty values
		$ids = join(',', $ids);
		if (strlen($ids)) {
			$echo.= content($oid, $uid, $group_ids, '', 'tbl.object_parent_id IN (' . $ids . ') AND tbl.fk_object_type_id > 12', false, true);
		}
	}
	if (empty($echo)) $echo = '<row id="new"><cell>No documents.</cell><cell>../../img/16x1.gif</cell><cell></cell><cell></cell><cell></cell><cell></cell><cell></cell><cell></cell></row>';
	else {
		foreach ($node_names as $id => $name) {
			$echo = preg_replace('/<cell>' . $id . '<\/cell>/', '<cell><![CDATA[' . $name . ']]></cell>', $echo);
		}
		$echo = preg_replace('/<cell>' . $_SESSION['maintenanceID'] . '<\/cell>/', '<cell><![CDATA[' . 'template/master card' . ']]></cell>', $echo);
	}
	echo '<?xml version="1.0" ?><rows>' . $echo . '</rows>';
	break;

case 'RxLtpl': // list document objects attached to a technical data template
	$echo = '';
	$oid = (int)$oid;
	if ($oid) {
		$tbl = $_SESSION['DB_APPS'] . '.maintenance';
		$where = "m_id = '$oid'";
		$qry = "SELECT m_lnk_obj_id FROM $tbl WHERE $where";
		$rs = mysql_query($qry);
		if ($rs) list($obj_ids) = mysql_fetch_row($rs);
		else exit('Error: ' . mysql_error());
		if (strlen($obj_ids)) {
			$echo.= content($oid, $uid, $group_ids, '', "tbl.object_id IN ($obj_ids)");
		}
	}
	if (empty($echo)) $echo = '<row id="new"><cell>../../img/16x1.gif</cell><cell>No documents attached.</cell><cell></cell><cell></cell><cell></cell><cell></cell><cell></cell><cell></cell></row>';
	echo '<?xml version="1.0" ?><rows>' . $echo . '</rows>';
	break;

case 'RxLidb': // list document objects attached to instruments records
	$echo = '';
	$oid = explode(',', $oids);
	$oids = $obj_ids = array();
	foreach ($oid as $v) if (((int)$v)) $oids[] = (int)$v;
	$oids = join(',', $oids);
	if (strlen($oids)) {
		$tbl = $_SESSION['DB_APPS'] . '.instrument';
		$where = "id IN ($oids)";
		$qry = "SELECT documents FROM $tbl WHERE $where";
		$rs = mysql_query($qry);
		if ($rs) {
			while ($row = mysql_fetch_row($rs)) {
				if (strlen($row[0])) $obj_ids[] = $row[0];
			}
			if (count($obj_ids)) {
				$obj_ids = join(',', $obj_ids);
				$obj_ids = explode(',', $obj_ids);
				$obj_ids = array_unique($obj_ids);
				$obj_ids = join(',', $obj_ids);
			} else $obj_ids = '';
		} else exit('Error: ' . mysql_error() . "\n" . $qry);
		if (strlen($obj_ids)) {
			$echo.= content($oid, $uid, $group_ids, '', "tbl.object_id IN ($obj_ids)");
		}
	}
	if (empty($echo)) $echo = '<row id="new"><cell>../../img/16x1.gif</cell><cell>No documents attached.</cell><cell></cell><cell></cell><cell></cell><cell></cell><cell></cell><cell></cell></row>';
	echo '<?xml version="1.0" ?><rows>' . $echo . '</rows>';
	break;

case 'RxLog': // list logbooks objects attached to a maintenance item
	$echo = '';
	$oid = (int)$oid;
	$ids = '';
	$childs = array();
	if (!empty($_GET['name'])) $name = $_GET['name'];
	else $name = '';
	function readLogs($where)
	{
		$tbl_user = $_SESSION['DB_IC'] . '.info_user';
		$tbl_objects = $_SESSION['DB_IC'] . '.info_object';
		$tbl_folders = $_SESSION['DB_IC'] . '.info_object_folder';
		$tbl_maint = $_SESSION['DB_APPS'] . '.maintenance';
		$qry = "SELECT object_id, join6.info_object_folder_typ, object_hrn, join6.info_object_folder_description, DATE_FORMAT(MAX(m_changed),'%d-%m-%Y'), CONCAT_WS(' ',join17.user_fname_s,join17.user_mname,join17.user_lname)
    FROM $tbl_objects as tbl
	  LEFT OUTER JOIN $tbl_folders AS join6 ON join6.fk_object_id=tbl.object_id
	  LEFT OUTER JOIN $tbl_user AS join17 ON join17.user_id = fk_user_id
	  LEFT OUTER JOIN $tbl_maint AS join18 ON join18.fk_obj_id = tbl.object_id
    WHERE $where AND info_object_folder_typ>10 AND m_type = 'log'
    GROUP BY join18.fk_obj_id
    ";
		$res = mysql_query($qry) or die($qry . mysql_error());
		while (list($child, $type, $name, $desc, $lup, $cby) = mysql_fetch_row($res)) {
			if ($type > 10) {
				$ids[$child] = array($name, $desc, $lup, $cby);
			}
		}
		return $ids;
	}
	$childs = getChilds($oid, true);
	$ids = join(',', array_keys($childs));
	if ($ids) $ids = $oid . ',' . $ids;
	else $ids = $oid;
	$res = readLogs("FIND_IN_SET(object_id,'$ids') AND m_type='log'");
	if (empty($res)) $echo = '<row id="new"><cell></cell><cell>No data.</cell><cell></cell><cell></cell></row>';
	else {
		$echo = print_r($childs, 1);
		foreach ($res as $key => $val) {
			$echo.= '<row id="' . $key . '">
       <cell><![CDATA[' . utf8_encode($childs["$key"]) . ']]></cell>
       <cell><![CDATA[' . utf8_encode($val[0]) . ']]></cell>
      <cell><![CDATA[' . utf8_encode(str_replace(array("\n", "\r", '"'), array('<br />', '', '&quot;'), $val[1])) . ']]></cell>
      <cell>' . $val[2] . '</cell>
      <cell>' . utf8_encode($val[3]) . '</cell>
     </row>';
		}
	}
	echo '<?xml version="1.0" ?><rows>' . $echo . '</rows>';
	break;

case 'RxMo': // list maintenance objects
	$rxtype = 13;
case 'RxPo': // list part objects
	if (empty($rxtype)) $rxtype = 14;
case 'RxSyo': // list system objects
	if (empty($rxtype)) $rxtype = 12;
	$echo = '';
	$ids = $obj_ids = array();
	$tbl_user = $_SESSION['DB_IC'] . '.info_user';
	$tbl_objects = $_SESSION['DB_IC'] . '.info_object';
	$tbl_folders = $_SESSION['DB_IC'] . '.info_object_folder';
	function readMos($base)
	{
		global $tbl_objects, $tbl_folders, $tbl_user, $ids, $rxtype;
		static $f;
		$qry = "SELECT object_id, join6.info_object_folder_typ, object_hrn, join6.info_object_folder_description, DATE_FORMAT(object_last_upd,'%d-%m-%Y'), CONCAT_WS(' ',join17.user_fname_s,join17.user_mname,join17.user_lname)
    FROM $tbl_objects
	  LEFT OUTER JOIN $tbl_folders AS join6 ON join6.fk_object_id=object_id
	  LEFT OUTER JOIN $tbl_user AS join17 ON join17.user_id = info_object.object_changed_by
    WHERE object_parent_id='$base' AND info_object_folder_typ>10 AND fk_object_id=object_id";
		$res = mysql_query($qry) or die($qry . mysql_error());
		while (list($child, $type, $name, $desc, $lup, $cby) = mysql_fetch_row($res)) {
			if ($type == 11) {
				$f = $name;
			} elseif ($type == $rxtype) {
				$ids[$child] = array($f, $name, $desc, $lup, $cby);
			}
			readMos($child);
		}
	}
	readMos($oid);
	if (empty($ids)) {
		$echo = '<row id="new"><cell></cell><cell>No data.</cell><cell></cell><cell></cell></row>';
	} else {
		$echo = '';
		foreach ($ids as $key => $val) {
			$echo.= '<row id="' . $key . '">
       <cell><![CDATA[' . utf8_encode($val[0]) . ']]></cell>
       <cell><![CDATA[' . utf8_encode($val[1]) . ']]></cell>
      <cell><![CDATA[' . utf8_encode(str_replace(array("\n", "\r", '"'), array('<br />', '', '&quot;'), $val[2])) . ']]></cell>
      <cell>' . $val[3] . '</cell>
      <cell><![CDATA[' . utf8_encode($val[4]) . ']]></cell>
     </row>';
		}
	}
	echo '<?xml version="1.0" ?><rows>' . $echo . '</rows>';
	break;

case 'RxPath': // list path nodes
	$path = getPath($oid, true);
	echo join('|', array_keys($path));
	break;

case 'VV': // provide revisions
	//echo 'alert("Revisions found:'.$count.'");';break;
	$sql = "SELECT info_object_file_origin FROM {$_SESSION['DB_IC']}.info_object_file WHERE fk_object_id = '$oid'";
	$res = mysql_query($sql);
	while ($row = @mysql_fetch_row($res)) list($origin) = $row;
	// get child objects
	$sql = "SELECT info_object_file_origin FROM {$_SESSION['DB_IC']}.info_object_file WHERE info_object_file_origin = '$origin'";
	$res = mysql_query($sql);
	$count = mysql_num_rows($res);
	if ($count > 1) { // render revision only if revisions available
		//echo 'alert("Revisions found:'.$count.'");';  // for debug
		list($maxOid) = mysql_fetch_row(mysql_query("SELECT MAX(fk_object_id) FROM {$_SESSION['DB_IC']}.info_object_file WHERE info_object_file_origin = '$origin' GROUP BY info_object_file_origin"));
		echo content($oid, $uid, $group_ids, 'parent.L.vdata', 'join8.info_object_file_origin = "' . $origin . '" AND join8.fk_object_id<>' . $oid);
		echo 'parent.L.vlistonload(parent.L.vdata,""';
		if ($oid != $maxOid) echo ',"noC"';
		echo ');';
	} // else echo 'alert("No revisions found.");';	// debug only
	break;

case 'VW': // provide window information
	$sql = "SELECT info_object_url_authenticate,info_object_urL_target,info_object_urL_width,info_object_urL_height FROM {$_SESSION['DB_IC']}.info_object_url WHERE fk_object_id = '$oid'";
	$res = mysql_query($sql);
	while ($row = @mysql_fetch_row($res)) list($wauth, $wtarget, $wwidth, $wheight) = $row;
	if ($wtarget) { // provide only if target available
		//echo 'alert("data found:'.$wtarget.'");';  // for debug
		echo "parent.L.setWindata({auth:$wauth,target:'$wtarget',width:$wwidth,height:$wheight});";
	} // else echo 'alert("No data found.");';	// debug only
	break;

case 'COW': // set owner select options
	$fn = $_POST['fns'];
	$oid = (int)$_POST['sq'];
	if ($_POST['sq'] != "$oid") {
		$query = query_sdecode($_POST['sq']);
		$oid = $query['xflv'];
	}
	if (!(int)$oid) die('alert("Error: Owner not changed")');
	($res = mysql_query("SELECT fk_user_id FROM {$_SESSION['DB_IC']}.info_object WHERE object_id='$oid'")) || die(mysql_error());
	list($owner) = mysql_fetch_row($res);
	$qry = "SELECT user_id, CONCAT(user_lname,', ',user_fname,' ',user_mname) FROM {$_SESSION['DB_IC']}.info_user WHERE (user_active='1' || user_sysaccount='1') AND user_id <> '$owner' AND user_lname<>'system' ORDER BY user_lname";
	if ($rs = mysql_query($qry)) {
		$ret = '[';
		while ($row = mysql_fetch_row($rs)) {
			$ret.= ',"' . mysql_real_escape_string($row[1]) . '",' . $row[0];
		}
		$ret.= ']';
		$ret = preg_replace('/\[,/', '[', $ret);
		echo $fn . '(' . $ret . ');';
	} else die('alert("' . mysql_error() . '");');
	break;

case 'CPV': // view users/groups to add to permission
	$query = query_sdecode($_POST['sq']);
	if (!($oid = $query['oid'])) {
		echo 'alert("Error: invalid object")';
		break;
	}
	// check for admin and owner
	require_once PATH_IC . 'IC2objutil.php';
	if ($uid != 1 && (IC2_get_obj_right($oid, $uid) < 6)) {
		echo 'alert("No permission to change object rights.")';
		break;
	}
	if ((int)$_POST['mode'] == 1) $user = true;
	elseif ((int)$_POST['mode'] == 2) $group = true;
	else break;

	if ($user) {
		$qry = "SELECT fk_user_id FROM {$_SESSION['DB_IC']}.user_rights WHERE user_rights.fk_object_id=$oid";
		if ($res = mysql_query($qry)) {
			$user = array();
			while ($row = @mysql_fetch_row($res)) $user[] = $row[0];
			$user = join(',', $user);
		}
		$qry = "SELECT user_id,user_lname,user_fname,user_mname FROM {$_SESSION['DB_IC']}.info_user WHERE user_id NOT IN('$user') AND user_active='1' AND user_sysaccount='0' ORDER BY info_user.user_lname";
		if ($res = mysql_query($qry)) {
			$ret = '';
			while ($row = @mysql_fetch_row($res)) {
				$row[1].= ', ' . $row[2];
				if ($row[3]) $row[1].= ', ' . $row[3];
				$ret.= ',"' . query_encode('oid', $oid, 'uid', $row[0]) . '","' . $row[1] . '"';
			}
			$ret.= '];parent.ulist("vgroup",vuser);';
			echo 'var vuser=["Select User from List"' . $ret;
		} else die('alert("' . mysql_error() . '");');
	} elseif ($group) {
		$qry = "SELECT fk_group_id FROM {$_SESSION['DB_IC']}.group_rights WHERE group_rights.fk_object_id=$oid";
		if ($res = mysql_query($qry)) {
			$group = array();
			while ($row = @mysql_fetch_row($res)) $group[] = $row[0];
			$group = join(',', $group);
		}
		$qry = "SELECT group_id,group_hrn FROM {$_SESSION['DB_IC']}.info_group WHERE group_id NOT IN('$group') AND group_active='1' AND group_sysaccount='0' ORDER BY group_hrn";
		if ($res = mysql_query($qry)) {
			$ret = '';
			while ($row = @mysql_fetch_row($res)) {
				$ret.= ',"' . query_encode('oid', $oid, 'gid', $row[0]) . '","' . $row[1] . '"';
			}
			$ret.= '];parent.ulist("vgroup",vgroups);';
			echo 'var vgroups=["Select Group from List"' . $ret;
		} else die('<script>alert("' . mysql_error() . '")</script>');
	}
	break;

case 'CPA': // add a user/group to permissions and set read permissions
	//	echo "alert('".$_POST['sq']."');";
	$query = query_sdecode($_POST['sq']);
	if (!($oid = $query['oid'])) {
		echo 'alert("Error: invalid object")';
		break;
	}
	// check for admin and owner
	require_once PATH_IC . 'IC2objutil.php';
	if ($uid != 1 && (IC2_get_obj_right($oid, $uid) < 6)) {
		echo 'alert("No permission to change object rights.")';
		break;
	}
	$cpa = true;
	$_POST['t'] = 2;
	if (isset($query['uid'])) echo 'parent.removeData(vuser,"' . $_POST['sq'] . '");parent.ulist("vgroup",vuser);';
	elseif (isset($query['gid'])) echo 'parent.removeData(vgroups,"' . $_POST['sq'] . '");parent.ulist("vgroup",vgroups);';
	else break;

case 'CP': // change permissions
	if (!$cpa) $query = query_sdecode($_POST['sq']);
	if (!($oid = $query['oid'])) {
		echo 'alert("Error: invalid object")';
		break;
	}
	// check for admin and owner
	require_once PATH_IC . 'IC2objutil.php';
	if ($uid != 1 && (IC2_get_obj_right($oid, $uid) < 6)) {
		echo 'alert("No permission to change object rights.")';
		break;
	}
	$oid = (array)$oid;
	$perm = (int)$_POST['t'];
	// look for "apply to child objects of next level"			todo: check right to change rights of child object
	if ($tid = (int)$_POST['all']) {
		// if object is file we have to gather all versions.
		if ($tid > 12) { // scan info_object_file for versions
			$sql = "SELECT info_object_file_origin FROM {$_SESSION['DB_IC']}.info_object_file 
					WHERE fk_object_id = '{$oid[0]}'";
			$res = mysql_query($sql);
			while ($row = @mysql_fetch_row($res)) {
				list($origin) = $row;
			}
			// get child objects
			$sql = "SELECT fk_object_id FROM {$_SESSION['DB_IC']}.info_object_file 
					WHERE info_object_file_origin = '$origin'";
			$res = mysql_query($sql);
			while ($row = mysql_fetch_row($res)) $oid[] = (int)$row[0];
		} elseif ($tid == 1) { // we have a folder where all childs have the same parent object
			$sql = "SELECT object_id FROM {$_SESSION['DB_IC']}.info_object WHERE object_parent_id = '{$oid[0]}'";
			$res = mysql_query($sql);
			while ($row = mysql_fetch_row($res)) {
				$oid[] = (int)$row[0];
			}
		}
	}
	if (isset($query['uid']) && $query['uid']) { // change user right
		$user_id = $query['uid'];
		$e = $w = $r = $owner = false;
		switch ($perm) {
		case 0:
		case 1:
			break;

		case 2:
			$r = true;
			break;

		case 3:
			$r = true;
			$w = true;
			break;

		case 4:
		case 5:
			$r = true;
			$w = true;
			$e = true;
			break;

		case 6:
			$r = true;
			$w = true;
			$e = true;
			break;

		case 7:
			$r = true;
			$w = true;
			$e = true;
			break;

		case 8:
			$r = true;
			$w = true;
			$e = true;
			break;

		default:
			break;
		}
		foreach ($oid as $object_id) {
			set_user_rights($_SESSION['DB_IC'], $object_id, $user_id, $r, $w, $e, $owner);
		}
		if ($perm == 0) echo 'parent.L.cpa("' . rawurlencode($_POST['sq']) . '",1);';
	} elseif (isset($query['gid']) && $query['gid']) { // change group right
		$group_id = $query['gid'];
		$w = $r = false;
		switch ($perm) {
		case 0:
		case 1:
			break;

		case 2:
			$r = true;
			break;

		case 3:
			$r = true;
			$w = true;
		default:
			break;
		}
		foreach ($oid as $object_id) {
			set_group_rights($_SESSION['DB_IC'], $object_id, $group_id, $w, $r);
		}
		if ($perm == 0) echo 'parent.L.cpa("' . rawurlencode($_POST['sq']) . '",2);';
	}
	$oid = $oid[0]; // provide correct oid for next action: VOR		(Note: if CP is called, also VOR has to be executed!!)
	// provide all user&group rights for a selected object
	
case 'VOR': // view object rights
	$db = $_SESSION['DB_IC'];
	// get object rights
	$sql = 'SELECT info_user.user_fname, info_user.user_mname, info_user.user_lname, perm_hrn, user_id, object_right FROM ' . $db . '.user_rights LEFT JOIN ' . $db . '.info_user ON info_user.user_id=user_rights.fk_user_id LEFT JOIN ' . $db . '.permission ON permission.perm_id=object_right WHERE (fk_object_id=' . $oid . ' AND perm_hrn <> "PATH" AND user_active="1") ORDER BY info_user.user_lname';
	$res = mysql_query($sql);
	while ($row = @mysql_fetch_array($res, MYSQL_ASSOC)) {
		$row['sq'] = query_encode('uid', $row['user_id'], 'oid', $oid);
		$user_rights[] = $row;
	}
	$sql = 'SELECT group_id, group_hrn, perm_hrn, object_right FROM ' . $db . '.group_rights LEFT JOIN ' . $db . '.info_group ON info_group.group_id=group_rights.fk_group_id LEFT JOIN ' . $db . '.permission ON permission.perm_id=object_right WHERE (fk_object_id=' . $oid . ' AND perm_hrn <> "PATH") ORDER BY group_hrn';
	$res = mysql_query($sql);
	while ($row = @mysql_fetch_array($res, MYSQL_ASSOC)) {
		$row['sq'] = query_encode('gid', $row['group_id'], 'oid', $oid);
		$group_rights[] = $row;
	}
	$count = max($countu = count($user_rights), $countg = count($group_rights));
	$echo = "[";
	for ($i = 0; $i < $count; $i++) {
		$pre = $i ? ',' : '';
		$echo.= $pre . "[";
		if ($i < $countu) {
			$echo.= "'" . rawurlencode("{$user_rights[$i]['user_fname']} {$user_rights[$i]['user_mname']} {$user_rights[$i]['user_lname']}") . "',";
			switch ($user_rights[$i]['perm_hrn']) {
			case 'EDIT':
			case 'ROOT':
			case 'OWNER':
			case 'WORKFLOW':
			case 'ADMIN':
				$echo.= "5,1,1,";
				break;

			case 'WRITE':
				$echo.= "0,3,1,";
				break;

			case 'READ':
				$echo.= "0,0,2,";
				break;

			default:
				$echo.= "0,0,0,";
			}
			if ($user_rights[$i]['object_right'] < 6) $echo.= "'" . $user_rights[$i]['sq'] . "',";
			else $echo.= "'',";
		} else {
			$echo.= "'',0,0,0,'',";
		}
		if ($i < $countg) {
			//			$echo .="'".rawurlencode("<a href='#' onclick=\"pg('$_S{$group_rights[$i]['group_id']}')\" title=\"show members\">{$group_rights[$i]['group_hrn']}</a>")."',";
			$echo.= "['$_S{$group_rights[$i]['group_id']}','" . rawurlencode($group_rights[$i]['group_hrn']) . "'],";
			switch ($group_rights[$i]['perm_hrn']) {
			case 'WRITE':
			case 'ADMIN':
				$echo.= "3,1,";
				break;

			case 'READ':
				$echo.= "0,2,";
				break;

			default:
				$echo.= "0,0,";
			}
			$echo.= "'" . $group_rights[$i]['sq'] . "'";
		} else {
			$echo.= "'',0,0,''";
		}
		$echo.= ']';
	}
	$echo.= ']';
	echo "parent.L.permData = $echo;";
	echo "parent.L.paintPermTbl('" . query_encode('oid', $oid) . "');";
	break;

case 'C': // change object
	if ($_POST['C'] && ($typ = $_POST['typ'])) {
		if ($typ == '1') $oid = (int)$_POST['C'];
		else {
			$query = query_sdecode($_POST['C']);
			$oid = $query['xflv'];
		}
	}
	$value = array();
	if (isset($_POST['object_hrn'])) $value['object_hrn'] = utf8_decode($_POST['object_hrn']);
	if (isset($_POST['object_dsc'])) $value['object_dsc'] = utf8_decode($_POST['object_dsc']);
	if (isset($_POST['url'])) $value['URL'] = $_POST['url'];
	$value['fk_object_type_id'] = (int)$_POST['typ'];
	if (isset($_POST['sub_type'])) $value['object_subtype'] = (int)$_POST['sub_type'];
	// if file object then avoid changes of revisions
	if ($value['fk_object_type_id'] > 12) {
		$sql = "SELECT info_object_file_origin FROM {$_SESSION['DB_IC']}.info_object_file WHERE fk_object_id = '$oid'";
		$res = mysql_query($sql);
		while ($row = @mysql_fetch_row($res)) list($origin) = $row;
		list($maxOid) = mysql_fetch_row(mysql_query("SELECT MAX(fk_object_id) FROM {$_SESSION['DB_IC']}.info_object_file WHERE info_object_file_origin = '$origin' GROUP BY info_object_file_origin"));
		if ($oid != $maxOid) die('<script>alert("You can\'t change previous revisions.")</script>');
	}
	if ($value['fk_object_type_id'] == 5 || $value['fk_object_type_id'] == 11) {
		$value['w_auth'] = (int)$_POST['w_auth'];
		$value['w_target'] = $_POST['w_target'];
		$value['w_width'] = (int)$_POST['w_width'];
		$value['w_height'] = (int)$_POST['w_height'];
	}
	// change owner
	if ((int)$_POST['sowner']) $value['owner'] = (int)$_POST['sowner'];
	require PATH_IC . 'IC2objutil.php';
	$or = IC2_get_obj_right($oid, $uid, $_SESSION['group_ids']);
	if ($or > 3) {
		$msg = update_object($value, $oid);
		echo '1';
	} else echo "You have no permissions to change the object properties. $oid $or $uid ", print_r($_POST, 1);
	break;

case 'A': // add object
	if (!($typ = $_POST['typ']) || !($base = $_POST['base'])) break;

	$value = array();
	$value['object_hrn'] = utf8_decode($_POST['object_hrn']);
	$value['object_dsc'] = utf8_decode($_POST['object_dsc']);
	$value['URL'] = @$_POST['url'];
	$value['fk_object_type_id'] = (int)$_POST['typ'];
	$value['object_subtype'] = (int)$_POST['sub_type'];
	if ($value['fk_object_type_id'] == 5 || $value['fk_object_type_id'] == 11) {
		$value['w_auth'] = (int)$_POST['w_auth'];
		$value['w_target'] = $_POST['w_target'];
		$value['w_width'] = (int)$_POST['w_width'];
		$value['w_height'] = (int)$_POST['w_height'];
	}
	$pre = $post = '';
	//		if($value['fk_object_type_id']>12){ // file upload
	//			$pre='<?xml version="1.0" encoding="ISO-8859-1"
	//		?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "xhtml1-transitional.dtd"><html xmlns="http://www.w3.org/1999/xhtml">
	//      <script><![CDATA[parent.my.onDocUploaded("';
	//			$post='")]]></script></html>';
	//		}
		require PATH_IC.'IC2objutil.php';
		$or = IC2_get_obj_right ($base,$uid,$_SESSION['group_ids']);//echo 'OR=',$or,$_SESSION['IClist_oid'];
		if($or > 2) {
      $echo = insert_object($value,$base);
      // special case: link object to a template object in maintenance portal
//      echo 'tplId:',$_POST['tplId'],'',$echo;
      if(($cId = (int)$echo) >0 && ($tplId = (int)$_POST['tplId'])>0){
        $qry = "UPDATE {$_SESSION['DB_APPS']}.maintenance SET m_lnk_obj_id = IF(m_lnk_obj_id,CONCAT_WS(',',m_lnk_obj_id,'$cId'),'$cId') WHERE m_id='$tplId'";
        $rs = mysql_query($qry);
        if(!$rs) exit('Error: '.mysql_error());
      }
			exit('<script>parent.my.onDocUploaded("'.$echo.'")</script>');
		} else 
			exit( '<script>alert("You have no permissions to add an item to this object.")</script>');
	break;
	case 'D':	// delete objects
		if(($idstr=$_POST['D']) && ($typstr=$_POST['typ'])) {
			$id = explode('&',$idstr);
			$typ = explode('&',$typstr);
		}

		require PATH_IC.'IC2objutil.php';
		for($i=0;$i<count($id);$i++) {
			if($typ[$i]==1) $oid = $id[$i];
			else {$query = query_decode('',$id[$i]); $oid=$query['xflv'];}
			$or = IC2_get_obj_right ($oid,$uid,$_SESSION['group_ids']);
			if($or > 4) {
				$msg=delete_object($oid);
				if($msg===false)
					exit('ok');
				else
					exit('Error: '.print_r($msg,1));
			} else echo 'You have no permissions to delete the object.';
		}
	break;
	case 'P':	// paste shortcuts
		require PATH_IC.'IC2objutil.php';
		$or = IC2_get_obj_right ($_SESSION['IClist_oid'],$uid,$_SESSION['group_ids']);
		if($or<3) {
			echo '<script>alert("You have no permissions to paste a shortcut.")</script>';break;}
		if(($idstr=$_POST['D']) && ($typstr=$_POST['typ'])) {
			$id = explode('&',$idstr);
			$typ = explode('&',$typstr);
		}
		for($i=0;$i<count($id);$i++) {
			if($typ[$i]==1) $oid = $id[$i];
			else {$query = query_decode('',$id[$i]); $oid=$query['xflv'];}
			$or = IC2_get_obj_right ($oid,$uid,$_SESSION['group_ids']);
			if($or > 2) {$msg=paste_sc($oid,$_SESSION['IClist_oid'],$uid);
				if($msg===false && $typ[$i]==1) $refresh_explorer=true;
	//			echo '<script>alert("msg="+"',$msg,'")</script>';
			} else echo '<script>alert("You have no permissions to paste a shortcut.")</script>';
		}
	break;
	case 'M':	// move objects
		require PATH_IC.'IC2objutil.php';
    if(isset($_POST['T']))
      $target = (int)$_POST['T'];
    else
      $target = $_SESSION['IClist_oid'];
    if(!$target) {
      echo 'Target unknown.';
      break;
    }
		$or = IC2_get_obj_right ($target,$uid,$_SESSION['group_ids']);
		if($or<3) {
			echo 'You have no permissions to move objects to this destination.';
      break;
    }
		if(($idstr=$_POST['D']) && ($typstr=$_POST['typ'])) {
			$id = explode('&',$idstr);
			$typ = explode('&',$typstr);
		}
    $to_trash = $_POST['TR']?true:false;
		for($i=0;$i<count($id);$i++) {
			if($typ[$i]==1) $oid = $id[$i];
			else {$query = query_decode('',$id[$i]); $oid=$query['xflv'];}
			$or = IC2_get_obj_right ($oid,$uid,$_SESSION['group_ids']);
			if($or > 4) {
        $msg = move($oid,$target,$uid);
				if($msg===false && $typ[$i]==1) {$refresh_explorer=false;echo 'ok';}
        if($to_trash){
          removeRights($oid,$uid);
        }
	//			echo '<script>alert("msg="+"',$msg,'")</script>';
			} else echo 'You have no permissions to move this object.';
		}
	if($refresh_explorer) echo '<script>parent.refreshExplorer();</script>';
	break;
	case 'PG': // provide group names
		if($g=(int)$_POST['g']){
			$qry="SELECT user_lname,user_fname,user_mname,group_hrn FROM {$_SESSION['DB_IC']}.info_user JOIN {$_SESSION['DB_IC']}.info_fk_user_group ON info_user.user_id=info_fk_user_group.fk_user_id JOIN {$_SESSION['DB_IC']}.info_group ON info_group.group_id=info_fk_user_group.fk_group_id WHERE info_fk_user_group.fk_group_id='$g' ORDER BY user_lname";
			if($res = mysql_query($qry)) {
				$ret = '';
				while ($row = @mysql_fetch_row($res)) {$gr=$row[3];$row[0] .=', '.$row[1];if($row[2])$row[0] .=', '.$row[2];$ret .= ',"'.$row[0].'"';}
				$ret .= '];parent.dlist("vgroup",vgroup);';
				echo 'var vgroup=["Members of Group \''.$gr.'\'"'.$ret;
			} else die('<script>alert("'.mysql_error().'")</script>');
		}
	break;
	default: echo 'Unknown request:',print_r($_POST,1);
}

//file_put_contents('log.txt',"\n".date('d-m-y H:i:s').' user:'.$_SESSION['user_lname'].' Rx rights:'.$rights."\n".ob_get_contents()."\n", FILE_APPEND);

session_write_close();
header('Content-Length: '.ob_get_length());
if ( stristr($_SERVER["HTTP_ACCEPT"],"application/xhtml+xml") ) { 
  header("Content-type: application/xhtml+xml"); 
} else { 
  header("Content-type: text/xml"); 
} 
header('Connection: close');
//ob_end_flush();
die;

####################  FUNCTIONS  ################################################################################

function content($oid,$uid,$group_ids,$dest,$filt=1,$as_array=false,$parent=false) {	// list table content
	global $admin,$rights;
	$db = $_SESSION['DB_IC'];
	$dba = $_SESSION['DB_APPS'];
  $tbl_ur = $db.'.user_rights';
  $tbl_gr = $db.'.group_rights';
	$thumbdir = 'b:/ic/thumbs/';
  
  if(stristr($_SERVER['HTTP_HOST'],'dnw.aero')){
    $tbl_ur = $dba.'.user_rights';
    $tbl_gr = $dba.'.group_rights';
	  $thumbdir = 'd:/ic/thumbs/';
  }

	$qry = '
	SELECT
	tbl.object_id AS id,
	object_type_icon AS icon,
	tbl.object_hrn AS name,
	CASE
		WHEN (fk_object_type_id =1 && xlink=0) THEN info_object_folder_description
		WHEN (fk_object_type_id=10 && xlink=0) THEN object_mail_subject
		WHEN (fk_object_type_id=7 && xlink=0) THEN info_object_blog_description
		WHEN (fk_object_type_id IN (4,5,10,11,12) && xlink=0) THEN info_object_url_description
		WHEN (fk_object_type_id >12 && xlink=0) THEN info_object_file_description
		WHEN xlink > 0 THEN IF(LENGTH(tbl.object_short_desc)>0,tbl.object_short_desc,tbl.object_hrn)
		ELSE tbl.object_short_desc
	END AS descr,
	DATE_FORMAT(tbl.object_last_upd,"%d-%m-%Y") AS lup,
	tbl.fk_object_type_id AS tid,
	CASE
		WHEN(fk_object_type_id >12) THEN CONCAT(info_object_file_revision,"|",info_object_file_size)
		WHEN(fk_object_type_id=7) THEN info_object_blog_size
		WHEN(fk_object_type_id=10) THEN CONCAT_WS("|",object_mail_at,object_mail_attachments,object_mail_size)
		ELSE CONCAT("")
	END AS remark,
	GREATEST(IFNULL(CAST(join10.object_right AS unsigned),0),IFNULL(MAX(CAST(join11.object_right AS unsigned)),0)) AS ur,
	xlink AS xl,
	CONCAT_WS(" ",join16.user_fname,join16.user_mname,join16.user_lname)AS owner,
	CONCAT_WS(" ",join17.user_fname,join17.user_mname,join17.user_lname)AS cby,
	info_object_file_realname as lnk,
	info_object_url_link as url,
  info_object_folder_typ as ftype,
  object_parent_id as parent
	FROM '.$db.'.info_object AS tbl
	LEFT OUTER JOIN '.$db.'.info_object_type AS join2 ON join2.object_type_id = tbl.fk_object_type_id
	LEFT OUTER JOIN '.$db.'.info_object_folder AS join6 ON join6.fk_object_id=tbl.object_id
	LEFT OUTER JOIN '.$db.'.info_object_mail AS join3 ON join3.fk_object_id=tbl.object_id
	LEFT OUTER JOIN '.$db.'.info_object_blog AS join4 ON join4.fk_object_id=tbl.object_id
	LEFT OUTER JOIN '.$db.'.info_object_url AS join7 ON join7.fk_object_id=tbl.object_id
	LEFT OUTER JOIN '.$db.'.info_object_file AS join8 ON join8.fk_object_id=tbl.object_id
	LEFT OUTER JOIN '.$tbl_ur.' AS join10 ON join10.fk_object_id=tbl.object_id AND join10.fk_user_id='.$uid.'
	LEFT OUTER JOIN '.$tbl_gr.' AS join11 ON join11.fk_object_id=tbl.object_id AND join11.fk_group_id IN ('.$group_ids.')
	LEFT OUTER JOIN '.$db.'.info_user AS join16 ON join16.user_id = tbl.fk_user_id
	LEFT OUTER JOIN '.$db.'.info_user AS join17 ON join17.user_id = tbl.object_changed_by
	WHERE ('.$filt.' AND(join10.object_right > 1 OR join11.object_right > 1))
	GROUP BY tbl.object_id
	ORDER BY IF(join2.object_type_hrn="Folder",CONCAT("1",tbl.object_hrn),tbl.object_hrn),tbl.object_last_upd DESC
	';
	$res = 'Error: ';
	$res_ar = array();
	if($rs=mysql_query($qry)) {
    if($dest){
		  $res = $dest.'=[["0","","","","","","1","",""],';
    } else
      $res = '';
		while($r=mysql_fetch_assoc($rs)){
			if($r['xl']>0) {if($r['tid'] == 1) $xl=$r['xl']; else $xl=query_encode('xflv',$r['xl'],'k',rand());} else $xl='';
			$at=$rev=0;
			if(strlen($r['remark'])) {
		        if($r['tid'] == 10){
				  list($at,$att,$size) = explode("|",$r['remark']);
				}
				elseif($r['tid'] == 7) $size=$r['remark'];
		        else
				  list($rev,$size) = explode("|",$r['remark']);
				if(($size /=1024)>1024) {$size /= 1024;$dm=' MB';} else $dm=' kB';
				if($rev) $r['remark']="Rev.:&nbsp;$rev "; else $r['remark']='';
				$r['remark'] .= "Size:&nbsp;".sprintf('%01.2f',$size).$dm;
			    if($at > 0) $r['remark'] .= rawurlencode(" <img src='images/paperclip.gif' title='$att' align='absmiddle'>");
			}
			if($xl) $typ_id = 6;
			//elseif($r['tid'] >12) $typ_id = 13;
			else $typ_id = $r['tid'];
			// if folder then provide object id, else provide encrypted object id

			$r['icon'] = PATH_IC.'img/'.$r['icon'].'^dbl-click to open';
			if($r['tid'] == 1) {
        $id=$r['id']; 
        switch($r['ftype']){
          case 10: $r['icon'] = 'trashs.png?10^Trash'; break;
          case 11: $r['icon'] = 'facility.gif?11^Facility'; break;
          case 12: $r['icon'] = 'pos.gif?12^System'; break;
          case 13: $r['icon'] = 'mo.gif?13^Maintenance Object'; break;
          case 14: $r['icon'] = 'part.gif?14^Part'; break;
          case 15: $r['icon'] = 'so.gif?15^Storage'; break;
  				default: $r['icon'] = 'obj_folder.gif?^Folder'; break;
        }
      }
        else $id=query_encode('xflv',$r['id'],'k',rand());

			if($admin) $r['ur']=$rights;
      $style='';

			// object type && permission dependend information
			switch($typ_id){
				case 6:
				case 1: 
				case 10: $r['lnk'] = $r['name']; break;
				case 4: $r['lnk'] = $r['url']; break;
				case 5:
				case 11:
				case 12: 
					$r['lnk'] = $r['ur'] > 5 ? $r['url']:'InfoCenter Application';
					break;
				case 115:
				case 117:
				case 118:
				case 180:
          $fname = $r['id'].'.dat';
          if(!is_file($thumbdir.$fname)){
            require_once(PATH_IC.'getThumb.inc.php');
          }
          if(is_file($thumbdir.$fname))
            list($width, $height, $type, $attr) = getimagesize($thumbdir.$fname);
          else
            $width=$height=0;
          $style = 'style="padding-left:'.($width+5).'px;height:'.$height.'px;background:transparent;background-image:url('.PATH_IC.'img/?t'.$r['id'].');background-repeat:no-repeat;"';
					break;
			}


// xml coding
			$res_ar[$id] = '<row id="' .$id. '">'.($parent? '<cell>' .$r['parent']. '</cell>':'').'
      <cell><![CDATA['. $r['icon']. ']]></cell>
      <cell><![CDATA[' .utf8_encode($r['name']). ']]></cell>
      <cell '.$style.'><![CDATA[' .utf8_encode(str_replace(array("\n","\r"),array('<br />',''),$r['descr'])). ']]></cell>
      <cell>' .$r['lup']. '</cell>
      <cell><![CDATA[' .$r['remark']. ']]></cell>
      <cell>' .$r['ur']. '</cell>
      <cell>' .$r['tid']. '</cell>
      <cell><![CDATA[' .$xl. ']]></cell>
      <cell><![CDATA[' .utf8_encode($r['owner']). ']]></cell>
      <cell><![CDATA[' .utf8_encode($r['cby']). ']]></cell>
      <cell><![CDATA[' .utf8_encode($r['lnk']). ']]></cell>'.($as_array?'':'</row>');
    }
		
    $res = $as_array ? $res_ar : $res.join('',$res_ar);

	}
  elseif(mysql_error()) 
    $res = mysql_error();
  else
    $res=$qry;
	return $res;
}
function showThumb($s,$qlink){
	if($qlink){
		$href = "img/?$qlink";
		return '<table class="thumb"><tr><td><img src="../../img/?t'.$qlink.'"></td><td class=pme-cell-0 style="border:0">'.$s.'</td></tr></table>';
//		return '<table><tr><td><img src="img/?t'.$qlink.'" onClick=\'%%%%\'></td><td class=pme-cell-0 style="border:0">'.$s.'</td></tr></table>';
	}
	else
		return $s;
}
?>