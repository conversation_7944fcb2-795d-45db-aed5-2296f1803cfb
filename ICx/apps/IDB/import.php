<?php
/********************************************************************************
 * This script performs mysql data import from csv (and html tables) files
 *  - all data should be available in one main file and optional a second file, which could be merged to the main file
 *  - during merge each row of the main file will be extended by the corresponding row of the merge file
 *  - the number of rows in the merged file should be the same as in the main file
 *  - both files may have the same amount of table header rows
 *  - html files should have data in a html table
 *  - html files will be converted into csv and saved as txt files
 *  - conditional insert into master - details database tables supported
 *  - mysql table columns, which should be filled, must have the remark 'imp'
 *  - conversion from csv to mysql table(s) is configured by patch panels
 *
 *  Patch-panel configuration settings:
 *  - fixed values can be defined by a string with double-quotes
 *  - a field from the csv table will be selected by the related index (0,1,...)
 *  - conditional patching is provided with a IF-function similar as in sql but condition must be a field index
 *  - column values can be concatenated  and extended with fixed strings, e.g.: concat(21," ",3) concatenates field with index 21 with a space and with field index 3
 *  - current date will be inserted with NOW()
 *  - date fields should have the standard MySql coding Y-m-d. To swap a date field from d-m-Y to Y-m-d, add 'swap' to the index (e.g.: 5 swap)
 *  - field for conditional insert uses field index of the input record
 *
 *  configuration settings could be saved to and loaded from files.
 *
 *  G. Lehmann, 16-05-2012
 ************************************************************************************/
if (isset($_GET['rcfg'])) {
	$allfiles = scandir(getcwd());
	$cfgfiles = array();
	foreach ($allfiles as $f) {
		$fa = explode('.', $f);
		if ($fa[1] == 'impcfg') $cfgfiles[] = $fa[0];
	}
	if (count($cfgfiles)) echo join(',', $cfgfiles);
	else echo 'none';
	exit;
}
if ($_POST['configure'] == $_POST['cfgfile'] && is_file($_POST['cfgfile'] . '.impcfg')) {
	$f = $_POST['cfgfile'];
	$a = file_get_contents($_POST['cfgfile'] . '.impcfg');
	$_POST = unserialize($a);
	$_POST['actio'] = 'read';
	$_POST['cfgfile'] = $f;
}
if ($_POST['actio'] == 'read' || $_POST['actio'] == 'test' || $_POST['actio'] == 'insert' || $_POST['actio'] == 'insert all' || $_POST['scfg'] == 'save conf.') {
	/*** file name of config file ***/
	$cfgfile = $_POST['cfgfile'];
	if (!$cfgfile) $cfgfile = '';
	/*** file name of html or csv table ***/
	$file = $_POST['file'];
	if (!$file) $file = '';
	/*** file name of html or csv table to be merged ***/
	$mfile = $_POST['mfile'];
	if (!$mfile) $mfile = '';
	/*** character used for separation in csv file ***/
	$split = $_POST['split'];
	if (!$split) $split = '\t';
	/*** first row which should be read ***/
	$row = (int)$_POST['row'];
	if (!$row) $row = 1;
	/*** last row which should be read ***/
	$trow = (int)$_POST['trow'];
	if (!$trow || $trow < $row) $trow = $row;
	/*** database name for insert***/
	$db = $_POST['db'];
	if (!$db) $db = 'apps';
	/*** table name for insert***/
	$tbl = $_POST['tbl'];
	if (!$tbl) $tbl = '';
	/*** flush table before insert ***/
	$trtbl = $_POST['trtbl'];
	if (!$trtbl) $trtbl = 'NO';
	/*** database name for details insert***/
	$db2 = $_POST['db2'];
	if (!$db2) $db2 = '';
	/*** table name for details insert***/
	$tbl2 = $_POST['tbl2'];
	if (!$tbl2) $tbl2 = '';
	/*** flush details table before insert ***/
	$trtbl2 = $_POST['trtbl2'];
	if (!$trtbl2) $trtbl2 = 'NO';
	/*** foreign key field ***/
	$fkf = $_POST['fkf'];
	if (!$fkf) $fkf = '?';
	/*** conditional insert ***/
	$cins2 = $_POST['cins2'];
	if (!$cins2) $cins2 = '';
	/*** database name for details insert***/
	$db3 = $_POST['db3'];
	if (!$db3) $db3 = '';
	/*** table name for details insert***/
	$tbl3 = $_POST['tbl3'];
	if (!$tbl3) $tbl3 = '';
	/*** flush details table before insert ***/
	$trtbl3 = $_POST['trtbl3'];
	if (!$trtbl3) $trtbl3 = 'NO';
	/*** foreign key field ***/
	$fkf3 = $_POST['fkf3'];
	if (!$fkf3) $fkf3 = '?';
	/*** conditional insert ***/
	$cins3 = $_POST['cins3'];
	if (!$cins3) $cins3 = '';
	/*** multiple space: if 'YES' remove multiple spaces ***/
	$mspace = $_POST['mspace'];
	if (!$mspace) $mspace = 'YES';
	$patch = $patch2 = array();
	$patch = $_POST['patch'];
	$patch2 = $_POST['patch2'];
	$patch3 = $_POST['patch3'];
	$msg = array();
	if ($_POST['scfg'] == 'save conf.') {
		$f = $_POST['cfgfile'];
		unset($_POST['scfg'], $_POST['cfgfile']);
		if ($f) {
			$f = $f . '.impcfg';
			file_put_contents($f, serialize($_POST));
			$msg[] = 'saved config to ' . $f . '! <br>';
		} else $msg[] = 'File name for config file is missing. Config not saved! <br><pre>' . print_r($_POST, 1) . '</pre>';
	}
}
if ($_POST['actio'] == 'read' || $_POST['actio'] == 'insert' || $_POST['actio'] == 'insert all') {
	if (is_file($file) && $db && $tbl) {
		$fname = explode('.', $file);
		if ($fname[1] == 'htm' || $fname[1] == 'html') {
			// convert html file to csv format and save csv data
			$fname[1] = 'txt';
			file_put_contents(join('.', $fname), html2csv(file_get_contents($file)));
			$file = join('.', $fname);
		}
		//    else
		$data = file($file);
		if ($mfile && is_file($mfile)) { // merge file with mfile. If mfile is html, first convert it to csv
			$mname = explode('.', $mfile);
			if ($mname[1] == 'htm' || $mname[1] == 'html') {
				// convert html file to csv format and save csv data
				$mname[1] = 'txt';
				file_put_contents(join('.', $mname), html2csv(file_get_contents($mfile)));
				$mfile = join('.', $mname);
			}
			$mdata = file($mfile);
			for ($i = 0; $i < count($data); $i++) { // extent each record of $data with record of $mdata
				if (!$mdata[$i]) continue;
				$data[$i] = str_replace(array("\n", "\r"), array(''), $data[$i]) . "\t" . str_replace(array("\n", "\r"), array(''), $mdata[$i]);
			}
			$fname[0].= '_' . $mname[0];
			file_put_contents(join('.', $fname), join("\n", $data));
			$msg[] = 'Merged file saved to ' . join('.', $fname);
		}
		require_once ('../../lib/mysql_simple.inc.php');
		require_once ('../../lib/utils.inc.php');
		// get the record fields from database
		mysql_connect('localhost:3306', 'IC17', 'IC17dnw');
		$qry = "SELECT COLUMN_NAME, COLUMN_COMMENT
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE table_name = '$tbl' AND table_schema = '$db'";
		$fields = array();
		$res = dbs_get_all($qry);
		if ($res[0] === false) $res = mysql_error();
		elseif (!count($res)) {
			$msg[] = "Table $tbl not found";
		} else {
			foreach ($res as $key => $field) {
				if ($field['COLUMN_COMMENT'] == 'imp') {
					$fields[$key]['name'] = $field['COLUMN_NAME'];
					$fields[$key]['value'] = "<input name='patch[$key]' value='{$patch[$key]}'>";
				}
			}
			if (!count($fields)) { // take all fields
				foreach ($res as $key => $field) {
					$fields[$key]['name'] = $field['COLUMN_NAME'];
					$fields[$key]['value'] = "<input name='patch[$key]' value='{$patch[$key]}'>";
				}
			}
		}
		// details table
		$qry = "SELECT COLUMN_NAME, COLUMN_COMMENT
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE table_name = '$tbl2' AND table_schema = '$db'";
		$fields2 = array();
		if ($tbl2) {
			$res = dbs_get_all($qry);
			if ($res[0] === false) $res = mysql_error();
			else {
				foreach ($res as $key => $field) {
					if ($field['COLUMN_COMMENT'] == 'imp') {
						$fields2[$key]['name'] = $field['COLUMN_NAME'];
						$fields2[$key]['value'] = "<input name='patch2[$key]' value='{$patch2[$key]}'>";
					}
				}
			}
		}
		// details table 2
		$qry = "SELECT COLUMN_NAME, COLUMN_COMMENT
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE table_name = '$tbl3' AND table_schema = '$db'";
		$fields3 = array();
		if ($tbl3) {
			$res = dbs_get_all($qry);
			if ($res[0] === false) $res = mysql_error();
			else {
				foreach ($res as $key => $field) {
					if ($field['COLUMN_COMMENT'] == 'imp') {
						$fields3[$key]['name'] = $field['COLUMN_NAME'];
						$fields3[$key]['value'] = "<input name='patch3[$key]' value='{$patch3[$key]}'>";
					}
				}
			}
		}
		if (($record = $data[$row - 1])) {
			switch ($split) {
			case '\t':
				$recordar = explode("\t", $record);
				break;

			default:
				$recordar = explode($split, $record);
				break;
			}
		}
		$tr = count($data);
		$irows = $trow - $row + 1;
		$echo = $qry2 = $qry3 = '';
		// build the insert
		list($qry, $set) = buildInsert($db, $tbl, $patch, $mspace, $fields, $recordar);
		// build insert in details table
		if (count($patch2) && (!$cins2 || ((int)$cins2 && $recordar[$cins2]))) {
			list($qry2, $set2) = buildInsert($db2, $tbl2, $patch2, $mspace, $fields2, $recordar);
		}
		// build insert in details table2
		if (count($patch3) && (!$cins3 || ((int)$cins3 && $recordar[$cins3]))) {
			list($qry3, $set3) = buildInsert($db3, $tbl3, $patch3, $mspace, $fields3, $recordar);
		}
	} else {
		$_POST['actio'] = '';
		if (!$file) $msg[] = "Filename missing.";
		else $msg[] = "File '$file' not found.";
		if (!$db) $msg[] = "Database name missing.";
		if (!$tbl) $msg[] = "Table name missing.";
	}
	$msg = join('<br>', $msg);
}
/**********************/
if ($_POST['actio'] == 'insert') {
	if ($trtbl == 'YES') dbs_update("TRUNCATE TABLE $db.$tbl");
	$inserted = dbs_insert($qry);
	if ((int)$inserted) {
		$echo.= "inserted rowID = $inserted <br>";
		if ($qry2) {
			if ($trtbl2 == 'YES') dbs_update("TRUNCATE TABLE $db2.$tbl2");
			$qry2 = "INSERT INTO $db2.$tbl2 SET $fkf='$inserted', $set2";
			$inserted2 = dbs_insert($qry2);
			if ((int)$inserted2) $echo.= "inserted detail rowID = $inserted2<br>";
			else $echo.= $inserted2;
		}
		if ($qry3) {
			if ($trtbl3 == 'YES') dbs_update("TRUNCATE TABLE $db3.$tbl3");
			$qry3 = "INSERT INTO $db3.$tbl3 SET $fkf3='$inserted', $set3";
			$inserted3 = dbs_insert($qry3);
			if ((int)$inserted3) $echo.= "inserted detail rowID = $inserted3<br>";
			else $echo.= $inserted3;
		}
	} else $echo.= print_r($inserted, 1);
}
if ($_POST['actio'] == 'insert all') {
	$i0 = $row - 1;
	$iend = $trow;
	if ($trtbl == 'YES') dbs_update("TRUNCATE TABLE $db.$tbl");
	if ($trtbl2 == 'YES') dbs_update("TRUNCATE TABLE $db2.$tbl2");
	if ($trtbl3 == 'YES') dbs_update("TRUNCATE TABLE $db3.$tbl3");
	for ($i = $i0; $i < $iend; $i++) {
		$record = $data[$i];
		$set = array();
		$qry = $qry2 = $qry3 = '';
		switch ($split) {
		case '\t':
			$recordar = explode("\t", $record);
			break;

		default:
			$recordar = explode($split, $record);
			break;
		}
		// build the insert
		list($qry, $set) = buildInsert($db, $tbl, $patch, $mspace, $fields, $recordar);
		// build insert in details table
		if (count($patch2) && (!$cins2 || ((int)$cins2 && $recordar[$cins2]))) {
			list($qry2, $set2) = buildInsert($db2, $tbl2, $patch2, $mspace, $fields2, $recordar);
		}
		// build insert in details table 2
		if (count($patch3) && (!$cins3 || ((int)$cins3 && $recordar[$cins3]))) {
			list($qry3, $set3) = buildInsert($db3, $tbl3, $patch3, $mspace, $fields3, $recordar);
		}
		if ($qry) $inserted = dbs_insert($qry);
		if ((int)$inserted) {
			$echo.= "inserted rowID = $inserted <br>";
			if ($qry2) {
				$qry2 = "INSERT INTO $db2.$tbl2 SET $fkf='$inserted', $set2";
				$inserted2 = dbs_insert($qry2);
				if ((int)$inserted2) $echo.= "inserted detail rowID = $inserted2<br>";
				else $echo.= $inserted2;
			}
			if ($qry3) {
				$qry3 = "INSERT INTO $db3.$tbl3 SET $fkf3='$inserted', $set3";
				$inserted3 = dbs_insert($qry3);
				if ((int)$inserted3) $echo.= "inserted detail rowID = $inserted3<br>";
				else $echo.= $inserted3;
			}
		} else $echo.= $inserted;
	}
}
// ---- functions --------------
function buildInsert($db, $tbl, $patch, $mspace, $fields, $recordar)
{
	$now = date('Y-m-d H:i');
	if (!count($patch)) return;
	foreach ($patch as $k => $v) { // loop over record
		if (!strlen($v)) continue;
		$v = str_replace(array('\"', '\,', '\(', '\)'), array('&34;', '&44;', '&40;', '&41;'), trim($v));
		if (substr($v, 0, 3) == 'IF(' && substr($v, -1, 1) == ')') {
			$v = substr($v, 3, -1);
			preg_match('/(\d+), *(\d+|".*"|\bconcat\(.+\)), *(\d+|".*"|\bconcat\(.+\))/', $v, $matches);
			if (trim($recordar[$matches[1]])) $v = $matches[2];
			else $v = $matches[3];
		}
		if ($v[0] == '"') {
			if ($mspace == 'YES') $v = preg_replace('/\s\s+/', ' ', $v);
			$field = $fields[$k]['name'] . "='" . mysql_real_escape_string(trim($v, ' "')) . "'";
		} elseif ($v == 'NOW()') {
			$field = $fields[$k]['name'] . "='" . $now . "'";
		} elseif (stristr($v, 'swap')) {
			$v = preg_replace('/[^0-9]/', '', $v);
			$v = trim($recordar[$v]);
			$v = swapDate($v);
			$field = $fields[$k]['name'] . "='" . $v . "'";
		} elseif (stristr($v, 'concat')) {
			$concat = '';
			$v = str_replace(array('concat', '(', ')'), '', $v);
			$v = explode(',', $v);
			foreach ($v as $valstr) {
				$valstr = trim($valstr);
				if (!strstr($valstr, '"')) {
					$concat.= trim($recordar[(int)$valstr]);
				} else {
					$concat.= substr($valstr, 1, -1);
				}
			}
			$concat = str_replace(array('&34;', '&44;', '&40;', '&41;'), array('"', ',', '(', ')'), $concat);
			$field = $fields[$k]['name'] . "='" . mysql_real_escape_string($concat) . "'";
		} else {
			$vv = trim($recordar[$v]);
			if ($mspace == 'YES') $vv = preg_replace('/\s\s+/', ' ', $vv);
			$field = $fields[$k]['name'] . "='" . mysql_real_escape_string($vv) . "'";
		}
		$set[] = $field;
	}
	$set = @join(',', $set);
	$qry = "INSERT INTO $db.$tbl SET $set";
	return array($qry, $set);
};
function html2csv($html)
{
	// convert html file to csv format
	$doc = new DomDocument;
	$doc->validateOnParse = true;
	$data = str_replace(array('&nbsp;', '<br />', '<br>'), array('', "\n", "\n"), $html);
	@$doc->loadHTML($data);
	$rows = $doc->getElementsByTagName('tr');
	$_row = array();
	for ($i = 0; $i < $rows->length; $i++) {
		$cells = $rows->item($i)->getElementsByTagName('td');
		$_cell = array();
		for ($j = 0; $j < $cells->length; $j++) {
			//          echo "[$i, $j]: ",str_replace("\n",'<br>',$cells->item($j)->nodeValue),'<br>';
			$_cell[] = str_replace("\n", '<br>', $cells->item($j)->nodeValue);
		}
		$_row[] = join("\t", $_cell);
	}
	return join("\n", $_row);
}
?>

<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="content-type" content="text/html; charset=utf-8">
  <meta http-equiv="expires" content="0">
  <title>IDB Import Tool</title>
  <style>
  table td{vertical-align:top;padding-left:20px}
  </style>
  <script src="../../js/dhtmlxcommon.js"></script>
  <script>
  var Cfg = {
    select: function(f){
      document.myform.cfgfile.value = f;
      document.getElementById('configFiles').style.display = 'none';
      document.myform.configure.value = f;
      document.myform.submit();
    },
    read : function(){
      dhtmlxAjax.get('?rcfg',function(ret){
        var cfgFiles = ret.xmlDoc.responseText;
        if(cfgFiles.length && cfgFiles !== 'none'){
          cfgFiles = cfgFiles.split(',');
          for(var i=0; i<cfgFiles.length;i++) cfgFiles[i] = '<a href="#" onclick="Cfg.select(this.innerHTML);return false">'+cfgFiles[i]+'</a>';
          document.getElementById('configFilesContent').innerHTML = cfgFiles.join('<br>');
          document.getElementById('configFiles').style.display = 'block';
        }
        else
          alert('No configuration file found!');
      });
    },
    incr : function(p){
      p = p || -1;
      document.myform.row.value = parseInt(document.myform.row.value)+p;
      document.myform.actio[0].click();
    }
  }
  </script>
</head>
<body style="background-color:#e9e9e9">
  <h3>IDB Import Tool</h3>
  <?php
if ($msg) echo '<span style="color:red;font-weight:bold">', $msg, '</span>';
echo '<form name=myform method=post><input type=hidden name=configure value="">';
echo '<table><tr>';
echo '<td>Import Sources';
echo '<pre>', str_pad('File:', 20, ' ', STR_PAD_LEFT), "<input name='file' value='$file'>\n";
echo str_pad('File to merge:', 20, ' ', STR_PAD_LEFT), "<input name='mfile' value='$mfile'>\n";
echo str_pad('From Row:', 20, ' ', STR_PAD_LEFT), "<input name='row' value='$row'>\n";
echo str_pad('To Row:', 20, ' ', STR_PAD_LEFT), "<input name='trow' value='$trow'>\n";
echo str_pad('Separator:', 20, ' ', STR_PAD_LEFT), "<input name='split' value='$split'>\n";
echo str_pad('Remove mult. spaces:', 20, ' ', STR_PAD_LEFT), "<input name='mspace' value='$mspace'>\n";
echo str_pad('Total rows:', 20, ' ', STR_PAD_LEFT), "<input value='$tr'>\n\n";
echo str_pad('', 20, ' ', STR_PAD_LEFT), '<input type=button value="prev" onclick="Cfg.incr()"> <input type=button value="next" onclick="Cfg.incr(1)"></pre>';
echo '</td>';
echo '<td >Dispatch: ', "Record #$row:", '<pre>', print_r($recordar, 1), '</pre></td></tr>';
echo '<tr><td>';
echo "Database:";
echo '<pre>', str_pad('Main Database:', 20, ' ', STR_PAD_LEFT), "<input name='db' value='$db'>\n";
echo str_pad('Table:', 20, ' ', STR_PAD_LEFT), "<input name='tbl' value='$tbl'>\n";
echo str_pad("Flush Table   ", 20, ' ', STR_PAD_LEFT), "\n";
echo str_pad("before insert:", 20, ' ', STR_PAD_LEFT), "<input name='trtbl' value='$trtbl'>";
echo '</td>';
echo '<td>Fields:<pre>';
if ($fields) foreach ($fields as $k => $v) echo str_pad($v['name'], 20, ' ', STR_PAD_LEFT), ': ', $v['value'], "\n";
echo '</pre></td></tr>';
echo '<tr><td>';
echo '<pre>', str_pad('Details Database 1:', 20, ' ', STR_PAD_LEFT), "<input name='db2' value='$db2'>\n";
echo str_pad('Table:', 20, ' ', STR_PAD_LEFT), "<input name='tbl2' value='$tbl2'>\n";
echo str_pad('Foreign key field:', 20, ' ', STR_PAD_LEFT), "<input name='fkf' value='$fkf'>\n";
echo str_pad("Flush Table   ", 20, ' ', STR_PAD_LEFT), "\n";
echo str_pad("before insert:", 20, ' ', STR_PAD_LEFT), "<input name='trtbl2' value='$trtbl2'>\n";
echo str_pad("Insert only if this", 20, ' ', STR_PAD_LEFT), "\n";
echo str_pad("field has a value:", 20, ' ', STR_PAD_LEFT), "<input name='cins2' value='$cins2'></pre>";
echo '</td>';
echo '<td>Fields:<pre>';
if ($fields2) foreach ($fields2 as $k => $v) echo str_pad($v['name'], 20, ' ', STR_PAD_LEFT), ': ', $v['value'], "\n";
echo '</pre></td></tr>';
echo '<tr><td>';
echo '<pre>', str_pad('Details Database 2:', 20, ' ', STR_PAD_LEFT), "<input name='db3' value='$db3'>\n";
echo str_pad('Table:', 20, ' ', STR_PAD_LEFT), "<input name='tbl3' value='$tbl3'>\n";
echo str_pad('Foreign key field:', 20, ' ', STR_PAD_LEFT), "<input name='fkf3' value='$fkf3'>\n";
echo str_pad("Flush Table   ", 20, ' ', STR_PAD_LEFT), "\n";
echo str_pad("before insert:", 20, ' ', STR_PAD_LEFT), "<input name='trtbl3' value='$trtbl3'>\n";
echo str_pad("Insert only if this", 20, ' ', STR_PAD_LEFT), "\n";
echo str_pad("field has a value:", 20, ' ', STR_PAD_LEFT), "<input name='cins3' value='$cins3'></pre>";
echo '</td>';
echo '<td>Fields:<pre>';
if ($fields3) foreach ($fields3 as $k => $v) echo str_pad($v['name'], 20, ' ', STR_PAD_LEFT), ': ', $v['value'], "\n";
echo '</pre></td></tr>';
echo '</table>';
echo 'SQL: <pre>', $qry, '</pre>';
if ($qry2) echo 'SQL details: <pre>', $qry2, '</pre>';
if ($qry3) echo 'SQL details2: <pre>', $qry3, '</pre>';
?>
  <hr>
<input type=submit value="read" name=actio><input type=submit value="test" name=actio><input type=submit value="insert" name=actio><input type=submit value="insert all" name=actio>&nbsp;&nbsp;<input name=cfgfile value="<?php
echo $cfgfile ?>"><input type=submit value="save conf." name=scfg><input type=button value="get conf." name=rcfg onclick="Cfg.read();">
</form>
<div id=configFiles style="width:20%;height:2cm;overflow:auto;border:1px solid silver; display:none">
Config files:
<div id=configFilesContent></div></div>
<br>Message:
<div style="width:50%;height:3cm;overflow:auto;border:1px solid silver"><?php
echo $echo ?></div>
</body>
</html>