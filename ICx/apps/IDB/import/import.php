<?php
/********************************************************************************
 * This script performs mysql data import from csv (and html tables) files
 *  - all data should be available in one main file and optional a second file, which could be merged to the main file
 *  - during merge each row of the main file will be extended by the corresponding row of the merge file
 *  - the number of rows in the merged file should be the same as in the main file
 *  - both files may have the same amount of table header rows
 *  - html files should have data in a html table
 *  - html files will be converted into csv and saved as txt files
 *  - conditional insert into master - details database tables supported
 *  - mysql table columns, which should be filled, must have the remark 'imp'
 *  - conversion from csv to mysql table(s) is configured by patch panels
 *
 *  Patch-panel configuration settings:
 *  - fixed values can be defined by a string with double-quotes
 *  - a field from the csv table will be selected by the related index (0,1,...)
 *  - conditional patching is provided with a IF-function similar as in sql but condition must be a field index
 *  - column values can be concatenated  and extended with fixed strings, e.g.: concat(21," ",3) concatenates field with index 21 with a space and with field index 3
 *  - current date will be inserted with NOW()
 *  - date fields should have the standard MySql coding Y-m-d. To swap a date field from d-m-Y to Y-m-d, add 'swap' to the index (e.g.: 5 swap)
 *  - field for conditional insert uses field index of the input record
 *
 *  configuration settings could be saved to and loaded from files.
 *
 *  G. Lehmann, 16-05-2012
 ************************************************************************************/
if (!$rights) die('<h3>Access denied!</h3>'); // avoid to run other than as include file
if (isset($_GET['rcfg'])) {
	$allfiles = scandir(getcwd() . '/' . $datadir);
	$cfgfiles = array();
	foreach ($allfiles as $f) {
		$fa = explode('.', $f);
		if ($fa[1] == 'impcfg') $cfgfiles[] = $fa[0];
	}
	if (count($cfgfiles)) echo join(',', $cfgfiles);
	else echo 'none';
	exit;
}
if (isset($_POST['configure']) && isset($_POST['cfgfile'])){
    if($_POST['configure'] == $_POST['cfgfile'] && is_file($datadir . $_POST['cfgfile'] . '.impcfg'))
    {
        $f = $_POST['cfgfile'];
        $a = file_get_contents($datadir . $_POST['cfgfile'] . '.impcfg');
        $_POST = unserialize($a);
        $_POST['actio'] = 'read';
        $_POST['cfgfile'] = $f;
    }
}
if (isset($_POST['actio']) && ($_POST['actio'] == 'read' || $_POST['actio'] == 'test' || $_POST['actio'] == 'insert' || $_POST['actio'] == 'insert all' || $_POST['scfg'] == 'save conf.')) {
	/*** file name of config file ***/
	$cfgfile = $_POST['cfgfile'];
	if (!$cfgfile) $cfgfile = '';
	/*** file name of html or csv table ***/
	$file = $_POST['file'];
	if (!$file) $file = '';
	/*** file name of html or csv table to be merged ***/
	$mfile = $_POST['mfile'];
	if (!$mfile) $mfile = '';
	/*** character used for separation in tsv file ***/
	$split = $_POST['split'];
	if (!$split || $split == '\t') $split = "\t";
	/*** first row which should be read ***/
	$row = (int)$_POST['row'];
	if (!$row) $row = 1;
	/*** last row which should be read ***/
	$trow = (int)$_POST['trow'];
	if (!$trow || $trow < $row) $trow = $row;
	/*** database name for insert***/
	$db = $_POST['db'];
	if (!$db) $db = 'apps';
	/*** table name for insert***/
	$tbl = $_POST['tbl'];
	if (!$tbl) $tbl = '';
	/*** flush table before insert ***/
	$trtbl = $_POST['trtbl'];
	if (!$trtbl) $trtbl = 'NO';
	/*** database name for details insert***/
	$db2 = $_POST['db2'];
	if (!$db2) $db2 = 'apps';
	/*** table name for details insert***/
	$tbl2 = $_POST['tbl2'];
	if (!$tbl2) $tbl2 = 'instrument_calibration';
	/*** flush details table before insert ***/
	$trtbl2 = $_POST['trtbl2'];
	if (!$trtbl2) $trtbl2 = 'NO';
	/*** foreign key field ***/
	$fkf = $_POST['fkf'];
	if (!$fkf && $tbl2 == 'instrument_calibration') $fkf = 'cal_fk_id';
	/*** conditional insert ***/
	$cins2 = $_POST['cins2'];
	if (!$cins2) $cins2 = '';
	/*** database name for details insert***/
	$db3 = $_POST['db3'];
	if (!$db3) $db3 = 'apps';
	/*** table name for details insert***/
	$tbl3 = $_POST['tbl3'];
	if (!$tbl3) $tbl3 = 'instrument_log';
	/*** flush details table before insert ***/
	$trtbl3 = $_POST['trtbl3'];
	if (!$trtbl3) $trtbl3 = 'NO';
	/*** foreign key field ***/
	$fkf3 = $_POST['fkf3'];
	if (!$fkf3 && $tbl3 == 'instrument_log') $fkf3 = 'log_fk_id';
	/*** conditional insert ***/
	$cins3 = $_POST['cins3'];
	if (!$cins3) $cins3 = '';
	/*** multiple space: if 'YES' remove multiple spaces ***/
	$mspace = $_POST['mspace'];
	if (!$mspace) $mspace = 'YES';
	$patch = $patch2 = array();
	$patch = (isset($_POST['patch']) ? $_POST['patch'] : "");
	$patch2 = (isset($_POST['patch2']) ? $_POST['patch2'] : "");
	$patch3 = (isset($_POST['patch3']) ? $_POST['patch3'] : "");
	$msg = array();
	if (isset($_POST['scfg']) && $_POST['scfg'] == 'save conf.') {
		$f = $_POST['cfgfile'];
		unset($_POST['scfg'], $_POST['cfgfile']);
		if ($f) {
			$f = $f . '.impcfg';
			file_put_contents($datadir . $f, serialize($_POST));
			$msg[] = 'saved config to ' . $f . '! <br>';
		} else $msg[] = 'File name for config file is missing. Config not saved! <br><pre>' . print_r($_POST, 1) . '</pre>';
	}
}
if (isset($_POST['actio']) && ($_POST['actio'] == 'read' || $_POST['actio'] == 'insert' || $_POST['actio'] == 'insert all')) {
	if (is_file($datadir . $file) && $db && $tbl) {
		$fname = explode('.', $file);
		if ($fname[1] == 'htm' || $fname[1] == 'html') {
			// convert html file to tsv format and save tsv data
			$fname[1] = 'txt';
			file_put_contents($datadir . join('.', $fname), html2csv(file_get_contents($datadir . $file)));
			$file = join('.', $fname);
		}
		//    else
		$data = file($datadir . $file);
		$msg[] = $datadir . $file;
		if ($mfile && is_file($datadir . $mfile)) { // merge file with mfile. If mfile is html, first convert it to csv
			$mname = explode('.', $mfile);
			if ($mname[1] == 'htm' || $mname[1] == 'html') {
				// convert html file to csv format and save csv data
				$mname[1] = 'txt';
				file_put_contents($datadir . join('.', $mname), html2csv(file_get_contents($datadir . $mfile)));
				$mfile = join('.', $mname);
			}
			$mdata = file($datadir . $mfile);
			for ($i = 0; $i < count($data); $i++) { // extent each record of $data with record of $mdata
				if (!$mdata[$i]) continue;
				$data[$i] = str_replace(array("\n", "\r"), array(''), $data[$i]) . $split . str_replace(array("\n", "\r"), array(''), $mdata[$i]);
			}
			$fname[0].= '_' . $mname[0];
			file_put_contents($datadir . join('.', $fname), join("\n", $data));
			$msg[] = 'Merged file saved to ' . join('.', $fname);
		}
		require_once (PATH_ICx . 'lib/mysql_simple.inc.php');
		require_once (PATH_ICx . 'lib/utils.inc.php');
		// get the record fields from database
		$qry = "SELECT COLUMN_NAME, COLUMN_COMMENT
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE table_name = '$tbl' AND table_schema = '$db'";
		$fields = array();
		$res = dbs_get_all($qry);
		if ($res[0] === false) $res = mysql_error();
		elseif (!count($res)) {
			$msg[] = "Table $tbl not found";
		} else {
			foreach ($res as $key => $field) {
				if ($field['COLUMN_COMMENT'] == 'imp') {
					$fields[$key]['name'] = $field['COLUMN_NAME'];
					$fields[$key]['value'] = "<input name='patch[$key]' value='{$patch[$key]}'>";
				}
			}
			if (!count($fields)) { // take all fields
				foreach ($res as $key => $field) {
					$fields[$key]['name'] = $field['COLUMN_NAME'];
					$fields[$key]['value'] = "<input name='patch[$key]' value='{$patch[$key]}'>";
				}
			}
		}
		// details table
		$qry = "SELECT COLUMN_NAME, COLUMN_COMMENT
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE table_name = '$tbl2' AND table_schema = '$db'";
		$fields2 = array();
		if ($tbl2) {
			$res = dbs_get_all($qry);
			if ($res[0] === false) $res = mysql_error();
			else {
				foreach ($res as $key => $field) {
					if ($field['COLUMN_COMMENT'] == 'imp') {
						$fields2[$key]['name'] = $field['COLUMN_NAME'];
						$fields2[$key]['value'] = "<input name='patch2[$key]' value='{$patch2[$key]}'>";
					}
				}
			}
		}
		// details table 2
		$qry = "SELECT COLUMN_NAME, COLUMN_COMMENT
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE table_name = '$tbl3' AND table_schema = '$db'";
		$fields3 = array();
		if ($tbl3) {
			$res = dbs_get_all($qry);
			if ($res[0] === false) $res = mysql_error();
			else {
				foreach ($res as $key => $field) {
					if ($field['COLUMN_COMMENT'] == 'imp') {
						$fields3[$key]['name'] = $field['COLUMN_NAME'];
						$fields3[$key]['value'] = "<input name='patch3[$key]' value='{$patch3[$key]}'>";
					}
				}
			}
		}
		if (($record = $data[$row - 1])) {
			$recordar = explode($split, $record);
		}
		$tr = count($data);
		$irows = $trow - $row + 1;
		$echo = $qry = $qry2 = $qry3 = '';
		// build the insert
		if (count($patch)) list($qry, $set) = buildInsert($db, $tbl, $patch, $mspace, $fields, $recordar);
		// build insert in details table
		if (count($patch2) && (!$cins2 || ((int)$cins2 && trim($recordar[$cins2])))) {
			list($qry2, $set2) = buildInsert($db2, $tbl2, $patch2, $mspace, $fields2, $recordar);
		}
		// build insert in details table2
		if (count($patch3) && (!$cins3 || ((int)$cins3 && trim($recordar[$cins3])))) {
			list($qry3, $set3) = buildInsert($db3, $tbl3, $patch3, $mspace, $fields3, $recordar);
		}
	} else {
		$_POST['actio'] = '';
		if (!$file) $msg[] = "Filename missing.";
		if (!is_file($datadir . $file)) $msg[] = "File '$datadir$file' not found.";
		if (!$db) $msg[] = "Database name missing.";
		if (!$tbl) $msg[] = "Table name missing.";
	}
}
/**********************/
if (isset($_POST['actio']) && $_POST['actio'] == 'insert') {
	if ($qry) {
		if ($trtbl == 'YES') dbs_update("TRUNCATE TABLE $db.$tbl");
		$inserted = dbs_insert($qry);
		if ((int)$inserted) {
			$echo.= "inserted rowID = $inserted <br>";
			if ($qry2) {
				if ($trtbl2 == 'YES') dbs_update("TRUNCATE TABLE $db2.$tbl2");
				$qry2 = "INSERT INTO $db2.$tbl2 SET $fkf='$inserted', $set2";
				$inserted2 = dbs_insert($qry2);
				if ((int)$inserted2) $echo.= "inserted detail rowID = $inserted2<br>";
				else $echo.= $inserted2;
			}
			if ($qry3) {
				if ($trtbl3 == 'YES') dbs_update("TRUNCATE TABLE $db3.$tbl3");
				$qry3 = "INSERT INTO $db3.$tbl3 SET $fkf3='$inserted', $set3";
				$inserted3 = dbs_insert($qry3);
				if ((int)$inserted3) $echo.= "inserted detail rowID = $inserted3<br>";
				else $echo.= $inserted3;
			}
		} else $echo.= $inserted;
	} else $echo.= 'Dispatch table is empty!';
}
if (isset($_POST['actio']) && $_POST['actio'] == 'insert all') {
	$i0 = $row - 1;
	$iend = $trow;
	if ($trtbl == 'YES') dbs_update("TRUNCATE TABLE $db.$tbl");
	if ($trtbl2 == 'YES') dbs_update("TRUNCATE TABLE $db2.$tbl2");
	if ($trtbl3 == 'YES') dbs_update("TRUNCATE TABLE $db3.$tbl3");
	for ($i = $i0; $i < $iend; $i++) {
		$record = $data[$i];
		$set = array();
		$qry = $qry2 = $qry3 = '';
		$recordar = explode("$split", $record);
		// build the insert
		list($qry, $set) = buildInsert($db, $tbl, $patch, $mspace, $fields, $recordar);
		// build insert in details table
		if (count($patch2) && (!$cins2 || ((int)$cins2 && $recordar[$cins2]))) {
			list($qry2, $set2) = buildInsert($db2, $tbl2, $patch2, $mspace, $fields2, $recordar);
		}
		// build insert in details table 2
		if (count($patch3) && (!$cins3 || ((int)$cins3 && $recordar[$cins3]))) {
			list($qry3, $set3) = buildInsert($db3, $tbl3, $patch3, $mspace, $fields3, $recordar);
		}
		if ($qry) $inserted = dbs_insert($qry);
		if ((int)$inserted) {
			$echo.= "inserted rowID = $inserted <br>";
			if ($qry2) {
				$qry2 = "INSERT INTO $db2.$tbl2 SET $fkf='$inserted', $set2";
				$inserted2 = dbs_insert($qry2);
				if ((int)$inserted2) $echo.= "inserted detail rowID = $inserted2<br>";
				else $echo.= $inserted2[1];
			}
			if ($qry3) {
				$qry3 = "INSERT INTO $db3.$tbl3 SET $fkf3='$inserted', $set3";
				$inserted3 = dbs_insert($qry3);
				if ((int)$inserted3) $echo.= "inserted detail rowID = $inserted3<br>";
				else $echo.= $inserted3[1];
			}
		} else $echo.= $inserted[1];
	}
}
// ---- functions --------------
function buildInsert($db, $tbl, $patch, $mspace, $fields, $recordar)
{
	$now = date('Y-m-d H:i');
	if (!count($patch)) return;
	foreach ($patch as $k => $v) { // loop over record
		if (!strlen($v)) continue;
		$v = str_replace(array('\"', '\,', '\(', '\)'), array('&34;', '&44;', '&40;', '&41;'), trim($v));
		if (substr($v, 0, 3) == 'IF(' && substr($v, -1, 1) == ')') {
			$v = substr($v, 3, -1);
			preg_match('/(\d+), *(\d+|".*"|\bconcat\(.+\)), *(\d+|".*"|\bconcat\(.+\))/', $v, $matches);
			if (trim($recordar[$matches[1]])) $v = $matches[2];
			else $v = $matches[3];
		}
		if ($v[0] == '"') {
			if ($mspace == 'YES') $v = preg_replace('/\s\s+/', ' ', $v);
			$field = $fields[$k]['name'] . "='" . mysql_real_escape_string(trim($v, ' "')) . "'";
		} elseif ($v == 'NOW()') {
			$field = $fields[$k]['name'] . "='" . $now . "'";
		} elseif (stristr($v, 'swap')) {
			$v = preg_replace('/[^0-9]/', '', $v);
			$v = trim($recordar[$v]);
			$v = swapDate($v);
			$field = '`' . $fields[$k]['name'] . "`='" . $v . "'";
		} elseif (stristr($v, 'concat')) {
			$concat = '';
			$v = str_replace(array('concat', '(', ')'), '', $v);
			$v = explode(',', $v);
			foreach ($v as $valstr) {
				$valstr = trim($valstr);
				if (!strstr($valstr, '"')) {
					$concat.= trim($recordar[(int)$valstr]);
				} else {
					$concat.= substr($valstr, 1, -1);
				}
			}
			$concat = str_replace(array('&34;', '&44;', '&40;', '&41;'), array('"', ',', '(', ')'), $concat);
			$field = '`' . $fields[$k]['name'] . "`='" . mysql_real_escape_string($concat) . "'";
		} else {
			$vv = trim($recordar[$v]);
			if ($mspace == 'YES') $vv = preg_replace('/\s\s+/', ' ', $vv);
			$field = '`' . $fields[$k]['name'] . "`='" . mysql_real_escape_string($vv) . "'";
		}
		$set[] = $field;
	}
	$set = @join(',', $set);
	if ($set) $qry = "INSERT INTO $db.$tbl SET $set";
    else $qry = "";
	return array($qry, $set);
};
function html2csv($html)
{
	// convert html file to tsv format
	global $split;
	$doc = new DomDocument;
	$doc->validateOnParse = true;
	$data = str_replace(array('&nbsp;', '<br />', '<br>'), array('', "\n", "\n"), $html);
	@$doc->loadHTML($data);
	$rows = $doc->getElementsByTagName('tr');
	$_row = array();
	for ($i = 0; $i < $rows->length; $i++) {
		$cells = $rows->item($i)->getElementsByTagName('td');
		$_cell = array();
		for ($j = 0; $j < $cells->length; $j++) {
			//          echo "[$i, $j]: ",str_replace("\n",'<br>',$cells->item($j)->nodeValue),'<br>';
			$_cell[] = str_replace("\n", '<br>', $cells->item($j)->nodeValue);
		}
		$_row[] = join("$split", $_cell);
	}
	return join("\n", $_row);
}
?>
<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="content-type" content="text/html; charset=utf-8">
  <meta http-equiv="expires" content="0">
  <title>Instrumentation Portal :: Equipment Import Tool</title>
  <style>
  table td{vertical-align:top;padding-left:20px}
  </style>
  <script src= "<?php
echo PATH_IC ?>js/dhtmlxcommon.js"></script>
  <script>
  window.focus();
  var Cfg = {
    select: function(f){
      document.myform.cfgfile.value = f;
      document.getElementById('configFiles').style.display = 'none';
      document.myform.configure.value = f;
      document.myform.submit();
    },
    read : function(){
      dhtmlxAjax.get('?rcfg',function(ret){
        var cfgFiles = ret.xmlDoc.responseText;
        if(cfgFiles.length && cfgFiles !== 'none'){
          cfgFiles = cfgFiles.split(',');
          for(var i=0; i<cfgFiles.length;i++) cfgFiles[i] = '<a href="#" onclick="Cfg.select(this.innerHTML);return false">'+cfgFiles[i]+'</a>';
          document.getElementById('configFilesContent').innerHTML = cfgFiles.join('<br>');
          document.getElementById('configFiles').style.display = 'block';
        }
        else
          alert('No configuration file found!');
      });
    },
    incr : function(p){
      p = p || -1;
      document.myform.row.value = parseInt(document.myform.row.value)+p;
      document.myform.actio[0].click();
    }
  }
  </script>
</head>
<body style="background-color:#e9e9e9">
  <h3>Instrumentation Portal :: Equipment Import Tool</h3>
  <?php
// read equipment managers
//  $eM = file_get_contents('../perm.txt');
//  $eM = json_decode(preg_replace('/(,|\{)[ \t\n]*(\w+)[ ]*:[ ]*/','$1"$2":',$eM),true);
//echo 'Equipment managers: <pre>',print_r($eM,1),'</pre>';
if (isset($split) && "\t" == $split) $split = '\t';
if (isset($msg)) echo '<span style="color:red;font-weight:bold">', join('<br>', $msg), '</span>';
echo '<form name=myform method=post><input type=hidden name=import value="instruments"><input type=hidden name=configure value="">';
echo '<table><tr>';
echo '<td>Import Sources';
echo '<pre>', str_pad('File:', 20, ' ', STR_PAD_LEFT), "<input name='file' value='" . (isset($file) ? $file : ""). "'>\n";
echo str_pad('File to merge:', 20, ' ', STR_PAD_LEFT), "<input name='mfile' value='" . (isset($mfile) ? $mfile : ""). "'>\n";
echo str_pad('From Row:', 20, ' ', STR_PAD_LEFT), "<input name='row' value='" . (isset($row) ? $row : ""). "'>\n";
echo str_pad('To Row:', 20, ' ', STR_PAD_LEFT), "<input name='trow' value='" . (isset($trow) ? $trow : ""). "'>\n";
echo str_pad('Separator:', 20, ' ', STR_PAD_LEFT), "<input name='split' value='" . (isset($split) ? $split : ""). "'>\n";
echo str_pad('Remove mult. spaces:', 20, ' ', STR_PAD_LEFT), "<input name='mspace' value='" . (isset($mspace) ? $mspace : ""). "'>\n";
echo str_pad('Total rows:', 20, ' ', STR_PAD_LEFT), "<input value='" . (isset($tr) ? $tr : ""). "'>\n\n";
echo str_pad('', 20, ' ', STR_PAD_LEFT), '<input type=button value="prev" onclick="Cfg.incr()"> <input type=button value="next" onclick="Cfg.incr(1)"></pre>';
echo '</td>';
echo '<td>';
if (isset($recordar) && $recordar) {
	echo 'Dispatch: ', "Record #$row:", '<pre>';
	echo '<table>';
	foreach ($recordar as $k => $v) {
		echo '<tr><td>[' . $k . ']</td><td>', $v, '</td></tr>';
	}
	echo '</table></pre>';
} else echo '<strong><font color=red>No data for import!</font></strong>';
echo '</td></tr>';
echo '<tr><td>';
echo "Database:";
echo '<pre>', str_pad('Main Database:', 20, ' ', STR_PAD_LEFT), "<input name='db' value='" . (isset($db) ? $db : ""). "'>\n";
echo str_pad('Table:', 20, ' ', STR_PAD_LEFT), "<input name='tbl' value='" . (isset($tbl) ? $tbl : ""). "'>\n";
echo str_pad("Flush Table   ", 20, ' ', STR_PAD_LEFT), "\n";
echo str_pad("before insert:", 20, ' ', STR_PAD_LEFT), "<input name='trtbl' value='" . (isset($trtbl) ? $trtbl : ""). "'>";
echo '<br><select style="margin-left:42mm;margin-top:1cm">
        <option>Equipment typeID</options>
        <option> 10 Reference Device</options>
        <option> 20 Pressure Module</options>
        <option> 30 Press. Transducer</options>
        <option> 40 Inclinometer</options>
        <option> 50 Temp./Hum.</options>
        <option> 60 Balance</options>
        <option> 70 Comp. Instr.</options>
        <option> 80 Microphone</options>
        <option> 90 Accelerometer</options>
        <option>100 Conditioning Unit</options>
        </select>';
echo '<br><select style="margin-left:42mm;margin-top:1cm">
        <option>Facilities</options>
        <option>16 LLF</options>
        <option>17 HST</options>
        <option>18 GOE</options>
        <option>19 NWB</options>
        <option>20 LST</options>
        <option>21 SST</options>
        <option>23 KKK</options>
        </select>';
echo '<br><select style="margin-left:42mm;margin-top:1cm">
        <option>Equipment Manager</options>
        <option>236 T. Buruma</options>
        <option>140 D. Peters</options>
        <option>315 J. Gomes</options>
        </select>';
echo '</td>';
echo '<td>Fields:<pre>';
if (isset($fields)) foreach ($fields as $k => $v) echo str_pad($v['name'], 20, ' ', STR_PAD_LEFT), ': ', $v['value'], "\n";
echo '</pre></td></tr>';
echo '<tr><td>';
echo '<pre>', str_pad('Details Database 1:', 20, ' ', STR_PAD_LEFT), "<input name='db2' value='" . (isset($db2) ? $db2 : ""). "'>\n";
echo str_pad('Table:', 20, ' ', STR_PAD_LEFT), "<input name='tbl2' value='" . (isset($tbl2) ? $tbl2 : ""). "'>\n";
echo str_pad('Foreign key field:', 20, ' ', STR_PAD_LEFT), "<input name='fkf' value='" . (isset($fkf) ? $fkf : ""). "'>\n";
echo str_pad("Flush Table   ", 20, ' ', STR_PAD_LEFT), "\n";
echo str_pad("before insert:", 20, ' ', STR_PAD_LEFT), "<input name='trtbl2' value='" . (isset($trtbl2) ? $trtbl2 : ""). "'>\n";
echo str_pad("conditional insert:", 20, ' ', STR_PAD_LEFT), "<input name='cins2' value='" . (isset($cins2) ? $cins2 : ""). "'></pre>";
echo '</td>';
echo '<td>Fields:<pre>';
if (isset($fields2)) foreach ($fields2 as $k => $v) echo str_pad($v['name'], 20, ' ', STR_PAD_LEFT), ': ', $v['value'], "\n";
echo '</pre></td></tr>';
echo '<tr><td>';
echo '<pre>', str_pad('Details Database 2:', 20, ' ', STR_PAD_LEFT), "<input name='db3' value='" . (isset($db3) ? $db3 : ""). "'>\n";
echo str_pad('Table:', 20, ' ', STR_PAD_LEFT), "<input name='tbl3' value='" . (isset($tbl3) ? $tbl3 : ""). "'>\n";
echo str_pad('Foreign key field:', 20, ' ', STR_PAD_LEFT), "<input name='fkf3' value='" . (isset($fkf3) ? $fkf3 : ""). "'>\n";
echo str_pad("Flush Table   ", 20, ' ', STR_PAD_LEFT), "\n";
echo str_pad("before insert:", 20, ' ', STR_PAD_LEFT), "<input name='trtbl3' value='" . (isset($trtbl3) ? $trtbl3 : ""). "'>\n";
echo str_pad("conditional insert:", 20, ' ', STR_PAD_LEFT), "<input name='cins3' value='" . (isset($cins3) ? $cins3 : ""). "'></pre>";
echo '</td>';
echo '<td>Fields:<pre>';
if (isset($fields3)) foreach ($fields3 as $k => $v) echo str_pad($v['name'], 20, ' ', STR_PAD_LEFT), ': ', $v['value'], "\n";
echo '</pre></td></tr>';
echo '</table>';
echo 'SQL: <pre>', (isset($qry) ? $qry : ""), '</pre>';
if (isset($qry2)) echo 'SQL details: <pre>', $qry2, '</pre>';
if (isset($qry3)) echo 'SQL details2: <pre>', $qry3, '</pre>';
?>
  <hr>
<input type=submit value="read" name=actio><input type=submit value="test" name=actio><input type=submit value="insert" name=actio><input type=submit value="insert all" name=actio>&nbsp;&nbsp;<input name=cfgfile value="<?php
echo (isset($cfgfile) ? $cfgfile : "") ?>"><input type=submit value="save conf." name=scfg><input type=button value="get conf." name=rcfg onclick="Cfg.read();">
</form>
<div id=configFiles style="width:20%;height:2cm;overflow:auto;border:1px solid silver; display:none">
Config files:
<div id=configFilesContent></div></div>
<br>Message:
<div style="width:50%;height:3cm;overflow:auto;border:1px solid silver"><?php
echo (isset($echo) ? $echo : "") ?></div>
</body>
</html>