<?php
require './IC2auth.inc.php';
if ($uid == 1) $admin = true;
require_once ('./qc.php');
if (empty($group_ids) && is_array($_SESSION['user_groups'])) $group_ids = implode(',', $_SESSION['user_groups']);

include_once 'defines.php';
include_once 'DNW_DB.php';
include_once 'IC2auth.inc.php';
include_once ('./qc.php');

use DNW\DNW_DB;

$DB = new DNW_DB();

$apps = $_SESSION['DB_APPS'];
$q_getObject = "SELECT object_hrn,object_id, fk_object_type_id,
	IFNULL(user_rights.object_right,0) AS ur,MAX(IFNULL(group_rights.object_right,0)) AS gr,object_type_icon,object_sec as sec
	FROM infocenter.info_object
	LEFT OUTER JOIN $apps.user_rights ON user_rights.fk_object_id=object_id AND user_rights.fk_user_id=?
	LEFT OUTER JOIN $apps.group_rights ON group_rights.fk_object_id=object_id AND group_rights.fk_group_id IN (##GROUP_IDS##)
	LEFT OUTER JOIN infocenter.info_object_type ON object_type_id=fk_object_type_id
	WHERE object_id=?
	GROUP BY group_rights.fk_object_id";
$q_getObjectLink = "SELECT object_hrn,object_id, fk_object_type_id, object_link,
	IFNULL(user_rights.object_right,0) AS ur,MAX(IFNULL(group_rights.object_right,0)) AS gr,object_type_icon,object_sec as sec
		FROM infocenter.info_object
	LEFT OUTER JOIN $apps.linked_objects ON object_id=lo_link_oid
	LEFT OUTER JOIN $apps.user_rights ON user_rights.fk_object_id=object_id AND user_rights.fk_user_id=?
	LEFT OUTER JOIN $apps.group_rights ON group_rights.fk_object_id=object_id AND group_rights.fk_group_id IN (##GROUP_IDS##)
	LEFT OUTER JOIN infocenter.info_object_type ON object_type_id=fk_object_type_id
		WHERE id=?
	GROUP BY group_rights.fk_object_id";
$q_getLinkObject = 'SELECT IF(info_obj_url_icx_link<>"",info_obj_url_icx_link,info_object_url_link)as link,info_object_urL_target,info_object_url_height,info_object_url_width, info_object_url_authenticate FROM infocenter.info_object_url WHERE fk_object_id=?';

if ($uid == ADMIN_USER_ID) $admin = true;
else $admin = false;

$group_ids = $_SESSION['group_ids'];
$_SESSION['my_or_download.php'] = $rights; // provide rights to associated script
if (strlen($sq = $_REQUEST['sq'])) {
	//echo "<script>alert('$sq')</script>";
	$query = query_decode('', $sq);
	//print_r($query);
	//echo 'step2<br>';exit;
	if (isset($query['xflv']) && ($xfl = (int)$query['xflv']) > 0) $q = 0;
	elseif (isset($query['xflo']) && ($xfl = (int)$query['xflo']) > 0) $q = 1;
} elseif (isset($_REQUEST['xflv']) && ($xfl = (int)$_REQUEST['xflv']) > 0) $q = 0;
elseif (isset($_REQUEST['xflo']) && ($xfl = (int)$_REQUEST['xflo']) > 0) $q = 1;
else exit('nothing received..');
$noaccess = '<script>alert("Access denied: No permissions to open this object.")</script>';
$info = $_SESSION['DB_IC'];
switch ($q) {
	case 0:
		$result = $DB->Select(str_replace("##GROUP_IDS##", $group_ids, $q_getObject), [$uid, $xfl]);
		trigger_error(var_export(str_replace("##GROUP_IDS##", $group_ids, $q_getObject), true));
		trigger_error($uid);
		trigger_error($xfl);
		if (!$result) die('DB Error occurred');
		list($name, $object_id, $object_type, $ur, $gr, $icon, $sec) = $result;
		if (($ur = max($ur, $gr)) < 2 && $admin == false) die($noaccess);
		break;

	case 1:
		$result = $DB->Select(str_replace("##GROUP_IDS##", $group_ids, $q_getObjectLink), [$uid, $xfl]);
		if (!$result) die('DB Error occurred');
		list($name, $object_id, $object_type, $object_link, $ur, $gr, $icon, $sec) = $row;
		if (($ur = max($ur, $gr)) < 2 && $admin == false) die($noaccess);
		break;

	default:
		die($noaccess);
}

$secSc = <<<secScript
<script>
var o;
if(((parent && parent.name == 'ICx')&&(o=parent))||((opener&&opener.name=='notify')&&(o=opener))) o&&o.L.requestOTP($object_type);
else alert('The requested file is confidential ');</script>
secScript;


if ($sec > 1 && (!$_SESSION['usec'] || !$_SESSION['usec'][$object_id] || ($_SESSION['usec'][$object_id] != $_COOKIE['ICsec_'.$object_id]))) die($secSc);
$_SESSION['get_file']['oid'] = $object_id;
$_SESSION['get_file']['ur'] = $ur;
$key = $_SESSION['get_file']['key'] = $name;
$_GET['file'] = urlencode($name);

if ($object_type == TYPE_MAIL) { // email objects
	$qry = "SELECT object_mail_id, object_mail_msguid FROM {$_SESSION['DB_IC']}.info_object_mail WHERE fk_object_id='{$xfl}'";
	$res = $DB->Select($qry,[],true);
	if($res) list($id, $msguid) = $res;
	trigger_error('$xfl: '.$xfl);
	trigger_error('in s.php: '.var_dump($res));
	// check for eml file
	$file = PATH_MAIL_STORE . "/$id.eml";
	if (file_exists($file)) {
		header("Pragma: public");
		header("Expires: Thu, 19 Nov 1981 08:52:00 GMT");
		header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
		header("Cache-Control: no-store, no-cache, must-revalidate");
		header("Cache-Control: private");
		header("Content-Transfer-Encoding: binary");
		header("Content-Type: application/octet-stream");
		header("Content-Disposition: attachment; filename=\"email.eml\"");
		header("Content-Length: " . filesize($file));
		header('Connection: close');
		ob_clean();
		flush();
		readfile($file);
		exit;
	}
	if (!$msguid) exit('<script>alert("Error: No uid for requested email.")</script>');
	$sq = query_encode(rand(), '', 'msguid', $msguid);
	$_GET['sq'] = $sq;
	require_once 'apps/imap/IMAPmessage_viewer.php';
	exit;
}
//  file objects;
if (($object_type >= TYPE_FILE) && (!in_array($object_type, array(TYPE_HTML, TYPE_TXT, TYPE_GIF, TYPE_JPEG, TYPE_PNG, TYPE_PJPEG)) || $remote)) {
	include 'download.php';
	exit;
}

echo '<script>
function popwindow(url, wname, wsize) {
 if(wsize !="") wsize = wsize + ",scrollbars=1, resizable=1, left=50, top=10";
 var myWindow = window.open(url, wname, wsize);
 myWindow.focus();
}
function popfull(url) {
 var myWindow = window.open(url, "_blank","");
 myWindow.focus();
}
function popIC(url, wname, wsize){
  top.ICW.open(url, wname, wsize);
}
</script>';

if (in_array($object_type, array(TYPE_HTML, TYPE_TXT))) {
	echo '<script>popwindow("download.php?file=' . urlencode($name) . '&' . rand() . '","","height=600, width=700, menubar=yes");window.close();</script>';
	exit;
}
if (in_array($object_type, array(TYPE_GIF, TYPE_JPEG, TYPE_PNG, TYPE_PJPEG))) {
	$mime = array('115' => 'gif', '117' => 'jpg', '118' => 'png', '180' => 'pjpeg',);
	echo '<script>popwindow("img/?' . $object_id . '.' . $mime[$object_type] . '","","height=600, width=700, menubar=yes");window.close();</script>';
	exit;
}
if ($object_type == TYPE_BLOG) { // text/html blog/message object
	$_SESSION['view_blog']['oid'] = $object_id;
	$_SESSION['view_blog']['ur'] = $ur;
	echo '<script>popwindow("view_blog.php","blog' . $object_id . '","height=600, width=870, menubar=yes");window.close();</script>';
	exit;
}
// apps & url objects
$x = $DB->Select($q_getLinkObject, [$xfl]);
//$sql = 'SELECT info_object_url_icx_link as link,info_object_urL_target,info_object_url_height,info_object_url_width, info_object_url_authenticate FROM '.$_SESSION['DB_IC'].'.info_object_url WHERE fk_object_id='.$xfl;
$url = $x['link'];
$target = $x['info_object_urL_target'];
$wh = $x['info_object_url_height'];
$ww = $x['info_object_url_width'];
$wna = explode('?', $url);
$wn = basename($wna[0]);
// if authentication is required, set user rights
if ($x['info_object_url_authenticate'] == 1) {
	$_SESSION['my_or_' . $wn] = $admin ? 7 : $ur;
	$_SESSION['my_oid_' . $wn] = $object_id;
}
// construct the link
$wn = preg_replace('/[^a-zA-Z0-9_-]/', '', $wn . $_SERVER['HTTP_HOST']); // IE complains if other chars are in the window name
if (stristr($wn, 'tunnelagenda')) {
	$wn.= "$object_id";
}
$target = trim($target);
if ($target == 'js_elastic' || $target == 'js_ICpop') {
	$link = "'$url','$wn','width=$ww, height=$wh'";
	$nlink = "'$wn',$url";
} elseif ($target == 'js_menubar') {
	$link = "'$url','$wn','menubar=1, width=$ww, height=$wh'";
} else $link = "'$url','$target',''";
switch ($object_type) {
	case 4: // URL
		echo '<script>popfull(' . $link . ');</script>';
		break;

	case 5: // application

	case 11: // database
		if ($_SESSION['tabBrowsing']) echo '<script>top.tabs.tabWin.add("' . $url . '","' . $wn . '","' . $name . '","' . $icon . '");</script>';
		elseif ($target == 'js_ICpop') echo '<script>popIC(' . $link . ');</script>';
		else echo '<script>popwindow(' . $link . ');</script>';
		break;

	case 12: // agenda
		$_SESSION['my_or_view_agenda.php'] = $rights; // provide rights to associated script
		echo '<script>popwindow("view_agenda.php?xfl=' . urlencode($link) . '","Agenda","height=600, width=800, menubar=1");</script>';
}
echo '<script>window.close();</script>';
?>
