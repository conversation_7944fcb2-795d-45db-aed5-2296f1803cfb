<?php
$script = basename($_SERVER['PHP_SELF']); // name of the script
session_start();
ob_start();
$rights = 3; //$_SESSION['my_or_'.$script];
$uid = $_SESSION['user_id'];
switch ($rights) {
case 0: // no rights at all
	echo '<h3>Access denied!</h3>';
	exit;
case 1: // path view rights
	echo '<h3>Access denied!</h3>';
	exit;
case 2: // read rights on object
	$opts['options'] = 'VF';
	break;

case 3: // write rights
	$opts['options'] = 'VFD';
	break;

case 4: // workflow edit rights
	$opts['options'] = 'VFD';
	break;

case 5: // edit rights
	$opts['options'] = 'VFD';
	break;

case 6: // owner rights
	$opts['options'] = 'VFD';
	break;

case 7: // administrator group
	$opts['options'] = 'VFD';
	break;

case 8: // root user
	$opts['options'] = 'VDF';
	break;
}
if (isset($_REQUEST['xfl'])) {
	$xfl = @$_REQUEST['xfl'];
}
if ((!isset($xfl)) || (@$_POST['navop'] == 'All')) {
	$xfl = - 1;
}
if ($xfl > 0) {
	$opts['cgi']['persist'] = array('xfl' => $xfl);
}
// Debug query
//$debug_query = true;
// Debug post variables
//$debug_posts = true;
// Debug get variables
//$debug_gets = true;
function xls_style()
{
	echo '
	<style>
		@page
			{mso-header-data:"&C&\0022Arial\,Bold\0022&18&A&RPrinted\: &D";
			mso-footer-data:"&L&\0022Arial\,Bold\0022DNW Confidential&RPage &P";
			margin:1.0in .75in 1.0in .75in;
			mso-header-margin:.5in;
			mso-footer-margin:.5in;
			mso-page-orientation:landscape;}
		.pme-header {border: #ffffff 1px solid; 
			height:20pt; 
			background: #D2D2D2;
			font-family: Verdana, Arial, Sans-Serif;
			font-size: 9pt;
			font-weight: bolder;
			color: #000000;
			text-decoration: none;
			margin: 0pt;
			vertical-align:middle;}
		.pme-cell-0 {border: #D2D2D2 .5pt solid;vertical-align:middle;}
		.pme-cell-1 {border: #D2D2D2 .5pt solid;background-color: #FFFFCC;vertical-align:middle;}
	</style>
	';
}
// MySQL host name, user name, password, database, and table
$opts['hn'] = 'localhost';
$opts['un'] = 'IC17';
$opts['pw'] = 'IC17dnw';
$opts['db'] = 'apps';
$opts['tb'] = 'cycletime';
$opts['myscript'] = $script; // required for authorization in InfoCenter
// current date
$today = date('Y-m-d');
// Name of field which contains the record userid
//$opts['useridfield'] = 'fk_uid';
// Name of field which is the unique key
$opts['key'] = 'id';
// Type of key field (int/real/string/date etc.)
$opts['key_type'] = 'int';
// Sorting field(s)
$opts['sort_field'] = array('-id');
// Number of records to display on the screen
// Value of -1 lists all records in a table
$opts['inc'] = 15;
$opts['language'] = $lang = $_SESSION['language'];
if (empty($lang)) {
	$lang = $_SERVER['HTTP_ACCEPT_LANGUAGE'];
	$lang = strtolower(substr($lang, 0, 2));
}
// image url
$opts['url'] = array('images' => 'imgIClist/');
$opts['excel_btn'] = 'Export to Excel';
if (!empty($_REQUEST['xls'])) $opts['excel'] = ' Logged cycle times';
$print_label = array('en' => 'Print', 'de' => 'Drucken', 'nl' => 'Afdrukken');
$back_label = array('en' => 'Close', 'de' => 'Schlie�en', 'nl' => 'Sluiten');
$report_label = array('en' => 'Report', 'de' => 'Report', 'nl' => 'Rapport');
$reset_label = array('en' => 'Reset', 'de' => 'Zur�cksetzen', 'nl' => 'Terugzetten');
$reset_btn = array('code' => '<input class="reset_btn" type="button" name="reset" value="' . $reset_label[$lang] . '" onClick="self.location.href=\'' . $_SERVER['PHP_SELF'] . '?pg=2\'">');
$print_btn = array('code' => '<input class="print_btn" type="button" name="print" value="' . $print_label[$lang] . '" onClick="window.print()">');
$report_btn = array('code' => '<input class="report_btn" type="button" name="reset" value="' . $report_label[$lang] . '" onClick="self.location.href=\'' . $_SERVER['PHP_SELF'] . '?rp=2&xfl=' . $xfl . '\'">');
$back_btn = array('code' => '<input class="back_btn" type="button" name="back" value="' . $back_label[$lang] . '" onClick="self.location.href=\'L.php\'">');
$n_row_btn = array('function' => 'nrow_btn');
$cr_lf = array('code' => '<br />');
$opts['buttons']['L']['up'] = array('<<', '<', 'goto_combo', '>', '>>', $print_btn, $report_btn, $reset_btn, $cr_lf, $n_row_btn);
$opts['buttons']['F']['up'] = $opts['buttons']['L']['up'];
$opts['buttons']['A']['up'] = array('');
$opts['buttons']['C']['up'] = array('');
$opts['buttons']['P']['up'] = array('');
$opts['buttons']['D']['up'] = array('save', 'cancel');
$opts['buttons']['V']['up'] = array('delete', 'cancel', $print_btn);
$opts['buttons']['L']['down'] = $opts['buttons']['L']['up'];
$opts['buttons']['V']['down'] = $opts['buttons']['V']['up'];
$opts['buttons']['F']['down'] = $opts['buttons']['F']['up'];
// buttons for view/change if not owner of the record
$opts['alt_buttons']['V']['up'] = $opts['alt_buttons']['V']['down'] = array('cancel', $print_btn);
$opts['alt_buttons']['C']['up'] = $opts['alt_buttons']['C']['down'] = array('cancel');
$opts['filter_btn'] = 'small'; // show small filter buttons
// Number of lines to display on multiple selection filters
$opts['multiple'] = '4';
// Navigation style: B - buttons (default), T - text links, G - graphic links
// Buttons position: U - up, D - down (default)
$opts['navigation'] = 'UG';
// Display special page elements
$opts['display'] = array('form' => true, 'query' => false, 'sort' => true, 'time' => false, 'tabs' => true);
// Set default prefixes for variables
$opts['js']['prefix'] = '';
$opts['dhtml']['prefix'] = '';
$opts['cgi']['prefix']['operation'] = 'PME::op::';
$opts['cgi']['prefix']['sys'] = '';
$opts['cgi']['prefix']['data'] = '';
$opts['fdd']['id'] = array('name' => 'Rec-ID', 'select' => 'T', 'options' => 'V', // auto increment
'maxlen' => 10, 'colattrs|L' => 'align="center"', 'xlsattrs' => 'align="center"', 'sort' => true, 'report' => true);
$opts['fdd']['date'] = array('name' => 'Date', 'select' => 'T', 'options' => 'LAVCPDR', 'maxlen' => 14, 'datemask|VDC' => 'D, d-m-Y H:i:s', 'datemask|LF' => 'd.m.y H:i', 'colattrs|L' => 'align="center"', 'xlsattrs' => 'align="center" width="100pt" style=\'mso-number-format:"ddd\\\,\\ dd\\\.\ mmm\\\.\ yyyy"\'', 'sort' => true, 'report' => true);
$opts['fdd']['uid'] = array('name' => 'User', 'select' => 'T', 'maxlen' => 20, 'colattrs|L' => 'align="center"', 'xlsattrs' => 'align="center"', 'sort' => true, 'report' => true, 'values' => array('db' => 'infocenter', 'table' => 'info_user', 'column' => 'user_id', 'filters' => "user_id=$uid", 'description' => array('columns' => array('0' => 'user_fname', '1' => 'user_mname', '2' => 'user_lname'), 'divs' => array('0' => ' ', '1' => ' ')),));
$opts['fdd']['ip'] = array('name' => 'Ip', 'select' => 'T', 'maxlen' => 255, 'sort' => true, 'report' => true);
$opts['fdd']['bytes'] = array('name' => 'bytes', 'select' => 'N', 'maxlen' => 255, 'colattrs|L' => 'align="center"', 'xlsattrs' => 'align="center"', 'sort' => true, 'report' => true);
$opts['fdd']['ms'] = array('name' => 'Cycle time [mS]', 'select' => 'N', 'maxlen' => 255, 'colattrs|L' => 'align="center"', 'xlsattrs' => 'align="center"', 'sort' => true, 'report' => true);
$opts['fdd']['file'] = array('name' => 'file', 'select' => 'T', 'maxlen' => 255, 'colattrs|L' => 'align="center"', 'xlsattrs' => 'align="center"', 'sort' => true, 'report' => true);
?>
<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN">
<HTML>
<HEAD>
<title>DNW Database Editor:&nbsp;Cycle Time Log</title>
<script type="text/javascript" src="js/utils.js"></script>
<link rel="stylesheet" type="text/css" href="pme.css">
</HEAD>
<BODY bgcolor="E4E4CB">
<div align="center">
<table class="body"><tr><td class="bodycell">


<h3>Cycle Time Log</h3>

<?php
if (@$_REQUEST['rp']) {
	$opts['cgi']['persist'] = array('rp' => true);
	require_once 'extensions/phpMyEdit_report.class.php';
	new phpMyEdit_report($opts);
} else {
	require_once 'extensions/phpMyEdit-htmlcal.class164js.php';
	$myedit = new phpMyEdit_htmlcal($opts);
}
ob_flush();
?>
</td></tr>
</table></div>
</BODY>
</HTML>


