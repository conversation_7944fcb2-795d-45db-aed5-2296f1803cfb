<?php
require 'IC2auth.inc.php'; // sets user id ($uid) and permission (&rights)
require_once ('IC2_obj_util.php'); // object utilities
if (isset($_POST['m'])) $mode = trim($_POST['m']);
else exit('no params'); // ur or gr
if (!in_array($mode, array('ur', 'gr'))) exit('invalid params');
if (isset($_POST['sq'])) {
	require_once 'qc.php';
	$oid = query_sdecode($_POST['sq']);
	$_POST['L'] = $oid['xflv'];
}
(isset($_POST['L']) && ($oid = (int)$_POST['L']) > 0) || $oid = 0; // valid oid provided
(isset($_POST['T']) && ($tid = (int)$_POST['T']) > 0) || $tid = 0; // valid tid provided
if (!$oid || !$tid) exit('invalid object');
// check for admin and owner
if ($uid != 1 && !has_edit_right($oid)) {
	error_log('No permission in set_rights');
	exit();
}
$oid = (array)$oid;
// look for "apply to child objects of next level"			todo: check right to change rights of child object
if (!empty($_POST['x']) && ($_POST['x'] == 'true')) {
	// if object is file we have to gather all versions.
	if ($tid > 12) { // scan info_object_file for versions
		$sql = "SELECT info_object_file_origin FROM {$_SESSION['DB_IC']}.info_object_file 
				WHERE fk_object_id = '$object_id'";
		$res = mysql_query($sql);
		while ($row = @mysql_fetch_row($res)) {
			list($origin) = $row;
		}
		// get child objects
		$sql = "SELECT fk_object_id FROM {$_SESSION['DB_IC']}.info_object_file 
				WHERE info_object_file_origin = '$origin'";
		$res = mysql_query($sql);
		while ($row = mysql_fetch_row($res)) $oid[] = (int)$row[0];
	} elseif ($tid == 1) { // we have a folder where all childs have the same parent object
		$sql = "SELECT object_id FROM {$_SESSION['DB_IC']}.info_object WHERE object_parent_id = '$object_id'";
		$res = mysql_query($sql);
		while ($row = mysql_fetch_row($res)) {
			$oid[] = (int)$row[0];
		}
	}
}
switch ($mode) {
case 'ur':
	$user_id = dec_idperm(true);
	if ($user_id === false) exit('no valid params:');
	$e = $w = $r = $owner = false;
	switch ($perm) {
	case 0:
	case 1:
		break;

	case 2:
		$r = true;
		break;

	case 3:
		$r = true;
		$w = true;
		break;

	case 4:
	case 5:
		$r = true;
		$w = true;
		$e = true;
		break;

	case 6:
		$r = true;
		$w = true;
		$e = true;
		break;

	case 7:
		$r = true;
		$w = true;
		$e = true;
		break;

	case 8:
		$r = true;
		$w = true;
		$e = true;
		break;

	default:
		break;
	}
	foreach ($oid as $object_id) {
		set_user_rights($object_id, $user_id, $r, $w, $e, $owner);
	}
	exit($t_id);
	break;

case 'gr':
	$group_id = dec_idperm(false);
	if ($group_id === false) exit('no valid params:');
	$w = $r = false;
	switch ($perm) {
	case 0:
	case 1:
		break;

	case 2:
		$r = true;
		break;

	case 3:
		$r = true;
		$w = true;
	default:
		break;
	}
	foreach ($oid as $object_id) {
		set_group_rights($object_id, $group_id, $w, $r);
	}
	exit($t_id);
}
error_log('No valid params in set_rights');
exit();
function dec_idperm($m)
{
	if ($m) $perm_a = array(0, 2, 3, 4, 5, 6, 7, 8);
	else $perm_a = array(0, 2, 3);
	global $perm, $t_id;
	if (isset($_POST['t'])) $t_id = $_POST['t'];
	else return false;
	$len = strlen($t_id) - 1;
	//	$perm = (int)$t_id[$len];
	if (!in_array($perm = (int)$t_id[$len], $perm_a)) return false;
	$id = (int)substr($t_id, 0, $len);
	if ($id > 0) return $id;
	else return false;
}
function has_edit_right($object_id)
{
	$owner = 0;
	$ur = 0;
	$type = 0;
	$uid = $_SESSION['user_id'];
	$sql = "SELECT CAST(object_right AS unsigned) as ur,info_object.fk_user_id,fk_object_type_id,xlink FROM {$_SESSION['DB_IC']}.user_rights LEFT OUTER JOIN {$_SESSION['DB_IC']}.info_object ON info_object.object_id=user_rights.fk_object_id WHERE user_rights.fk_object_id='$object_id' AND user_rights.fk_user_id='$uid'";
	list($ur, $owner, $type, $xlink) = mysql_fetch_row(mysql_query($sql));
	($type > 12) && $type = 13;
	($xlink) && $type = 6;
	// determine the change permission for different object types
	// special settings for admin
	if ($uid == 1) {
		$ur = 7;
		$owner = 1;
	}
	$change = false;
	switch ($type) {
	case 1: //($ur >= 5 && $owner==$uid) && $change=true; break;	// folder
		
	case 4:
		($ur >= 5) && $change = true;
		break; // url
		
	case 11: // database
		
	case 5:
		($ur >= 7 && $owner == $uid) && $change = true;
		break; // application
		
	case 6:
		($ur >= 5) && $change = true;
		break; // shortcut
		
	case 13:
		($ur >= 5) && $change = true;
		break; // file
		
	default:
		($ur > 5 && $owner == $uid) && $change = true; // all other objects
		
	}
	if ($change) return true;
	else return false;
}
?>