<?php
if (!require './IC2auth.inc.php') exit; // sets user id ($uid) and permission (rights), defines $script
switch ($rights) {
case 0: // no rights at all
	echo '<h3>Access denied!</h3>';
	exit;
case 1: // path view rights
	echo '<h3>Access denied!</h3>';
	exit;
case 2: // read rights on object
	
case 3: // write rights
	
case 4: // workflow edit rights
	
case 5: // edit rights
	
case 6: // owner rights
	
case 7: // administrator group
	
case 8: // root user
	$opts['options'] = 'ACD'; // enables change, delete
	break;

default: // no rights at all
	echo '<h3>Access denied!</h3>';
	exit;
}
if (1 && $uid == 47) {
	// Debug query
	// $debug_query = true;
	// Debug post variables
	// $debug_posts = true;
	// Debug get variables
	//$debug_gets = true;
	$dev = true;
}
if ($_REQUEST['mode'] == 'add') {
	$add_quicklink = true;
	$opts['cgi']['persist']['mode'] = 'add';
} else $add_quicklink = false;
// language
$opts['language'] = $lang = $_SESSION['language'];
// MySQL host name, user name, password, database, and table
$opts['db'] = 'infocenter';
$opts['tb'] = 'info_user_links';
// current date
$today = date('Y-m-d');
// Name of field which contains the record userid
//$opts['useridfield'] = 'fk_uid';
// Name of field which is the unique key
$opts['key'] = 'link_id';
// Type of key field (int/real/string/date etc.)
$opts['key_type'] = 'int';
// Sorting field(s)
$opts['sort_field'] = array('user_links_hrn');
// Number of records to display on the screen
// Value of -1 lists all records in a table
$opts['inc'] = - 1;
//$opts['inc_array'] = array(5, 10, 15); // selection of number of records displayed on list page
// Report (button) enabled
//$opts['report'] = true;
// Number of lines to display on multiple selection filters
$opts['multiple'] = '4';
// Navigation style: B - buttons (default), T - text links, G - graphic links
// Buttons position: U - up, D - down (default)
$opts['navigation'] = 'DG';
// Display special page elements
$opts['display'] = array('form' => true, 'query' => false, 'sort' => false, 'time' => false, 'tabs' => true);
$n_row_btn = array('function' => 'nrow_btn');
$CrLf = array('code' => '<br />');
$done = array('code' => '<button type=submit name=savechange value=savechange class=doneBtn>Done</button>');
$opts['buttons']['L']['up'] = array('<<', '<', '>', '>>', $CrLf, $n_row_btn);
if ($dev) $opts['buttons']['L']['up'] = array('add', '<<', '<', '>', '>>', $CrLf, $n_row_btn);
$opts['buttons']['F']['up'] = $opts['buttons']['L']['up'];
$opts['buttons']['A']['up'] = array('save', 'cancel');
if ($add_quicklink) {
	$opts['buttons']['C']['up'] = array($done);
} else $opts['buttons']['C']['up'] = array('save', 'cancel');
$opts['buttons']['P']['up'] = array('');
$opts['buttons']['D']['up'] = array('');
$opts['buttons']['V']['up'] = array('');
$opts['buttons']['L']['down'] = $opts['buttons']['L']['up'];
$opts['buttons']['C']['down'] = $opts['buttons']['C']['up'];
$opts['buttons']['F']['down'] = $opts['buttons']['F']['up'];
$opts['buttons']['A']['down'] = $opts['buttons']['A']['up'];
//$opts['filter_btn']='small'; // show small filter buttons
//$opts['tbl_controls']='tpls_db_browse.php';  // small A-Z filter tabs
$opts['display']['xnav'] = array('V' => false, 'C' => true, 'P' => false, 'D' => true); // graphic nav links in list page
// Set default prefixes for variables
$opts['js']['prefix'] = '';
$opts['dhtml']['prefix'] = '';
$opts['cgi']['prefix']['operation'] = 'PME::op::';
$opts['cgi']['prefix']['sys'] = '';
$opts['cgi']['prefix']['data'] = '';
$opts['skip-delete-confirm'] = false;
// image url
$opts['url'] = array('images' => 'imgIClink/');
$opts['filters'] = 'PMEtable0.fk_user_id=' . $uid;
$opts['fdd']['link_id'] = array('options' => '',
//  'colattrs|L'=> 'width="150pt"',
);
$opts['fdd']['fk_object_id'] = array('options' => '', 'values' => array('db' => 'infocenter', 'table' => 'info_object', 'column' => 'object_id', 'description' => 'fk_object_type_id'));
$confDelete = array('en' => 'Delete selected Quick-Link?', 'de' => 'Den gew�hlten Quick-Link l�schen?', 'nl' => 'De geselecteerde Quick-Link wissen?');
$opts['fdd']['icon'] = array('name|D' => 'Object type', 'options' => 'LD', 'input' => 'V', 'escape' => false, 'colattrs|L' => 'align="right" width="25px"', 'values' => array('db' => 'infocenter', 'table' => 'info_object_type', 'column' => 'object_type_icon', 'description' => 'object_type_icon', 'join' => 'PMEjoin1.fk_object_type_id=PMEjoin2.object_type_id'), 'sql|L' => 'CONCAT("<img src=\"img/",IF(LENGTH(object_type_icon),object_type_icon,\'tool_folder.gif\'),"\"/>")', 'sql|D' => 'CONCAT("<img src=\"img/",IF(LENGTH(object_type_icon),object_type_icon,\'tool_folder.gif\'),"\" align=\"center\"/>&nbsp;",object_type_hrn)', 'header|D' => '<h3><font color="darkorange">' . $confDelete[$lang] . '</h3>',);
$opts['fdd']['user_links_hrn'] = array('name' => 'Name', 'name|A' => 'Name of Quick-Link folder', 'options' => 'LVCAD', 'maxlen' => '40', 'colattrs' => 'align=left', 'required' => true
//  'colattrs'=> 'width="150px"',
);
if ($dev) {
	$opts['sort_field'] = array('type_id');
	$opts['fdd']['fk_user_id'] = array('options' => 'A', 'default' => $uid, 'rowattrs' => 'style="display:none"',);
	$opts['fdd']['type_id'] = array('options' => '', 'input' => 'VH', 'sql' => 'PMEjoin2.object_type_id',);
	if ($_REQUEST['sq']) {
		require_once ('./qc.php');
		$qc = query_sdecode($_REQUEST['sq']);
		$qc = $qc['rec'];
	}
	$opts['fdd']['user_link_parent'] = array('name' => 'attach to Quick-Link Folder', 'options' => $qc ? 'CD' : '', 'values2' => array('0' => '--'), 'values|C' => array('table' => 'info_user_links', 'column' => 'link_id', 'description' => 'user_links_hrn', 'filters' => "fk_object_id=0 AND link_id<>'$qc' AND fk_user_id='$uid'"), 'values|D' => array('table' => 'info_user_links', 'column' => 'link_id', 'description' => 'user_links_hrn', 'filters' => "fk_user_id='$uid'")
	//  'colattrs'=> 'width="150px"',
	);
	$opts['fdd']['user_links_status_code'] = array('name' => 'Is quick-link', 'options' => 'VCD', 'checkbox' => true, 'values2' => array('1' => 'always', '0' => 'no')
	//  'colattrs'=> 'width="150px"',
	);
	$opts['fdd']['user_link_watch'] = array('name' => 'Notify if changed', 'options' => 'VCD', 'select' => 'M', 'checkbox' => true, 'values2' => array('1' => 'check early', '2' => 'check late')
	//  'colattrs'=> 'width="150px"',
	);
	$opts['fdd']['user_link_remind'] = array('name' => 'Remind on', 'options' => 'VCD', 'datemask' => 'D, d. M Y', 'datetime' => true, 'calendar' => array('ifFormat' => '%d-%m-%Y', // defaults to the ['strftimemask']
	'singleClick' => true, // single or double click to close
	'showsTime' => false, // Show time as well as date
	), 'func' => 'swapDate', 'JS|C' => ' onfocus="this.blur();document.getElementById(\'_calbutton_user_links_remind\').click()"',);
}
if ($add_quicklink) {
	$opts['triggers']['update']['after'] = 'done';
	function done()
	{
		exit('<script>addFavFinished()</script>');
	}
}
if ($_POST['user_link_remind']) $_POST['user_link_remind'] = swapDate($_POST['user_link_remind']);
function swapDate($date)
{
	if (!$date || $date == '0000-00-00') return '';
	list($d, $m, $y) = preg_split('#[/\.-]#', $date);
	return "$y-$m-$d";
}
ob_start();
?>
<!DOCTYPE HTML>
<HTML>
<HEAD>
<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
<meta http-equiv="X-UA-Compatible" content="IE=8">
<meta name="author" content="Gert Lehmann">
<meta http-equiv="expires" content="0"> 
<title>Quick-Links </title>
<link rel="stylesheet" type="text/css" href="pme.css">
<style type="text/css">
.pme-more, .pme-save, .pme-cancel, .pme-change, .pme-delete, .print_btn, .back_btn, .pme-goto, .pme-first, .pme-prev, .pme-next, .pme-last {
	font-size: 8pt;
}
.pme-first, .pme-prev, .pme-next, .pme-last {
	font-weight: bolder;
}
.doneBtn{font-size:8pt}
</style>
<link rel="stylesheet" type="text/css" href="js/loadFile.php?calendar-win2k-1.css">
<script type="text/javascript" src="js/loadFile.php?calendargz.js,calendar-en.js,calendar-setup.js"></script>
<script type="text/javascript" src="js/utils.js"></script>
<script>var addFavFinished= top.addFavFinished</script>
</HEAD>
<BODY style="background:<?php
echo $_SESSION['BGROUND'] ?>;">
<table class="body" width="100%"><tr><td class="bodycell">
<?php
require_once 'extensions/phpMyEdit-htmlcal.class164js.php';
$myedit = new phpMyEdit_htmlcal($opts);
echo '</td></tr>
</table>';
if ($dev) {
	echo <<<PME
  <script>function PMEhref(loc){if(loc.indexOf('PME::op::Change')>=0){
    var sq = loc.split('sq=');
    top.fav_edit(sq[1],'Edit');
  }
  else
    location.replace(String(loc))};
  </script>
PME;
	
}
echo '</BODY>
</HTML>';
?>


